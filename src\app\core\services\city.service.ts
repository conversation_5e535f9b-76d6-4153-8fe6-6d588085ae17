import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ErrorHandler } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { City } from '../models/city';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CityService {
  private url = `${environment.apiBaseUrl}cities/`

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.country_id) params = params.set('country_id', options.country_id);
    if (options.state_id) params = params.set('state_id', options.state_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  history(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.country_id) params = params.set('country_id', options.country_id);
    if (options.state_id) params = params.set('state_id', options.state_id);
    params = params.set('status', 1);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url + 'history'}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<City> {
    return this.http.get<City>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(city: City): Observable<City> {
    return this.http.post<City>(this.url, City.toFormData(city))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(city: City): Observable<City> {
    return this.http.post<City>(this.url + city.id, City.toFormData(city))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<City> {
    return this.http.delete<City>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  enable(id: string): Observable<City> {
    return this.http.post<City>(this.url + id + '/enable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  disable(id: string): Observable<City> {
    return this.http.post<City>(this.url + id + '/disable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
