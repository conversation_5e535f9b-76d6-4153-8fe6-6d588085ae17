import { <PERSON><PERSON><PERSON>cy<PERSON>ipe, formatDate, <PERSON>portScroller, LocationStrategy, DOCUMENT } from '@angular/common';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { environment } from '../../../../environments/environment';
import { User } from '../../../core/models/user';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { Restaurant } from '../../../core/models/restaurant';
import { NgForm } from '@angular/forms';
import { ReferralService } from '../../../core/services/referral.service';
import { Referral } from '../../../core/models/referral';
import { ReferralPending } from '../../../core/models/referral-pending';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-referral',
  host: { ngSkipHydration: 'true' },
  templateUrl: './referral.component.html',
  styleUrls: ['./referral.component.scss'],
})
export class ReferralComponent implements OnInit, OnDestroy {
  subs = new Subscription();

  user: User;
  referral: Referral = new Referral();
  referralPendings: ReferralPending[] = [];
  refferUrl: string;
  modalOptions: NgbModalOptions;
  previousPage: any;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isEmailModelLoading = false; ModelEmailerror = null;
  errorMessage = null;
  errorChangePassword = null;
  copyTextChange = false;
  tableExpand = false;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  constructor(
    public userService: UserService,
    private referralService: ReferralService,
    private restaurantService: RestaurantService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private locationStrategy: LocationStrategy,
    private notificationService: NotificationService,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    this.refferUrl = location.origin + this.locationStrategy.getBaseHref() + "home?referral=" + this.user.referral_code;
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    this.fetchReferral();
    this.fetchReferralPending();
  }


  fetchReferral() {
    this.isLoading = true;

    this.subs.add(this.referralService.show('1')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.referral = res;
      }, err => this.error = err)
    );
  }

  fetchReferralPending() {
    this.isLoading = true;

    this.subs.add(this.referralService.getReferralPending({ nopaginate: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.referralPendings = res;
        }, (err) => {
          this.referralPendings = [];
        }
      )
    )
  }

  copyMessage(val: string) {
    const selBox = this.document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.refferUrl;
    this.document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    this.document.execCommand('copy');
    this.document.body.removeChild(selBox);
    this.copyTextChange = true;
    this.notificationService.showInfo("referral link copied !!", "Gogrubz")
  }

  smsReferral(model) {
    this.openModal(model);
  }

  emailReferral(model) {
    this.openModal(model);
  }

  keyPress(event: any) {
    const pattern = /[0-9\+\-\ ]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  validatePhone(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isModelLoading = true; this.Modelerror = false;

    this.subs.add(
      this.userService.sendLink({ phone_number: this.user.phone_number, message: this.refferUrl }).
        pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("Referral link send successfully !!", "Gogrubz")
            this.modalService.dismissAll();
          },
          (err) => {
            this.Modelerror = err;
          }
        )
    )
  }

  validateEmail(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isEmailModelLoading = true; this.ModelEmailerror = false;

    this.subs.add(
      this.userService.sendLinkEmail({ email: this.user.username, message: this.refferUrl }).
        pipe(finalize(() => this.isEmailModelLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("Referral link send successfully !!", "Gogrubz")
            this.modalService.dismissAll();
          },
          (err) => {
            this.ModelEmailerror = err;
          }
        )
    )
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
