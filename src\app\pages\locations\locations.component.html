<section class="restaurant-location-section">
    <div class="restaurant-search-banner">
        <img class="restaurant-banner-image" src="assets/images/restaurant-bg.png" alt="Go-Grubz-Banner-image"
            loading="lazy">
        <h2 class="search-restaurant-title">Search for restaurant in your area</h2>
        <div class="enter-postcode-address">
            <form nz-form #fetchForm="ngForm" (ngSubmit)="restaurantFetch(fetchForm)">
                <nz-form-item>
                    <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode or delivery address!">
                        <nz-input-group>
                            <img class="send-icon" src="assets/images/send-icon.svg" alt="GoGrubz-Send-Icon"
                                loading="lazy">
                            <input class="form-control" type="text" nz-input name="zipcode" id="zipcode"
                                [(ngModel)]="restaurant.zipcode" required (keydown.space)="onSpaceKeyDown($event)"
                                placeholder="Enter postcode or delivery address" type="text">
                            <button class="btn" nz-button>
                                Search
                            </button>
                        </nz-input-group>
                    </nz-form-control>
                </nz-form-item>

                <nz-form-item *ngIf="error">
                    <span class="text-danger">{{ error }}</span>
                </nz-form-item>
            </form>
        </div>
    </div>
</section>

<section class="restaurants-bg" *ngIf="!isMapLoading">
    <div class="restaurants-near-me">
        <h3>
            Restaurants<br> near me
        </h3>
    </div>
    <google-map [center]="center" [zoom]="zoom" [options]="{ streetViewControl: false, fullscreenControl: true }">
        <map-marker *ngFor="let marker of markers" [position]="marker.position" [label]="marker.label"
            [title]="marker.title">
        </map-marker>
    </google-map>
    <!--<img src="assets/images/map-bg.png" alt="map-bg">-->
    <!-- <iframe class="border-0"
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2425.0280255222856!2d-1.9842219729866506!3d52.569104432592454!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4870a277546101e9%3A0x8a375d5902f9fe3c!2sTame%20Cl%2C%20Walsall%20WS1%204BA%2C%20UK!5e0!3m2!1sen!2sin!4v1706016793944!5m2!1sen!2sin"
        allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> -->
</section>

<section class="view-all-cities-bg" *ngIf="!isLoading">
    <div class="main-heading text-center">
        <h4>All cities</h4>
    </div>

    <div class="container" *ngIf="isLoading || isMapLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="row" *ngIf="cities?.length <= 0 && !isLoading">
        <div class="dont-have-order text-center">
            <p>Don't have any cities yet.</p>
        </div>
    </div>

    <div class="row" *ngIf="!isLoading">
        <div class="col-lg-12">
            <div class="all-cities-list">
                <ul>
                    <li *ngFor="let alpha of alphaStatus">
                        <button class="cursor" [ngClass]="{'active' : scrollingAlpha == alpha.id}"
                            (click)="clickAlpha(alpha.id)">{{ alpha.name }}
                        </button>
                    </li>
                </ul>
            </div>
        </div>
        <div *ngFor="let item of cities | keyvalue">
            <div class="col-lg-12">
                <div class="city-character-count">
                    <span>{{ item.key }}</span>
                </div>
            </div>
            <div class="row" [id]="'alpha_'+item.key">
                <div class="col-lg-3 col-sm-6" *ngFor="let city of item.value;">
                    <div class="cities-box">
                        <ul>
                            <li (click)="onCity(city.city_name)">
                                <span class="cursor">{{ city.city_name | titlecase }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

</section>

<div class="clearfix"></div>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>