.your-favourite-restaurants{
    margin-bottom: 90px;
}
.your-favourite-restaurants p{
    font-size: 40px;
    font-weight: 700;
    font-family: 'Visby CF';
    line-height: 29px;
    text-align: center;
}
.order-history-box {
    display: flex;
    margin-bottom: 70px;
}
.order-image {
  max-width: 280px;
  width: 100%;
  min-height: 170px;
  max-height: 170px;
  position: relative;
}

.order-image img.order-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0px 0px 25px 5px #00000033;
}

.order-image .order-logo {
  width: 74px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: absolute;
  top: 16px;
  left: 16px;
  border-radius: 50%;
  box-shadow: 0px 4px 15px 0px #00000080;
  background-color: #FFFFFF;
}

.order-image .order-logo img {
  width: 100%;
  box-shadow: none;
}

.order-details {
  width: 100%;
  padding-left: 45px;
}

.order-title {
  display: flex;
  margin-bottom: 12px;
}

.order-title h6 {
  font-family: 'Fredoka';
  color: #000000;
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 0;
}
.address-details p{
    color:#8F8F8A;
    font-family: 'Visby CF';
    font-size: 25px;
    font-weight: 700;
    line-height: 29px;    
    margin-bottom: 10px;
}
.address-details ul{
    margin-bottom:0;
}
.address-details ul li {
    color: #8F8F8A;
    font-family: 'Visby CF';
    font-size: 20px;
    font-weight: 700;
    display: block;
    position: relative;    
}

.view-store ul {
    display: flex;
    margin-top: 40px;
    margin-bottom: 0;
  }
  
  .view-store ul li {
    display: inline-block;
    margin-left: 40px;
  }
  
  .view-store ul li:first-child {
    margin-left: 0;
  }
  
  .view-store button.btn {
    font-size: 18px;
    font-weight: 700;
    padding: 7px 15px;
    width: 150px;
    height: 40px;
    white-space: nowrap;
  }
  .modal-header h5.login-title{
    padding-top:5px;
  }
  .modal-body.product-body p{
    font-size: 18px;
  }
  .modal-body.product-body button.btn{
    padding: 10px 40px;
    font-size: 18px;
    margin: 0 15px;
  }

  @media screen and (max-width:1800px) {
    .order-title {
      margin-bottom: 10px;
    }
  
    .order-title h6 {
      min-width: 220px;
    }
  
    .order-details {
      padding-left: 40px;
    }
  
    .address-details ul {
      margin-bottom: 10px;
    }
  
    .address-details ul li {
      font-size: 18px;
    }
  
    .view-store ul li {
      margin-left: 25px;
    }
  
    .view-store ul li:first-child {
      margin-left: 0;
    }
  
    .view-store button.btn {
      font-size: 16px;
      width: 140px;
    }
  }

  @media screen and (max-width:1500px) {  
    .your-favourite-restaurants{
      margin-bottom: 70px;
    }
    .your-favourite-restaurants p{
      font-size: 30px;
    }
    .dont-have-order{
      margin-bottom:40px;
    }
    .dont-have-order p{
      font-family: 'Visby CF';
      font-size: 15px;
      font-weight: 700;
      line-height: 22px;
      text-align: center;
    }
    .order-history-box{
      margin-bottom: 60px;
    }
    .order-image {
      max-width: 210px;
      min-height: 127px;
      max-height: 127px;
    }
    .order-image .order-logo {
      width: 55px;
      height: 55px;
      padding: 6px;
      top: 15px;
      left: 15px;
    }
    .order-title{
      margin-bottom: 3px;
    }
    .order-title h6{
      font-size: 22px;
    }      
    .order-details{
      padding-left: 35px;
    }
    .address-details p {
        font-size: 18px;
        margin-bottom: 5px;
    }
    .address-details ul li {
      font-size: 15px;
    }
    .view-store ul li {
      margin-left: 30px;
    }
    .view-store ul li:first-child {
      margin-left: 0;
    }
    .view-store button.btn {
      font-size: 14px;
      padding: 1px 10px;
      width: 112px;
      height: 30px;
      line-height: 24px;
    }
    .modal-header h5.login-title{
      padding-top:0px;
    }
    .modal-body.product-body{
      padding: 20px;
    }
    .modal-body.product-body p{
      font-size: 16px;
      margin-bottom:15px;
    }
    .modal-body.product-body button.btn{
      padding: 5px 20px;
      font-size: 14px;
      margin: 0 5px;
    }
  
  }

@media screen and (max-width:1199px) {
    .order-history-box{
      margin-bottom: 55px;
    }
  
  }
  
  @media screen and (max-width:991px) {    
    .your-favourite-restaurants{
      margin-bottom: 50px;
    }    
    .order-history-box {
      flex-wrap: wrap;
    }
    .order-image {
      margin-bottom: 20px;
    }
    .order-details {
      padding-left: 0px;
    }
    .view-store ul {
      margin-top: 25px;
    }
    
  }

  @media screen and (max-width:767px) {        
    .your-favourite-restaurants{
        margin-bottom: 40px;
    }
    .your-favourite-restaurants p{
        font-size: 24px;
        line-height: 30px;
    }
    .order-history-box{
      justify-content: center;
    }
      
  }

  @media screen and (max-width:480px) {    
  .view-store ul {
    flex-wrap: wrap;
  }
  .view-store ul li {
    width: 100%;
    text-align: center;
    margin: 0 0 8px;
  }
}