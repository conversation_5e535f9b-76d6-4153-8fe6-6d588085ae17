import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Cart } from '../models/cart';
import * as CryptoJS from 'crypto-js';
import { Order } from '../models/order';
import { environment } from '../../../environments/environment';
import { ErrorHandler } from '../../shared/error-handler';

@Injectable({
  providedIn: 'root',
})
export class CartService {
  private url = environment.apiBaseUrl + 'cart/';
  public carts: Cart[] = []
  public qty: number = 0;
  public grandTotal: number = 0;
  public order: Order = new Order();
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Cart> {
    return this.http.get<Cart>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(cart: Cart): Observable<any> {
    return this.http.post<Cart>(this.url, Cart.toFormData(cart))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(cart: Cart): Observable<any> {
    return this.http.post<Cart>(this.url + cart.id, Cart.toFormData(cart))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Cart>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  cartBulk(carts: any): Observable<any> {
    return this.http.post<any>(this.url + "cart-bulk", Cart.bulkUplaodAsString(carts))
      .pipe(catchError(ErrorHandler.handleError));
  }

  saveOrderType(order_type: string) {
    var key = CryptoJS.enc.Utf8.parse(environment.garble);
    var iv = CryptoJS.enc.Utf8.parse(environment.jumble);
    var encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(order_type), key,
      {
        keySize: 128 / 8,
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.order_type, encrypted.toString());
    }
  }

  getOrderType() {
    if (typeof localStorage !== 'undefined') {
      var key = CryptoJS.enc.Utf8.parse(environment.garble);
      var iv = CryptoJS.enc.Utf8.parse(environment.jumble);
      var encrypted = localStorage.getItem(environment.order_type);
      if (!encrypted) {
        this.saveOrderType('delivery');
        var encrypted = localStorage.getItem(environment.order_type);
      }
      var decrypted = CryptoJS.AES.decrypt(encrypted, key, {
        keySize: 128 / 8,
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
      return decrypted.toString(CryptoJS.enc.Utf8);
    } else {
      return null;
    }
  }

  saveOrder(orders: Order) {
    if (typeof localStorage !== 'undefined') {
      const key = CryptoJS.enc.Utf8.parse(environment.garble);
      const iv = CryptoJS.enc.Utf8.parse(environment.jumble);
      const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(JSON.stringify(orders)), key,
        {
          keySize: 128 / 8,
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        });
      localStorage.setItem(environment.order, encrypted.toString());
    } else {
      return null;
    }
    // localStorage.setItem(environment.order, JSON.stringify(orders));
  }

  getOrder() {
    if (typeof localStorage !== 'undefined') {
      const key = CryptoJS.enc.Utf8.parse(environment.garble);
      const iv = CryptoJS.enc.Utf8.parse(environment.jumble);
      const encrypted = localStorage.getItem(environment.order);
      if (encrypted) {
        const decrypted = CryptoJS.AES.decrypt(encrypted, key, {
          keySize: 128 / 8,
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
      } else {
        return encrypted;
      }
    } else {
      return null;
    }
    // return localStorage.getItem(environment.order);
  }

  saveCart(carts: Cart[]) {
    this.carts = carts;
    const items = JSON.stringify(carts);
    this.qty = 0;
    this.grandTotal = 0;
    if (this.carts.length > 0) {
      this.carts.forEach(item => {
        this.qty = this.qty + item.quantity;
        this.grandTotal = this.grandTotal + item.total_price;
      });
    }
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.cart, items.toString());
    }
  }

  getCart() {
    if (typeof localStorage !== 'undefined') {
      const items = localStorage.getItem(environment.cart);
      const cart = JSON.parse(items || '[]');
      this.carts = cart
      this.qty = 0;
      this.grandTotal = 0;
      if (this.carts.length > 0) {
        this.carts.forEach(item => {
          this.qty = this.qty + item.quantity;
          this.grandTotal = this.grandTotal + item.total_price;
        });
      }
      return cart;
    } else {
      return [];
    }
  }

  removeCart() {
    this.carts = [];
    this.qty = 0;
    this.grandTotal = 0;
    if (typeof localStorage !== 'undefined') {
      return localStorage.removeItem(environment.cart);
    }
  }

}
