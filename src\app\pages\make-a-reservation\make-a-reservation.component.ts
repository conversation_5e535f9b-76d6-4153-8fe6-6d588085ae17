import { <PERSON>mponent, OnInit, ElementRef, ViewChild, Inject, PLATFORM_ID } from '@angular/core';
import { formatDate, isPlatformBrowser, Location } from '@angular/common';
import { finalize } from 'rxjs/operators';
import { Subscription, interval } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { User } from '../../core/models/user';
import { Restaurant } from '../../core/models/restaurant';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { DomSanitizer, Meta, SafeResourceUrl, Title } from '@angular/platform-browser';
import { NotificationService } from '../../core/services/notification.service';
import { Promotions } from '../../core/models/promotions';
import { ModalDismissReasons, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Booking } from '../../core/models/booking';
import { BookingService } from '../../core/services/booking.service';
import { NgForm } from '@angular/forms';
// import { FacebookLoginProvider, GoogleLoginProvider, SocialAuthService } from 'angularx-social-login';
import { SiteSettingService } from '../../core/services/site-setting.service';
import { SiteSetting } from '../../core/models/site-setting';
import { environment } from '../../../environments/environment';
import { CanonicalService } from '../../shared/canonical.service';
import { StripeCustomerService } from '../../core/services/stripe-customer.service';
import { StripeCustomer } from '../../core/models/stripe-customer';
declare var Stripe;
@Component({
  selector: 'app-make-a-reservation',
  host: { ngSkipHydration: 'true' },
  templateUrl: './make-a-reservation.component.html',
  styleUrls: ['./make-a-reservation.component.scss']
})

export class MakeReservationComponent implements OnInit {
  @ViewChild('allergyModal', { static: true }) allergyModal: ElementRef;
  @ViewChild('signupModal', { static: true }) signupModal: ElementRef;
  @ViewChild('loginModal', { static: true }) loginModal: ElementRef;
  @ViewChild('forgotModal', { static: true }) forgotModal: ElementRef;
  @ViewChild('profileModal', { static: true }) profileModal: ElementRef;
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;
  @ViewChild('closeForgotUpModal') closeForgotUpModal;
  @ViewChild('newPassword', { static: true }) newPassword: ElementRef;

  private subs = new Subscription();
  mySubscription: Subscription;

  user: User;
  firstUser: User = new User();
  secondUser: User = new User();
  forgotUser: User = new User();
  restaurant: Restaurant = new Restaurant();
  siteSetting: SiteSetting = new SiteSetting();
  promotions: Promotions = new Promotions();
  booking: Booking = new Booking();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();

  publishKey: string;
  isConnect: string;
  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  timingOrderType: string = 'delivery';
  counter: number = 20;
  timeSlots = [];
  noOfGuest = [];
  minDate: any;
  maxDate: any;
  bookingDate: any;
  phoneTab = false;
  verifyOtp: string;
  emailOtp: string = '';
  streetAddress: SafeResourceUrl;
  streetRedirectAddress: string;

  hide: boolean = true;
  chide: boolean = true;
  lhide: boolean = true;

  errorTime = null;
  bookingComplete: boolean = false;
  bookingPayment: boolean = false;
  isLoading = false; error = null;
  isBookingLoading = false; errorBooking = null;
  isModelOtpLoading = false; Modelotperror = null;
  isModelProfileLoading = true; ModelProfileerror = false;
  isModelLoading = false; Modelerror = null;
  isSignModelLoading = false; SignModelerror = null;
  isOtpLoading = false; Otperror = null;
  isChangePasswordLoading = false; errorChangePassword = null;

  otpVisible = false;

  constructor(
    private restaurantService: RestaurantService,
    private siteSettingService: SiteSettingService,
    public userService: UserService,
    private stripeCustomerService: StripeCustomerService,
    private bookingService: BookingService,
    private route: ActivatedRoute,
    // private socialAuthService: SocialAuthService,
    private modalService: NgbModal,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private router: Router,
    public canonicalService: CanonicalService,
    public sanitizer: DomSanitizer,
    private location: Location,
  ) { }

  ngOnInit() {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    this.fetchSiteSetting();
    if (this.route.snapshot.paramMap.get('name') && this.route.snapshot.paramMap.get('name') != null) {
      this.restaurant.seo_url = this.route.snapshot.paramMap.get('name');
      this.fetchRestaurantName();
    } else if (this.restaurant.id) {
      this.fetchRestaurant();
    } else {
      this.router.navigateByUrl('/location/' + this.restaurantService.zipcode)
    }
  }

  fetchSiteSetting() {
    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
      }, err => this.error = err)
    );
  }

  hidePassword() {
    this.hide = !this.hide;
  }

  hideCpassword() {
    this.chide = !this.chide;
  }

  hideLpassword() {
    this.lhide = !this.lhide;
  }

  onSignupSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.signup(form)
  }

  signup(form: NgForm) {
    this.isSignModelLoading = true; this.SignModelerror = null;

    this.userService.signup(this.firstUser)
      .pipe(finalize(() => (this.isSignModelLoading = false)))
      .subscribe(
        (res) => {
          this.user = res;
          this.userService.saveUser(res);
          this.modalService.dismissAll();
          this.fetchRestaurant();
        },
        (err) => {
          this.SignModelerror = err;
        }
      );
  }

  loginSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.login(form)
  }

  login(form: NgForm) {
    this.isLoading = true; this.error = null;

    this.userService.login(this.secondUser)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.user = res;
          this.userService.saveUser(res);
          this.modalService.dismissAll();
          this.fetchRestaurant();
        },
        (err) => {
          this.error = err;
        }
      );
  }

  onSubmitForgot(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.forgotPassword(form);
  }

  forgotPassword(form: NgForm) {
    this.isModelLoading = true; this.Modelerror = null;

    this.subs.add(
      this.userService.forgotpassword(this.forgotUser).
        pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz");
            this.forgotUser = res;
            this.otpVisible = true;
          },
          (err) => {
            this.Modelerror = err;
          }
        )
    );
  }

  onNewSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isOtpLoading = true; this.Otperror = null;

    this.forgotUser.verify_type = 'email';
    this.forgotUser.otp = this.forgotUser.email_otp;

    this.subs.add(
      this.userService.varifyBothOtp(this.forgotUser).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.otpVisible = false;
            this.closeForgotUpModal?.nativeElement?.click();
            this.handleCancel();
            this.modalService.open(this.newPassword, { backdrop: 'static', backdropClass: 'customBackdrop', });
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  handleInput(event: any, index: number = 0) {
    const digit = event.target.value.replace(/\D/g, '');
    this.emailOtp = this.emailOtp.substring(0, index) + digit + this.emailOtp.substring(index + 1);
    this.forgotUser.email_otp = this.emailOtp;

    if (index < 5 && digit.length === 1) {
      document.getElementById(`otp-input-${index + 1}`)?.focus();
    }
  }

  onChangePasswordSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.changePassword(form)
  }

  changePassword(form: NgForm) {
    this.isChangePasswordLoading = true; this.errorChangePassword = null;

    this.userService.changepassword(this.forgotUser)
      .pipe(finalize(() => (this.isChangePasswordLoading = false)))
      .subscribe(
        (res) => {
          this.notificationService.showSuccess("Password changed successfully.", "Gogrubz");
          this.modalService.dismissAll();
          this.forgotUser = new User();
          this.modalService.open(this.loginModal, {});
        },
        (err) => {
          this.errorChangePassword = err;
        }
      );
  }

  loginWithFacebook(): void {
    // this.socialAuthService.signIn(FacebookLoginProvider.PROVIDER_ID);
  }

  loginWithGoogle(): void {
    // this.socialAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);
  }

  fetchRestaurant() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_tag.booking_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.booking_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.booking_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.booking_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.meta_tag.booking_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.meta_tag.booking_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.gogrubz_meta_description });
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }

        if (this.restaurant.sourcelatitude) {
          let address = this.restaurant.street_address.split(' ').join('+');
          let addresses = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q='
            + address.split(',').join('') + '&z=14&output=embed';
          this.streetAddress = this.sanitizer.bypassSecurityTrustResourceUrl(addresses);
          this.streetRedirectAddress = 'https://www.google.com/maps?q=' + this.restaurant.sourcelatitude + ',' + this.restaurant.sourcelongitude;
        }

        this.promotions = this.restaurant.promotions[0];

        for (let i = 1; i <= this.restaurant.no_of_guest; i++) {
          this.noOfGuest.push(i);
        }

        this.booking.restaurant_id = this.restaurant.id;
        this.booking.customer_id = this.userService?.user?.id;
        this.booking.customer_name = this.userService?.user?.first_name;
        this.booking.booking_email = this.userService?.user?.username;
        this.booking.booking_phone = this.userService?.user?.phone_number;
        //Time Slot Date Wise Find
        this.findTimeSlot();

        this.minDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
        this.maxDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 4, day: new Date(new Date().setDate(new Date().getDate())).getDate() };
        this.bookingDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };

        this.booking.booking_date = this.convertToDate(new Date());
        if (this.booking.guest_count == undefined) {
          this.booking.guest_count = "";
        }
        if (this.booking.booking_time == undefined) {
          this.booking.booking_time = "";
        }
        if (this.restaurant.booking_payment != 'none') {
          this.fetchCards();
        }

        if (this.restaurant?.business?.connect_service) {
          this.publishKey = this.restaurant?.business?.connect_stripe_public_key
          this.isConnect = 'connect';
        } else {
          if (this.restaurant?.site_setting?.stripe_mode == 'Test') {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkeyTest
          } else {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkey
          }
          this.isConnect = 'normal';
        }
        if (this.publishKey) {
          this.stripe = Stripe(this.publishKey);
        }
      }, err => this.error = err)
    );
  }

  fetchRestaurantName() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show_name(this.restaurant.seo_url)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(environment.googleFirebase, this.restaurant.id);
          if (!localStorage.getItem(environment.zipcode)) {
            localStorage.setItem(environment.zipcode, res.zipcode);
          }
        }
        if (this.restaurant.meta_tag.booking_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.booking_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.booking_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.booking_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.meta_tag.booking_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.meta_tag.booking_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.gogrubz_meta_description });
        }
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }

        if (this.restaurant.sourcelatitude) {
          let address = this.restaurant.street_address.split(' ').join('+');
          let addresses = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q='
            + address.split(',').join('') + '&z=14&output=embed';
          this.streetAddress = this.sanitizer.bypassSecurityTrustResourceUrl(addresses);
          this.streetRedirectAddress = 'https://www.google.com/maps?q=' + this.restaurant.sourcelatitude + ',' + this.restaurant.sourcelongitude;
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        this.promotions = this.restaurant.promotions[0];

        for (let i = 1; i <= this.restaurant.no_of_guest; i++) {
          this.noOfGuest.push(i);
        }

        this.minDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
        this.maxDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 4, day: new Date(new Date().setDate(new Date().getDate())).getDate() };
        this.bookingDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
        this.booking.booking_date = this.convertToDate(new Date());
        this.booking.restaurant_id = this.restaurant.id;
        this.booking.customer_id = this.userService?.user?.id;
        this.booking.customer_name = this.userService?.user?.first_name;
        this.booking.booking_email = this.userService?.user?.username;
        this.booking.booking_phone = this.userService?.user?.phone_number;
        //Time Slot Date Wise Find
        this.findTimeSlot();

        if (this.booking.guest_count == undefined) {
          this.booking.guest_count = "";
        }
        if (this.booking.booking_time == undefined) {
          this.booking.booking_time = "";
        }
        if (this.restaurant.booking_payment != 'none') {
          this.fetchCards();
        }
        if (this.restaurant?.business?.connect_service) {
          this.publishKey = this.restaurant?.business?.connect_stripe_public_key
          this.isConnect = 'connect';
        } else {
          if (this.restaurant?.site_setting?.stripe_mode == 'Test') {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkeyTest
          } else {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkey
          }
          this.isConnect = 'normal';
        }
        if (this.publishKey) {
          this.stripe = Stripe(this.publishKey);
        }
      }, err =>
        this.router.navigateByUrl('/' + this.restaurant?.city_name + '/' + this.restaurant.seo_url + '/menus')
      )
    );
  }

  findTimeSlot() {
    this.subs.add(
      this.restaurantService.bookingtimeslot({ restaurant_id: this.restaurant.id, date: this.booking.booking_date }).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            var lastChar = res.booking.slice(-1);
            if (lastChar == ',') {
              res.booking = res.booking.slice(0, -1); // trim last character
            }
            if (res.booking != '') {
              this.timeSlots = res.booking.split(',');
              this.booking.booking_time = this.timeSlots[0];
              this.errorTime = null;
            } else {
              this.timeSlots = [];
              this.booking.booking_time = 'Close';
            }
          },
          (err) => {
          }
        )
    )
  }

  allergyShow() {
    this.modalService.dismissAll();
    this.modalService.open(this.allergyModal, {});
  }

  onSelect(evt: any) {
    var deliveryDates = this.convertToDate(new Date(evt.year, evt.month - 1, evt.day));
    this.booking.booking_date = deliveryDates;
    this.timeSlots = [];
    this.findTimeSlot();
  }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.bookingPlace()
  }

  bookingPlace() {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    this.errorTime = null;

    if (this.booking.booking_time == 'Close') {
      this.errorTime = 'Restaurant close for the selected date, Please select another date.';
      this.isBookingLoading = false;
      return;
    }

    if (!this.userService?.user?.id) {
      this.modalService.open(this.signupModal, { backdrop: 'static', });
      return;
    }

    if ((((!this.userService.user.phone_verify && this.siteSetting.booking_verify_type == 'phone') || !this.userService.user.email_verify && this.siteSetting.booking_verify_type == 'mail') || (this.siteSetting.booking_verify_type == 'both' && (!this.userService.user.phone_verify || !this.userService.user.email_verify))) && this.siteSetting?.booking_verify == '1') {
      this.otpSend();
      this.modalService.open(this.otpModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
      return
    }

    this.isBookingLoading = true; this.errorBooking = null;
    this.booking.type = 'Web';
    this.booking.customer_id = this.userService?.user?.id;
    if (this.restaurant.booking_payment == 'none') {
      this.booking.status = 'Pending';
    }

    if (this.booking.id) {
      this.subs.add(
        this.bookingService.update(this.booking).
          pipe(finalize(() => this.isBookingLoading = false))
          .subscribe(
            (res) => {
              this.booking = res;
              if (this.restaurant.booking_payment != 'none') {
                this.bookingPayment = true;
              } else {
                this.notificationService.showSuccess("Your reservation placed successfully!!", "Go grubz")
                this.bookingComplete = true;
              }
            },
            (err) => {
              this.notificationService.showError(err, "Go grubz")
            }
          )
      )
    } else {
      this.subs.add(
        this.bookingService.create(this.booking).
          pipe(finalize(() => this.isBookingLoading = false))
          .subscribe(
            (res) => {
              this.booking.id = res.id;
              if (this.restaurant.booking_payment != 'none') {
                this.bookingPayment = true;
                if (this.stripeCustomers.length == 0) {
                  setTimeout(() => {
                    this.initCard();
                  }, 2000);
                }
              } else {
                this.notificationService.showSuccess("Your reservation placed successfully!!", "Go grubz")
                this.bookingComplete = true;
              }
            },
            (err) => {
              this.notificationService.showError(err, "Go grubz")
            }
          )
      )
    }
  }

  fetchCards() {
    this.subs.add(
      this.stripeCustomerService.get({ customer_id: this.user?.id, nopaginate: "1" }) // service_type: this.isConnect, 
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
            this.booking.card_id = this.stripeCustomers[0].id;
          },
          (err) => {
            this.stripeCustomers = [];
            this.booking.card_id = '';
          }
        )
    );
  }

  cardPaymentMethod(cardId: string) {
    this.booking.card_id = cardId;
  }

  checkPaymentMethod(payment_method_name: string) {
    this.booking.card_id = '';
    this.initCard();
  }

  initCard() {
    var elements = this.stripe.elements();

    var style = {
      base: {
        'height': '52px',
        'fontFamily': 'Visby CF',
        'fontWeight': '700',
        'borderRadius': '10px',
        'lineHeight': '1.5',
        'fontSize': '16px',
        'color': '#000',
      }
    };

    // Card number
    this.card = elements.create('cardNumber', {
      'placeholder': 'Enter card number',
      'style': style
    });
    this.card.mount('#card-number');

    // CVC
    var cvc = elements.create('cardCvc', {
      'placeholder': 'CVC',
      'style': style
    });
    cvc.mount('#card-cvc');

    // Card expiry
    var exp = elements.create('cardExpiry', {
      'placeholder': 'Expiry date',
      'style': style
    });
    exp.mount('#card-exp');

    // Postal Code
    var postalCode = elements.create('postalCode', {
      'placeholder': 'Zip Code',
      'style': style
    });
    postalCode.mount('#postalCode');

    // this.card = elements.create('card', { hidePostalCode: true });
    // this.card.mount(this.cardInfo);
    this.isModelLoading = false;
  }

  async handleForm(e) {
    e.preventDefault();
    // this.isModelLoading = true;
    this.isBookingLoading = true;
    this.Modelerror = false;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        if (!result.error) {
          this.stripeCustomer.customer_id = this.booking.customer_id;
          this.stripeCustomer.customer_name = this.booking.customer_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = this.isConnect;
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isBookingLoading = false))
              .subscribe(
                (res) => {
                  this.subs.add(
                    this.stripeCustomerService.get({ customer_id: this.user?.id, nopaginate: "1" }) // service_type: this.isConnect, 
                      .pipe(finalize(() => { }))
                      .subscribe(
                        (res) => {
                          this.stripeCustomers = res;
                          this.booking.card_id = this.stripeCustomers[0].id;
                          this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.booking.card_id);
                          this.createPaymentIntent(this.stripeCustomer);
                        },
                        (err) => {
                          this.stripeCustomers = [];
                        }
                      )
                  );
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.errorBooking = err;
                }
              )
          )
        } else {
          this.isBookingLoading = false
          if (!this.booking.card_id) {
            this.errorBooking = 'Please enter your card details first!';//result.error.message;
          } else {
            this.errorBooking = 'Something went wrong!';//result.error.message;
          }
        }
      });
  }

  validateBooking() {
    this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.booking.card_id);
    this.createPaymentIntent(this.stripeCustomer);
  }

  async createPaymentIntent(stripeCustomerNew) {
    this.isBookingLoading = true;

    this.stripeCustomer = stripeCustomerNew;
    this.stripeCustomer.restaurant_id = this.booking.restaurant_id;
    this.stripeCustomer.amount = this.restaurant.booking_payment == 'single' ? this.restaurant.booking_amount : (this.restaurant.booking_amount * parseFloat(this.booking.guest_count));
    this.stripeCustomer.booking_id = this.booking.id;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.errorBooking = 'Sorry your Payment Faild! Please try again';
            this.isBookingLoading = false;
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isBookingLoading = false;
        this.errorBooking = 'Something went wrong!';//result.error.message;
      } else {
        if (result.paymentIntent.status === 'succeeded') {
          const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
          this.errorTime = null;

          if (this.booking.booking_time == 'Close') {
            this.errorTime = 'Restaurant close for the selected date, Please select another date.';
            this.isBookingLoading = false;
            return;
          }

          if (!this.userService?.user?.id) {
            this.modalService.open(this.signupModal, { backdrop: 'static', });
            return;
          }

          this.isBookingLoading = true; this.errorBooking = null;
          this.booking.booking_amount = this.restaurant.booking_payment == 'single' ? this.restaurant.booking_amount : (this.restaurant.booking_amount * parseFloat(this.booking.guest_count));
          this.booking.txn_id = result.paymentIntent.id;
          this.booking.status = 'Pending';

          this.subs.add(
            this.bookingService.update(this.booking).
              pipe(finalize(() => this.isBookingLoading = false))
              .subscribe(
                (res) => {
                  this.notificationService.showSuccess("Your reservation placed successfully!!", "Go grubz")
                  this.booking = res;
                  this.bookingPayment = false;
                  this.bookingComplete = true;
                },
                (err) => {
                  this.notificationService.showError(err, "Go grubz")
                }
              )
          )
        }
      }
    });
  }

  convertNumber(event) {
    var val = parseFloat(event);
    var val1 = (val).toFixed(2);
    return val1
  }

  updateUser(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isModelProfileLoading = true; this.ModelProfileerror = null;

    if (this.userService.user.phone_number != this.userService.user.phone_number) {
      this.userService.user.phone_verify = false;
      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => {
            this.user = res;
            this.booking.booking_phone = this.userService.user.phone_number;
            this.counter = 20;
            this.mySubscription.unsubscribe();
          },
          (err) => { }
        );
    }

    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.notificationService.showSuccess("Profile updated successfully !!", "Gogrubz")
          this.modalService.dismissAll();
          if ((((!this.userService.user.phone_verify && this.siteSetting.order_verify_type == 'phone') || !this.userService.user.email_verify && this.siteSetting.order_verify_type == 'mail') || (this.siteSetting.order_verify_type == 'both' && (!this.userService.user.phone_verify || !this.userService.user.email_verify))) && this.siteSetting?.order_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
      }, err => this.Modelotperror = err)
    );
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    // || event.keyCode == 32 
    if (event.keyCode == 189 || event.keyCode == 9 || event.keyCode == 8 || event.keyCode == 48 || (event.keyCode >= 97 && event.keyCode <= 122) || (event.keyCode >= 65 && event.keyCode <= 90)) {
    } else if (event.code === 'Space') {
    } else {
      event.preventDefault();
    }
  }

  redirectRestaurant(restaurant) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, restaurant.id);
    }
    this.router.navigateByUrl('/' + restaurant.city_name + '/' + restaurant.seo_url + '/menus');
  }

  otpSend() {
    this.isModelOtpLoading = true;
    this.phoneTab = true;
    if (this.siteSetting.booking_verify_type == 'both') {
      if (!this.userService.user.phone_verify && !this.userService.user.email_verify) {
        this.userService.user.verify_type = 'both';
      } else {
        if (!this.userService.user.phone_verify && this.userService.user.email_verify) {
          this.userService.user.verify_type = 'phone';
        }
        if (this.userService.user.phone_verify && !this.userService.user.email_verify) {
          this.userService.user.verify_type = 'email';
        }
      }
    } else {
      if (this.siteSetting.booking_verify_type == 'mail') {
        this.userService.user.verify_type = 'email';
      } else {
        this.userService.user.verify_type = this.siteSetting.booking_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.mySubscription = interval(1000).subscribe(x => {
              this.timer();
            });
            this.notificationService.showSuccess("OTP sent successfully !!", "Go grubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.isModelOtpLoading = true;
    this.phoneTab = true;

    if (this.siteSetting.booking_verify_type == 'both') {
      if (!this.userService.user.phone_verify && !this.userService.user.email_verify) {
        this.userService.user.verify_type = 'both';
      } else {
        if (!this.userService.user.phone_verify && this.userService.user.email_verify) {
          this.userService.user.verify_type = 'phone';
        }
        if (this.userService.user.phone_verify && !this.userService.user.email_verify) {
          this.userService.user.verify_type = 'email';
        }
      }
    } else {
      if (this.siteSetting.booking_verify_type == 'mail') {
        this.userService.user.verify_type = 'email';
      } else {
        this.userService.user.verify_type = this.siteSetting.booking_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.counter = 20;
            this.mySubscription = interval(1000).subscribe(x => {
              this.timer();
            });
            this.notificationService.showSuccess("OTP sent successfully !!", "Go grubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  timer() {
    this.counter = this.counter - 1;
    if (this.counter <= 0) {
      this.mySubscription.unsubscribe();
    }
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.validateOtp();
  }

  validateOtp() {
    this.isModelOtpLoading = true; this.Modelotperror = false;

    if (this.siteSetting.booking_verify_type == 'both') {
      if (!this.userService.user.phone_verify && !this.userService.user.email_verify) {
        this.userService.user.verify_type = 'both';
      } else {
        if (!this.userService.user.phone_verify && this.userService.user.email_verify) {
          this.userService.user.verify_type = 'phone';
        }
        if (this.userService.user.phone_verify && !this.userService.user.email_verify) {
          this.userService.user.verify_type = 'email';
        }
      }
    } else {
      if (this.siteSetting.booking_verify_type == 'mail') {
        this.userService.user.verify_type = 'email';
      } else {
        this.userService.user.verify_type = this.siteSetting.booking_verify_type;
      }
    }

    this.userService.user.email_otp = this.userService.user.otp
    this.subs.add(
      this.userService.varifyBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.phoneTab = false;
            this.subs.add(this.userService.me()
              .pipe(finalize(() => this.isModelOtpLoading = false))
              .subscribe(res => {
                this.user = res
                this.notificationService.showSuccess("OTP verify successfully !!", "Go grubz")
                this.bookingPlace();
              }, err => this.Modelotperror = err)
            );
            this.modalService.dismissAll();
          },
          (err) => {
            this.Modelotperror = err
            this.notificationService.showError(err, "Go grubz")
          }
        )
    )
  }

  openModal(model) {
    this.modalService.dismissAll();
    this.modalService.open(model).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  handleCancel(): void {
    this.isLoading = false;
    this.error = null;
    this.user = new User();
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() { this.subs.unsubscribe(); }

}
