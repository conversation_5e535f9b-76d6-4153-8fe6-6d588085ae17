import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { City } from '../../core/models/city';
import { CityService } from '../../core/services/city.service';
import { NgForm } from '@angular/forms';
import { RestaurantService } from '../../core/services/restaurant.service';
import { Restaurant } from '../../core/models/restaurant';
import { User } from '../../core/models/user';
import { environment } from '../../../environments/environment';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-locations',
  host: { ngSkipHydration: 'true' },
  templateUrl: './locations.component.html',
  styleUrls: ['./locations.component.scss']
})

export class LocationsComponent implements OnInit {
  private subs = new Subscription();

  cities: City[] = [];
  restaurants: Restaurant[] = [];
  restaurant: Restaurant = new Restaurant();

  center: google.maps.LatLngLiteral = { lat: 52.56910257184157, lng: -1.9816220306280343 };
  zoom = 12;
  markers: Marker[] = [];

  isLoading = false; error = null;
  isMapLoading = false; errorMap = null;

  scrollingAlpha: string = 'A';

  alphaStatus = [
    { id: 'A', name: 'A' },
    { id: 'B', name: 'B' },
    { id: 'C', name: 'C' },
    { id: 'D', name: 'D' },
    { id: 'E', name: 'E' },
    { id: 'F', name: 'F' },
    { id: 'G', name: 'G' },
    { id: 'H', name: 'H' },
    { id: 'I', name: 'I' },
    { id: 'J', name: 'J' },
    { id: 'K', name: 'K' },
    { id: 'L', name: 'L' },
    { id: 'M', name: 'M' },
    { id: 'N', name: 'N' },
    { id: 'O', name: 'O' },
    { id: 'P', name: 'P' },
    { id: 'Q', name: 'Q' },
    { id: 'R', name: 'R' },
    { id: 'S', name: 'S' },
    { id: 'T', name: 'T' },
    { id: 'U', name: 'U' },
    { id: 'V', name: 'V' },
    { id: 'W', name: 'W' },
    { id: 'X', name: 'X' },
    { id: 'Y', name: 'Y' },
    { id: 'Z', name: 'Z' },
  ]

  options = {
    query: null,
    nopaginate: 1,
  };

  constructor(
    private citiesService: CityService,
    private restaurantService: RestaurantService,
    private route: ActivatedRoute,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit() {
    this.fetchCities();
    this.fetchRestaurants();
  }

  onCity(name) {
    this.restaurant.city_name = name
    localStorage.setItem(environment.zipcode, name);
    this.restaurantService.zipcode = name;
    this.router.navigateByUrl('/location/' + name);
    // this.restaurantService.findCity(this.restaurant)
    //   .pipe(finalize(() => (this.isLoading = false)))
    //   .subscribe(
    //     (res) => {
    //       if (typeof localStorage !== 'undefined') {
    //         localStorage.setItem(environment.zipcode, res.post_code);
    //         this.restaurantService.zipcode = res.post_code;
    //       }
    //       this.router.navigateByUrl('/location/' + res.post_code);
    //     },
    //     (err) => {
    //       alert('location not found, please try another city.');
    //     }
    //   );
  }

  fetchRestaurants() {
    this.isMapLoading = true; this.errorMap = null;

    this.subs.add(this.restaurantService.mini({ nopaginate: 1 })
      .pipe(finalize(() => this.isMapLoading = false))
      .subscribe(res => {
        this.restaurants = res;
        this.restaurants.forEach(rest => {
          let markerObject: Marker = new Marker();
          if (rest.sourcelatitude && rest.sourcelongitude) {
            markerObject.title = rest.restaurant_name.trim();
            markerObject.label = rest.restaurant_name.charAt(0).toUpperCase();
            markerObject.position = { lat: Number(rest.sourcelatitude), lng: Number(rest.sourcelongitude) };
            this.markers.push(markerObject);
          }
        });
      }, err => { this.errorMap = err; })
    );
  }

  fetchCities() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.citiesService.history({ nopaginate: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.cities = res;
      }, err => { this.cities = []; this.error = err; })
    );
  }

  restaurantFetch(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.restaurantService.saveZipCode(this.restaurant.zipcode);
    this.router.navigateByUrl('/location/' + this.restaurant.zipcode);
  }

  clickAlpha(alphaId) {
    this.scrollingAlpha = alphaId;
    this.scroll('alpha_' + alphaId);
  }

  scroll(id) {
    var top = this.document.getElementById(id).offsetTop - 150;
    window.scrollTo(0, top);
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

class Marker {
  position: google.maps.LatLngLiteral;
  label: string;
  title: string;
}

