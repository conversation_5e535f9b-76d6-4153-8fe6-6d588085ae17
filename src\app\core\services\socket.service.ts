// socket.service.ts
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';

@Injectable({
    providedIn: 'root',
})
export class SocketService {
    private socket: Socket;

    connectSocket(businessId: string, user: string, soketUrl: string) {
        this.socket = io(soketUrl, {
            query: {
                businessId: businessId,
                userNameId: `GoGrubz (Web),${user}`,
            },
            transports: ['websocket'],
        });
    }

    connectSoketWithOrder(businessId: string, user: string, soketUrl: string, orderNumber: string) {
        this.socket = io(soketUrl, {
            query: {
                businessId: businessId,
                userNameId: `GoGrubz (Web),${user}`,
                orderNumber: orderNumber,
            },
            transports: ['websocket'],
        });

        this.socket.on('connect', () => {
            // console.log('✅ Socket connected:', this.socket.id);
        });

        this.socket.on('error', (error) => {
            // console.error('❌ Socket connection error:', error);
        });

        this.socket.on('disconnect', (reason) => {
            // console.warn('⚠️ Socket disconnected:', reason);
        });
    }

    // Example: emit or listen
    emit(event: string, data: any) {
        this.socket.emit(event, data);
    }

    listen<T>(event: string): Observable<T> {
        return new Observable<T>(subscriber => {
            this.socket.on(event, (data: T) => {
                subscriber.next(data);
            });
        });
    }

    // Optional: To check connection status
    isConnected(): boolean {
        return this.socket && this.socket.connected;
    }

    disconnectSocket() {
        if (this.isConnected()) {
            this.socket.disconnect();
        }
    }
}
