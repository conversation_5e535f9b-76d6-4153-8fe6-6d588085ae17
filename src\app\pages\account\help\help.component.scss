.help-support-box{
    padding: 30px ;
    border-radius: 15px;
    text-align: center;
    background-color: #FFFFFF;
    transition: all 0.5s;
    box-shadow: 0px 0px 22.5px 0.75px #0000001A;
    margin-bottom: 40px;
}
.help-support-box h6{
    font-family:'Fredoka One';
    font-size: 30px;
    font-weight: 400;
    line-height: 40px;
    margin-bottom: 30px;
}
.help-support-box p{
    font-family: 'Visby CF';
    font-size: 18px;
    font-weight: 600;
    line-height: 22px; 
    margin-bottom: 30px;   
}
.help-support-box .btn{
    width:185px;
    font-size: 18px;
}
.help-support-box:hover{
    background-color: #FC353A;
}
.help-support-box:hover p,
.help-support-box:hover h6{
    color:#FFFFFF;
}
.help-support-box:hover button.btn{
    color:#FC353A !important;
    border-color:#FFFFFF !important;
    background-color: #FFFFFF !important;
}
.help-support-box button.btn:hover{
    border-color: #fff !important;
    color: #FFFFFF !important;
    background-color: transparent !important;
}

.main-heading {
    margin-bottom: 10px;
}

.main-heading h6 {
    font-family: 'Visby CF';
    font-weight: 700;
    font-size: 30px;
    margin-bottom: 0;
}

.accordion-item {
    margin: 40px 0;
    border: 0;
    box-shadow: 0px 0px 10px 3px #0000001A;
    border-radius: 15px;
}

.accordion-button:after {
    transition: all 0.5s;
}

.accordion-header button.accordion-button {
    font-family: 'Visby CF';
    font-size: 24px;
    color: #000000;
    display: inline-block;
    width: 100%;
    text-decoration: none;
    font-weight: 700;
    position: relative;
    padding:13px 15px 13px 90px;
    box-shadow: none;
    color: #FFFFFF;
    background-color: #ea3323;
    border-radius: 15px;
    cursor: pointer;
}

.accordion-header button.accordion-button.collapsed {
    color: #202020;
    background-color: #FFFFFF;
}
.accordion-header button.accordion-button::before {
    width: 35px;
    height: 35px;
    text-align: center;
    font-size: 24px;
    position: absolute;
    left: 25px;
    top: 13px;
    content: "\f068";
    font-family: 'fontawesome';    
    transition: all 0.5s;
    color: #FFFFFF;
    
}
.accordion-header button.accordion-button.collapsed::before {
    content: "\2b";
    color: #202020;
}

.accordion-body {
    padding:25px 30px 25px 90px;
}

.accordion-body p {
    font-size: 20px;
}

@media screen and (max-width:1500px) {
    .help-support-box{
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    .help-support-box h6 {
        font-size: 25px;
        margin-bottom: 25px;
    }
    .help-support-box p {
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 30px;
    }
    .help-support-box .btn {
        font-size: 13px;
        width: 132px;        
        padding: 3px 10px;
    }
    .main-heading h6 {
        font-size: 24px;
    }
    .accordion-item{
        margin: 30px 0;
        border-radius: 12px;
    }
    .accordion-header button.accordion-button {
        font-size: 20px;
        padding: 11px 15px 11px 70px;
        border-radius: 12px;
    }
    .accordion-header button.accordion-button:before {
        font-size: 20px;        
        top: 11px;
        left: 20px;        
        width: 30px;
        height: 30px;
    }
    .accordion-body{
        padding: 20px 20px 20px 70px;
    }
    .accordion-body p {
        font-size: 18px;
    }

}

@media screen and (max-width:1199px) {
    .help-support-box{
        padding: 15px;
    }    
    .help-support-box h6 {
        font-size: 22px;
        margin-bottom: 15px;
    }
    .help-support-box p {
        font-size: 13px;
        margin-bottom: 20px;
    }
    .accordion-body p {
        font-size: 16px;
    }

}

@media screen and (max-width:991px) {
    .help-support-box h6 {
        line-height: 30px;
    }

}

@media screen and (max-width:767px) {
    .accordion-item{
        margin: 20px 0;
    }
    .accordion-header button.accordion-button {
        font-size: 18px;
        padding: 10px 10px 10px 60px;
    }
    .accordion-body{
        padding: 15px 15px 15px 60px;
    }
    .accordion-header button.accordion-button:before {
        font-size: 16px;
        top: 12px;
        left: 15px;
    }
}

/* Chat Box Styles */
.chat-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
        opacity: 1;
        visibility: visible;
    }
}

.chat-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 380px;
    max-width: calc(100vw - 40px);
    height: 600px;
    max-height: calc(100vh - 40px);
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    transform: translateY(100%) scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &.active {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.chat-header {
    background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
    color: white;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 20px 20px 0 0;
    min-height: 60px;

    .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .back-btn {
            background: none;
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            i {
                font-size: 16px;
            }
        }

        .header-title {
            h4 {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
                color: #fff;
                font-family: 'Visby CF', sans-serif;
            }
        }
    }

    .header-right {
        display: flex;
        gap: 8px;
        align-items: center;

        .dropdown-container {
            position: relative;

            .menu-btn {
                background: none;
                border: none;
                color: white;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: background-color 0.2s ease;

                &:hover {
                    background: rgba(255, 255, 255, 0.2);
                }

                i {
                    font-size: 14px;
                }
            }

            .dropdown-menu {
                position: absolute;
                top: 100%;
                right: 0;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                min-width: 180px;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.2s ease;
                margin-top: 8px;

                &.show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }

                .dropdown-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    width: 100%;
                    padding: 12px 16px;
                    background: none;
                    border: none;
                    color: #333;
                    font-size: 14px;
                    font-family: 'Visby CF', sans-serif;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                    border-radius: 0;

                    &:first-child {
                        border-radius: 12px 12px 0 0;
                    }

                    &:last-child {
                        border-radius: 0 0 12px 12px;
                    }

                    &:only-child {
                        border-radius: 12px;
                    }

                    &:hover {
                        background: #f5f5f5;
                    }

                    i {
                        font-size: 14px;
                        color: #666;
                        width: 16px;
                    }

                    span {
                        font-weight: 400;
                    }
                }
            }
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            i {
                font-size: 14px;
            }
        }
    }
}

.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;

    &.minimized {
        height: 0;
        opacity: 0;
    }

    .welcome-section {
        padding: 40px 20px 20px;
        text-align: center;
        background: #f8f9fa;

        .illustration-container {
            margin-bottom: 20px;

            // .support-character {
            //     position: relative;
            //     display: inline-block;

            //     .character-avatar {
            //         position: absolute;
            //         top: 15px;
            //         left: 15px;
            //         width: 40px;
            //         height: 40px;
            //         background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
            //         border-radius: 50%;
            //         display: flex;
            //         align-items: center;
            //         justify-content: center;
            //         z-index: 2;
            //         box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);

            //         .avatar-letter {
            //             color: white;
            //             font-weight: 700;
            //             font-size: 18px;
            //             font-family: 'Visby CF', sans-serif;
            //         }
            //     }

            //     .character-illustration {
            //         width: 280px;
            //         height: 160px;
            //         background: #f0f0f0;
            //         border-radius: 20px;
            //         position: relative;
            //         overflow: hidden;

            //         // Red platform/bench
            //         &::before {
            //             content: '';
            //             position: absolute;
            //             bottom: 30px;
            //             left: 50%;
            //             transform: translateX(-50%);
            //             width: 120px;
            //             height: 20px;
            //             background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
            //             border-radius: 10px;
            //             box-shadow: 0 4px 8px rgba(252, 53, 58, 0.3);
            //         }

            //         // Red platform legs
            //         &::after {
            //             content: '';
            //             position: absolute;
            //             bottom: 20px;
            //             left: 50%;
            //             transform: translateX(-50%);
            //             width: 100px;
            //             height: 15px;
            //             background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
            //             border-radius: 8px;
            //         }

            //         // .person {
            //         //     position: absolute;
            //         //     bottom: 50px;
            //         //     left: 50%;
            //         //     transform: translateX(-50%);
            //         //     z-index: 1;

            //         //     .head {
            //         //         width: 25px;
            //         //         height: 25px;
            //         //         background: #fdbcbc;
            //         //         border-radius: 50%;
            //         //         margin: 0 auto 3px;
            //         //         position: relative;

            //         //         // Hair
            //         //         &::before {
            //         //             content: '';
            //         //             position: absolute;
            //         //             top: -5px;
            //         //             left: 2px;
            //         //             right: 2px;
            //         //             height: 15px;
            //         //             background: #8B4513;
            //         //             border-radius: 50% 50% 0 0;
            //         //         }
            //         //     }

            //         //     .body {
            //         //         width: 30px;
            //         //         height: 25px;
            //         //         background: #333;
            //         //         border-radius: 6px 6px 0 0;
            //         //         margin: 0 auto;
            //         //         position: relative;

            //         //         // Arms
            //         //         &::before {
            //         //             content: '';
            //         //             position: absolute;
            //         //             top: 5px;
            //         //             left: -8px;
            //         //             width: 6px;
            //         //             height: 15px;
            //         //             background: #fdbcbc;
            //         //             border-radius: 3px;
            //         //         }

            //         //         &::after {
            //         //             content: '';
            //         //             position: absolute;
            //         //             top: 5px;
            //         //             right: -8px;
            //         //             width: 6px;
            //         //             height: 15px;
            //         //             background: #fdbcbc;
            //         //             border-radius: 3px;
            //         //         }
            //         //     }

            //         //     .legs {
            //         //         display: flex;
            //         //         justify-content: space-between;
            //         //         width: 30px;
            //         //         margin: 0 auto;

            //         //         .leg {
            //         //             width: 6px;
            //         //             height: 20px;
            //         //             background: #fdbcbc;
            //         //             border-radius: 3px;

            //         //             &::after {
            //         //                 content: '';
            //         //                 display: block;
            //         //                 width: 12px;
            //         //                 height: 4px;
            //         //                 background: #333;
            //         //                 border-radius: 2px;
            //         //                 margin-top: 16px;
            //         //                 margin-left: -3px;
            //         //             }
            //         //         }
            //         //     }
            //         // }
            //     }
            // }
        }
    }

    .chat-messages {
        flex: 1;
        padding: 0 20px 20px;
        overflow-y: auto;
        scroll-behavior: smooth;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 2px;

            &:hover {
                background: #bbb;
            }
        }
    }

    .message-item {
        margin-bottom: 20px;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        &.sent {
            justify-content: flex-end;
            margin-left: 60px;

            .message-content {
                background: white;
                color: #333;
                border-radius: 20px 20px 6px 20px;
                max-width: 100%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                .message-time {
                     color: #666;
                }
            }

            .user-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                overflow: hidden;
                flex-shrink: 0;
                margin-left: 12px;
                margin-top: 4px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        &.received {
            justify-content: flex-start;
            margin-right: 60px;

            .message-avatar {
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                margin-top: 4px;

                .avatar-letter {
                    color: white;
                    font-weight: 600;
                    font-size: 14px;
                    font-family: 'Visby CF', sans-serif;
                }
            }

            .message-content {
                background: white;
                color: #333;
                border-radius: 20px 20px 20px 6px;
                max-width: 100%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                .message-time {
                    color: #666;
                }
            }
        }

        .message-content {
            padding: 14px 18px;
            font-family: 'Visby CF', sans-serif;

            p {
                margin: 0;
                font-size: 15px;
                line-height: 1.4;
                word-wrap: break-word;
            }

            .message-time {
                font-size: 11px;
                margin-top: 6px;
                display: block;
                opacity: 0.7;
            }
        }
    }

    .typing-indicator {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 20px;
        margin-right: 60px;

        .message-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .avatar-letter {
                color: white;
                font-weight: 600;
                font-size: 14px;
                font-family: 'Visby CF', sans-serif;
            }
        }

        .typing-content {
            background: white;
            border-radius: 20px 20px 20px 6px;
            padding: 14px 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

            .typing-dots {
                display: flex;
                gap: 4px;

                span {
                    width: 8px;
                    height: 8px;
                    background: #FC353A;
                    border-radius: 50%;
                    animation: typing 1.4s infinite ease-in-out;

                    &:nth-child(1) { animation-delay: -0.32s; }
                    &:nth-child(2) { animation-delay: -0.16s; }
                    &:nth-child(3) { animation-delay: 0s; }
                }
            }
        }
    }

    .quick-actions {
        padding: 20px;
        background: #f8f9fa;

        .quick-action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .quick-action-btn {
                background: white;
                border: 1px solid #f83338;
                border-radius: 25px;
                padding: 6px 15px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                font-family: 'Visby CF', sans-serif;
                color: #333;
                font-weight: 500;
                white-space: nowrap;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

                &:hover {
                    border-color: #FC353A;
                    background: #FC353A;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(252, 53, 58, 0.2);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }
    }

    // Order Help Flow Styles
    .order-help-flow {
        padding: 0;
        background: #f8f9fa;

        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;

            .back-btn {
                background: none;
                border: none;
                color: #FC353A;
                font-size: 16px;
                cursor: pointer;
                padding: 8px;
                border-radius: 50%;
                transition: background-color 0.2s ease;

                &:hover {
                    background: rgba(252, 53, 58, 0.1);
                }
            }

            .header-content {
                h4 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: #333;
                }

                p {
                    margin: 0;
                    font-size: 12px;
                    color: #666;
                }
            }

            h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }

            p {
                margin: 0;
                font-size: 12px;
                color: #666;
            }
        }

        .order-selection {
            padding: 20px;
        }

        .orders-list {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .order-card {
                background: white;
                border-radius: 12px;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 12px;
                border: 1px solid #e0e0e0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                .order-content {
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    margin-bottom: 12px;

                    .order-image {
                        width: 100px;
                        height: 100px;
                        border-radius: 8px;
                        overflow: hidden;
                        flex-shrink: 0;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .order-details {
                        flex: 1;
                        line-height: 12px;
                        .order-header {
                            margin-bottom: 4px;

                            h5 {
                                margin: 0;
                                font-size: 14px;
                                // font-weight: 600;
                                color: #333;
                                // font-family: 'Visby CF', sans-serif;
                            }
                        }

                        .order-meta {
                            display: flex;
                            align-items: center;
                            margin-bottom: 4px;
                            font-size: 14px;
                            color: #666;

                            .order-total {
                                font-weight: 600;
                                color: #333;
                            }

                            .order-items-count {
                                margin-left: 4px;
                            }
                        }

                        .order-status-row {
                            display: flex;
                            align-items: center;
                            font-size: 12px;

                            .order-status {
                                padding: 2px 8px;
                                border-radius: 12px;
                                font-weight: 500;
                                font-size: 11px;

                                &.status-completed {
                                    background: #e8f5e8;
                                    color: #2e7d32;
                                }

                                &.status-delivered {
                                    background: #e8f5e8;
                                    color: #2e7d32;
                                }

                                &.status-in-progress {
                                    background: #fff3e0;
                                    color: #f57c00;
                                }

                                &.status-cancelled {
                                    background: #ffebee;
                                    color: #d32f2f;
                                }
                            }

                            .order-date {
                                color: #999;
                                margin-left: 4px;
                            }
                        }

                        .help-order-btn {
                            background: #FC353A;
                            color: white;
                            border: none;
                            border-radius: 20px;
                            margin-top: 5px;
                            padding: 8px 16px;
                            font-size: 12px;
                            font-weight: 600;
                            font-family: 'Visby CF', sans-serif;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            align-self: flex-start;
        
                            &:hover {
                                background: #e02328;
                                transform: translateY(-1px);
                                box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                            }
        
                            &:active {
                                transform: translateY(0);
                            }

                            &.selected {
                                background: #2e7d32;

                                &:hover {
                                    background: #1b5e20;
                                }
                            }
                        }
                    }
                }

            }
        }

        .see-more-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding: 0 20px 20px;

            .see-more-btn {
                background: #FC353A;
                color: white;
                border: none;
                border-radius: 20px;
                padding: 10px 24px;
                font-size: 14px;
                font-weight: 600;
                font-family: 'Visby CF', sans-serif;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 100px;

                &:hover {
                    background: #e02328;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }

        .issue-selection {
            padding: 20px;

            .issue-buttons {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;

                .issue-btn {
                    background: white;
                    color: #FC353A;
                    border: 2px solid #FC353A;
                    border-radius: 20px;
                    padding: 10px 16px;
                    font-size: 14px;
                    font-weight: 600;
                    font-family: 'Visby CF', sans-serif;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    flex: 1;
                    min-width: 140px;
                    text-align: center;

                    &:hover {
                        background: #FC353A;
                        color: white;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                    }

                    &:active {
                        transform: translateY(0);
                    }
                }
            }
        }

        // Detailed Input Section
        .detailed-input-section {
            margin: 20px 0;

            .input-container {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 16px;
                margin: 0 16px;
            }

            .message-input-wrapper {
                margin-bottom: 16px;
            }

            .detail-textarea {
                width: 100%;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                color: #666;
                background: white;
                resize: vertical;
                min-height: 80px;
                font-family: inherit;
                box-sizing: border-box;

                &:focus {
                    outline: none;
                    border-color: #FC353A;
                }

                &::placeholder {
                    color: #999;
                }
            }

            .file-upload-section {
                background: white;
                border: 1px dashed #ccc;
                border-radius: 8px;
                padding: 16px;
                text-align: center;
                margin-bottom: 16px;
            }

            .upload-header {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                margin-bottom: 12px;
            }

            .upload-icon {
                width: 24px;
                height: 24px;
                background: #FC353A;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                font-weight: bold;
            }

            .upload-text {
                text-align: left;
            }

            .upload-title {
                font-size: 14px;
                font-weight: 500;
                color: #333;
                margin-bottom: 2px;
            }

            .upload-subtitle {
                font-size: 12px;
                color: #666;
            }

            .upload-btn {
                background: #FC353A;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.2s ease;

                &:hover {
                    background: #e02328;
                }
            }

            .selected-files {
                margin-bottom: 16px;
            }

            .file-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                margin-bottom: 8px;
                font-size: 12px;
            }

            .file-name {
                color: #333;
                flex: 1;
                text-align: left;
            }

            .remove-file-btn {
                background: none;
                border: none;
                color: #FC353A;
                font-size: 16px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background: #f5f5f5;
                    border-radius: 50%;
                }
            }

            .send-button-container {
                text-align: center;
            }

            .send-detail-btn {
                background: #FC353A;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 25px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 120px;

                &:hover:not(:disabled) {
                    background: #e02328;
                    transform: translateY(-1px);
                }

                &:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
            }
        }

        .selected-order-summary {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;

            .order-items-summary {
                margin-bottom: 12px;

                h5 {
                    margin: 0 0 8px 0;
                    font-size: 14px;
                    font-weight: 600;
                    color: #333;
                }

                ul {
                    margin: 0;
                    padding-left: 20px;

                    li {
                        font-size: 13px;
                        color: #666;
                        margin-bottom: 4px;
                    }
                }
            }

            .order-total-summary {
                font-size: 16px;
                color: #333;
            }
        }

        .issue-selection {
            h5 {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 600;
                color: #333;
            }

            .issues-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 12px;

                .issue-btn {
                    background: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 12px;
                    padding: 16px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    text-align: center;

                    &:hover {
                        border-color: #FC353A;
                        background: rgba(252, 53, 58, 0.02);
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    }

                    i {
                        font-size: 20px;
                        color: #FC353A;
                    }

                    span {
                        font-size: 12px;
                        font-weight: 500;
                        color: #333;
                        line-height: 1.3;
                    }
                }
            }
        }

        .resolution-content {
            .resolution-status {
                background: white;
                border-radius: 12px;
                padding: 20px;
                text-align: center;
                margin-bottom: 20px;
                border: 1px solid #e0e0e0;

                i {
                    font-size: 32px;
                    color: #4caf50;
                    margin-bottom: 12px;
                }

                p {
                    margin: 0;
                    font-size: 14px;
                    color: #666;
                    line-height: 1.4;
                }
            }

            .resolution-actions {
                display: flex;
                gap: 12px;

                .action-btn {
                    flex: 1;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border: none;
                    font-family: 'Visby CF', sans-serif;

                    &.primary {
                        background: #FC353A;
                        color: white;

                        &:hover {
                            background: #e02328;
                            transform: translateY(-1px);
                        }
                    }

                    &.secondary {
                        background: white;
                        color: #666;
                        border: 1px solid #e0e0e0;

                        &:hover {
                            background: #f5f5f5;
                            transform: translateY(-1px);
                        }
                    }
                }
            }
        }
    }

    // Reservation Help Flow Styles
    .reservation-help-flow {
        padding: 0;
        background: #f8f9fa;

        .reservation-selection {
            padding: 20px;
        }

        .reservations-list {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .reservation-card {
                background: white;
                border-radius: 12px;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 12px;
                border: 1px solid #e0e0e0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

                .reservation-content {
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    margin-bottom: 12px;

                    .reservation-image {
                        width: 80px;
                        height: 80px;
                        border-radius: 8px;
                        overflow: hidden;
                        flex-shrink: 0;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .reservation-details {
                        flex: 1;
                        line-height: 1.4;

                        .reservation-header {
                            margin-bottom: 8px;

                            h5 {
                                margin: 0;
                                font-size: 16px;
                                font-weight: 600;
                                color: #333;
                                font-family: 'Visby CF', sans-serif;
                            }
                        }

                        .reservation-meta {
                            display: flex;
                            flex-direction: column;
                            gap: 4px;
                            margin-bottom: 8px;
                            font-size: 14px;
                            color: #666;

                            .reservation-name,
                            .reservation-time {
                                font-weight: 400;
                            }
                        }

                        .reservation-date-row {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            font-size: 14px;
                            color: #666;
                            margin-bottom: 12px;

                            .reservation-date,
                            .reservation-guests {
                                font-weight: 400;
                            }
                        }

                        .help-reservation-btn {
                            background: #FC353A;
                            color: white;
                            border: none;
                            border-radius: 20px;
                            padding: 8px 16px;
                            font-size: 12px;
                            font-weight: 600;
                            font-family: 'Visby CF', sans-serif;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            align-self: flex-start;

                            &:hover {
                                background: #e02328;
                                transform: translateY(-1px);
                                box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                            }

                            &:active {
                                transform: translateY(0);
                            }

                            &.selected {
                                background: #2e7d32;

                                &:hover {
                                    background: #1b5e20;
                                }
                            }
                        }
                    }
                }
            }
        }

        .see-more-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding: 0 20px 20px;

            .see-more-btn {
                background: #FC353A;
                color: white;
                border: none;
                border-radius: 20px;
                padding: 10px 24px;
                font-size: 14px;
                font-weight: 600;
                font-family: 'Visby CF', sans-serif;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 100px;

                &:hover {
                    background: #e02328;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }

        .reservation-details-view {
            .selected-reservation-card {
                padding: 20px 20px 0 20px;
                margin-bottom: 0;

                .reservation-card {
                    margin-bottom: 0;

                    .reservation-content {
                        margin-bottom: 0;

                        .reservation-details {
                            .reservation-header {
                                margin-bottom: 6px;

                                h5 {
                                    font-size: 16px;
                                    font-weight: 600;
                                    color: #333;
                                    margin: 0;
                                }
                            }

                            .reservation-meta {
                                margin-bottom: 6px;
                                gap: 8px;

                                .reservation-name,
                                .reservation-time {
                                    font-size: 13px;
                                    color: #666;
                                    font-weight: 400;
                                }
                            }

                            .reservation-date-row {
                                margin-bottom: 12px;
                                font-size: 13px;

                                .reservation-date,
                                .reservation-guests {
                                    color: #666;
                                    font-weight: 400;
                                }
                            }
                        }
                    }
                }
            }

            .bot-message-container {
                padding: 20px;
                margin-bottom: 0;

                .message-item {
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    margin-bottom: 0;

                    .message-avatar {
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background: #FC353A;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;

                        .avatar-img {
                            width: 20px;
                            height: 20px;
                            border-radius: 50%;
                        }
                    }

                    .message-content {
                        flex: 1;

                        .message-bubble {
                            background: #f5f5f5;
                            border-radius: 18px;
                            padding: 12px 16px;
                            max-width: 280px;

                            p {
                                margin: 0;
                                font-size: 14px;
                                color: #333;
                                line-height: 1.4;
                            }
                        }
                    }
                }
            }
        }

        .reservation-actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 20px;
            margin-top: 0;

            .reservation-action-btn {
                background: white;
                color: #FC353A;
                border: 2px solid #FC353A;
                border-radius: 25px;
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Visby CF', sans-serif;
                cursor: pointer;
                transition: all 0.2s ease;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background: #FC353A;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }

                // Make some buttons span full width on smaller screens
                &:nth-child(5), &:nth-child(6) {
                    @media (max-width: 380px) {
                        grid-column: 1 / -1;
                    }
                }
            }

            // Responsive adjustments
            @media (max-width: 320px) {
                grid-template-columns: 1fr;

                .reservation-action-btn {
                    grid-column: 1;
                }
            }
        }

        .action-result-content {
            .result-status {
                background: white;
                border-radius: 12px;
                padding: 20px;
                text-align: center;
                margin-bottom: 20px;
                border: 1px solid #e0e0e0;

                i {
                    font-size: 32px;
                    color: #4caf50;
                    margin-bottom: 12px;
                }

                p {
                    margin: 0;
                    font-size: 14px;
                    color: #666;
                    line-height: 1.4;
                }
            }

            .result-actions {
                display: flex;
                gap: 12px;

                .action-btn {
                    flex: 1;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border: none;
                    font-family: 'Visby CF', sans-serif;

                    &.primary {
                        background: #FC353A;
                        color: white;

                        &:hover {
                            background: #e02328;
                            transform: translateY(-1px);
                        }
                    }

                    &.secondary {
                        background: white;
                        color: #666;
                        border: 1px solid #e0e0e0;

                        &:hover {
                            background: #f5f5f5;
                            transform: translateY(-1px);
                        }
                    }
                }
            }
        }
    }
}

.chat-input {
    border-top: 1px solid #e8e9ea;
    padding: 16px 20px;
    background: white;
    border-radius: 0 0 20px 20px;
    transition: all 0.3s ease;

    &.minimized {
        height: 0;
        padding: 0;
        opacity: 0;
        overflow: hidden;
    }

    .input-container {
        display: flex;
        gap: 12px;
        align-items: center;
        background: #f8f9fa;
        border-radius: 25px;
        padding: 8px 8px 8px 16px;
        border: 1px solid #e8e9ea;

        .attach-btn {
            background: none;
            border: none;
            color: #666;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: rgba(252, 53, 58, 0.1);
                color: #FC353A;
            }

            i {
                font-size: 16px;
            }
        }

        .message-input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 8px 0;
            font-size: 15px;
            outline: none;
            font-family: 'Visby CF', sans-serif;
            color: #333;

            &::placeholder {
                color: #999;
            }
        }

        .send-btn {
            background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: white;
            flex-shrink: 0;

            &:hover:not(:disabled) {
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(252, 53, 58, 0.3);
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                background: #ccc;
            }

            i {
                font-size: 14px;
            }
        }
    }
}

.chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FC353A 0%, #e02328 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(252, 53, 58, 0.3);
    z-index: 9999;
    transform: scale(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.show {
        transform: scale(1);
    }

    &:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(252, 53, 58, 0.4);
    }

    .chat-toggle-content {
        position: relative;

        img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #FF4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            border: 2px solid white;
            font-family: 'Visby CF', sans-serif;
        }
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Responsive Design for Chat */
@media screen and (max-width: 768px) {
    .chat-overlay.active {
        background-color: rgba(0, 0, 0, 0.8);
    }

    .chat-container {
        bottom: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100%;
        max-width: 100%;
        max-height: 100%;
        border-radius: 0;
        box-shadow: none;

        &.active {
            transform: translateY(0) scale(1);
        }
    }

    .chat-header {
        border-radius: 0;
        padding: 16px 20px;
        min-height: 70px;

        .header-left {
            .header-title h4 {
                font-size: 20px;
            }
        }
    }

    .chat-body {
        .welcome-section {
            padding: 60px 20px 40px;

            .illustration-container .support-character {
                .character-illustration {
                    width: 250px;
                    height: 140px;
                }
            }
        }

        .chat-messages {
            padding: 0 16px 20px;

            .message-item {
                &.sent {
                    margin-left: 40px;
                }

                &.received {
                    margin-right: 40px;
                }
            }
        }

        .quick-actions {
            padding: 20px 16px;

            .quick-action-buttons {
                gap: 12px;

                .quick-action-btn {
                    padding: 6px 13px;
                    font-size: 12px;
                }
            }
        }
    }

    .chat-input {
        padding: 20px 16px;
        border-radius: 0;

        .input-container {
            padding: 12px 12px 12px 20px;

            .message-input {
                font-size: 16px; /* Prevents zoom on iOS */
                padding: 10px 0;
            }

            .send-btn {
                width: 40px;
                height: 40px;
            }
        }
    }

    .chat-toggle {
        bottom: 16px;
        right: 16px;
        width: 56px;
        height: 56px;

        .chat-toggle-content img {
            width: 28px;
            height: 28px;
        }
    }
}

@media screen and (max-width: 480px) {
    .chat-body {
        .welcome-section {
            padding: 40px 16px 30px;

            .illustration-container .support-character {
                .character-illustration {
                    width: 200px;
                    height: 120px;
                }
            }
        }

        .chat-messages {
            padding: 0 12px 16px;

            .message-item {
                &.sent {
                    margin-left: 30px;
                }

                &.received {
                    margin-right: 30px;
                }

                .message-content {
                    font-size: 14px;
                }
            }
        }

        .quick-actions {
            padding: 16px 12px;

            .quick-action-buttons {
                .quick-action-btn {
                    padding: 6px 12px;
                    font-size: 12px;
                }
            }
        }
    }

    .chat-input {
        padding: 16px 12px;

        .input-container {
            padding: 10px 10px 10px 16px;

            .send-btn {
                width: 36px;
                height: 36px;
            }
        }
    }
}


