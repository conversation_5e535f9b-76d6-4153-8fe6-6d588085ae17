<div class="image-upload" [ngStyle]="{'height':getHeight(), 'width':getWidth()}">
  <ng-container *ngIf="!image && !imageUrl && !placeholder">
    <svg class="placeholder-icon" nz-icon [nzType]="icon"></svg>
  </ng-container>

  <img *ngIf="image || imageUrl" [src]="image || imageUrl" class="image" onerror="this.src='../assets/favicon.png';" />
  <img *ngIf="placeholder && !image && !imageUrl" [src]="placeholder" class="image"
    onerror="this.src='../assets/favicon.png';" [ngStyle]="{'object-fit':'contain'}" />

  <div class="overlay">
    <nz-upload [nzBeforeUpload]="beforeUpload" [nzAccept]="'image/*'">
      <svg class="upload-icon" nz-icon [nzType]="'edit'"></svg>
    </nz-upload>
    <svg class="delete-icon" *ngIf="imageFile" (click)="deleteImage()" nz-icon [nzType]="'close-square'"></svg>
  </div>
</div>