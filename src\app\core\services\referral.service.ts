import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from '../../shared/error-handler';
import { Referral } from '../models/referral';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ReferralService {
  private url = environment.apiBaseUrl + 'referrals/';
  public carts: Referral[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Referral> {
    return this.http.get<Referral>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Referral>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  getReferralPending(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}referral-pending`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }
}
