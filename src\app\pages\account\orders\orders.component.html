<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="dont-have-order text-center" *ngIf="orders.length <= 0 && !isLoading">
  <p>You don't have any previous orders.</p>
  <span>Make your first order by clicking the button below.</span>
  <br>
  <button class="btn" (click)="redirectOnRest()">Order Now</button>
</div>

<div *ngIf="orders.length > 0 && !isLoading">
  <div class="order-history-box" *ngFor=" let order of orders;let i = index;">
    <div class="order-image">
      <div class="order-logo">
        <img [src]="order?.restaurant?.image_url" [alt]="order?.restaurant?.restaurant_name"
          onerror="this.src='./assets/favicon.png';">
      </div>
      <img class="order-main-image" [src]="order?.restaurant?.promotions[0]?.image_url"
        [alt]="order?.restaurant?.restaurant_name" onerror="this.src='./assets/images/product-main-image.png';">
      <button *ngIf="order.status == 'Delivered' && (!order.reviews)" (click)="addReview(reviewModal,order)"
        class="btn review-btn">
        Review
      </button>
    </div>
    <div class="order-details">
      <div class="order-title">
        <h6>{{ order?.restaurant?.restaurant_name }}</h6>
        <ul class="order-date-time">
          <li>{{order.delivery_date}}</li>
          <li>{{order.delivery_time}}</li>
          <li>{{ order?.cart_view?.length }} Items For Total {{ convertNumber(order.order_grand_total) }}</li>
          <li><button class="cursor" (click)="orderView(order.id)">View Receipt</button></li>
        </ul>
      </div>
      <div class="reservation-id">
        <p><strong>Order ID</strong> : #{{ order.order_number }}</p>
      </div>
      <div class="order-addon-list">
        <ul *ngIf="order?.cart_view?.length > 0">
          <li *ngFor="let cart of order?.cart_view; let i=index;">
            <div class="order-count">{{cart.quantity}}</div>
            <span class="multiple-symbol">x</span>
            <div class="addon-order-detail">
              <p>{{cart.menu_name}}</p>
              <ul>
                <li>{{cart.subaddons_name}}</li>
              </ul>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="view-store">
      <ul>
        <li>
          <button class="btn" (click)="orderStore(order)">View Store</button>
        </li>
        <!-- <li *ngIf="order.status == 'Delivered' && (!order.reviews)">
          <a class="btn" (click)="addReview(reviewModal,order)">
            <img class="white-review" src="assets/images/white-review.png" alt="Review">
            <img class="red-review" src="assets/images/red-review.png" alt="Review">
            Review
          </a>
        </li> -->
        <li *ngIf="order.status == 'Delivered'">
          <button class="btn" (click)="reOrderCreate(order)" [disabled]="isReOrderLoading && reOrder.id == order.id">
            <i class="spinner-border" *ngIf="isReOrderLoading && reOrder.id == order.id"></i>
            <img class="white-reorder" *ngIf="!isReOrderLoading && reOrder.id != order.id"
              src="assets/images/white-reorder.png" alt="Go-Grubz-re-order-image">
            <img class="red-reorder" *ngIf="!isReOrderLoading && reOrder.id != order.id"
              src="assets/images/red-reorder.png" alt="Go-Grubz-re-order-image">
            Re-Order
          </button>
        </li>
        <li *ngIf="order.status == 'Delivered'">
          <button class="btn raise-issue-btn" [routerLink]="'/'+order?.id+'/complaint'">
            Raise Issue
          </button>
        </li>
      </ul>
    </div>
    <div #target *ngIf="i+1 == orders.length - 1"></div>
  </div>

  <div class="grubz-loader" *ngIf="isScrollLoading">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>

</div>


<ng-template #reviewModal let-modal>
  <div id="add-review-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body">
      <h5 class="login-title">Review</h5>
      <form>
        <div class="row">
          <form nz-form #reviewForm="ngForm" (ngSubmit)="validateReview(reviewForm)">

            <div class="col-md-12">
              <!--Star-Ratings-->
              <div class="star-ratings">
                <input type="radio" id="star5" name="rating" [(ngModel)]="reviewAdd.rating" [value]="5" />
                <label for="star5"></label>

                <input type="radio" id="star4" name="rating" [(ngModel)]="reviewAdd.rating" [value]="4" />
                <label for="star4"></label>

                <input type="radio" id="star3" name="rating" [(ngModel)]="reviewAdd.rating" [value]="3" />
                <label for="star3"></label>

                <input type="radio" id="star2" name="rating" [(ngModel)]="reviewAdd.rating" [value]="2" />
                <label for="star2"></label>

                <input type="radio" id="star1" name="rating" [(ngModel)]="reviewAdd.rating" [value]="1" />
                <label for="star1"></label>
              </div>

              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter message!">
                    <nz-input-group>
                      <textarea nz-input [(ngModel)]="reviewAdd.message" name="message" id="message"
                        class="form-control reviewMessage" placeholder="Enter your message here...."
                        required=""></textarea>
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>

            </div>

            <div class="col-md-12">
              <button class="btn modal-black-btn" nz-button [disabled]="isModelLoading">
                <i class="spinner-border" *ngIf="isModelLoading"></i>
                Submit
              </button>
            </div>

          </form>
        </div>
      </form>
    </div>
  </div>
</ng-template>