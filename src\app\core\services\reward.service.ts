import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from '../../shared/error-handler';
import { Reward } from '../models/reward';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class RewardService {
  private url = environment.apiBaseUrl + 'reward/';
  public carts: Reward[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Reward> {
    return this.http.get<Reward>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(reward: Reward): Observable<any> {
    return this.http.post<Reward>(this.url, Reward.toFormData(reward))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(reward: Reward): Observable<any> {
    return this.http.post<Reward>(this.url + reward.id, Reward.toFormData(reward))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Reward>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  rewardMain(): Observable<any> {
    return this.http.get<any>(environment.apiBaseUrl + 'rewards/')
      .pipe(catchError(ErrorHandler.handleError));
  }
}
