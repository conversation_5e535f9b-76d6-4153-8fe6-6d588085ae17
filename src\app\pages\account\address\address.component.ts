import { <PERSON><PERSON><PERSON>cy<PERSON>ipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { User } from '../../../core/models/user';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { AddressBook } from '../../../core/models/address-book';
import { AddressBookService } from '../../../core/services/address-book.service';
import { NotificationService } from '../../../core/services/notification.service';
import { NgForm } from '@angular/forms';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-address',
  host: { ngSkipHydration: 'true' },
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
})
export class AddressComponent implements OnInit, OnDestroy {
  subs = new Subscription();

  user: User;
  addressBooks: AddressBook[] = [];
  addressBook: AddressBook = new AddressBook();
  addressBookAdd: AddressBook = new AddressBook();;
  modalOptions: NgbModalOptions;
  previousPage: any;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isPostcodeLoading = false; Postcodeerror = null;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  constructor(
    public userService: UserService,
    private addressBookService: AddressBookService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user.id) {
      this.router.navigateByUrl('/auth');
    }
    this.fetchAddresses();
  }

  fetchAddresses() {
    this.isLoading = true;

    this.subs.add(
      this.addressBookService.get({ customer_id: this.user.id, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.addressBooks = res;
          },
          (err) => {
            this.addressBooks = [];
          }
        )
    );
  }

  onAddressSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    if (this.addressBookAdd.id != null) this.updateAddress(form);
    else this.addAddress(form);
  }

  addAddress(form: NgForm) {
    this.isModelLoading = true; this.Modelerror = false;

    this.addressBookAdd.user_id = this.user.id;
    this.subs.add(
      this.addressBookService.create(this.addressBookAdd).
        pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.fetchAddresses();
            this.notificationService.showSuccess("Address addded successfully !!", "Gogrubz")
            this.modalService.dismissAll();
            this.addressBookAdd = new AddressBook();
          },
          (err) => {
            this.Modelerror = err;
          }
        )
    )
  }

  updateAddress(form: NgForm) {
    this.isModelLoading = true; this.error = null;

    this.subs.add(this.addressBookService.update(this.addressBookAdd)
      .pipe(finalize(() => this.isModelLoading = false))
      .subscribe(res => {
        this.fetchAddresses();
        this.notificationService.showSuccess("Address updated successfully !!", "Gogrubz");
        this.modalService.dismissAll();
      }, err => this.Modelerror = err)
    );
  }

  deleteAddress(addressBook: AddressBook) {
    this.isLoading = true; this.error = null;

    this.subs.add(this.addressBookService.delete(addressBook.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        let addressBook = this.userService.getAddress();
        if (addressBook) {
          if (addressBook.id) {
            if (typeof localStorage !== 'undefined') {
              localStorage.removeItem(environment.address);
            }
          }
        }

        this.fetchAddresses();
        this.notificationService.showSuccess("Address deleted successfully !!", "Gogrubz")
      }, err => { this.error = err; })
    );
  }

  findzipcode(postcode: string) {
    this.isPostcodeLoading = true; this.Postcodeerror = null;

    if (postcode == null || postcode == undefined) {
      this.Postcodeerror = 'Please enter valid postcode and press lookup button';
      this.isPostcodeLoading = false;
    } else {
      this.subs.add(this.userService.postcode(postcode)
        .pipe(finalize(() => this.isPostcodeLoading = false))
        .subscribe(res => {
          var address = '';
          if (res.street) {
            address += res.street;
          }
          if (res.post_town) {
            address += ',' + res.post_town;
          }
          if (res.post_code) {
            address += ',' + res.post_code;
          }

          this.addressBookAdd.latitude = res.latitude;
          this.addressBookAdd.longitude = res.longitude;
          this.addressBookAdd.zipcode = res.post_code;
          this.addressBookAdd.address = address;
        }, err => this.Postcodeerror = err)
      );
    }
  }

  openModal(model, item) {
    this.Modelerror = null;

    if (item == null) {
      this.addressBookAdd = new AddressBook();
    } else {
      this.addressBookAdd = Object.assign({}, item);
    }

    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.modalService.dismissAll();
  }
}
