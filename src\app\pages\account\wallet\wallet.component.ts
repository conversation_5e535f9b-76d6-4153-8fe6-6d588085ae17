import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { environment } from '../../../../environments/environment';
import { NotificationService } from '../../../core/services/notification.service';
declare var Stripe;
import { User } from '../../../core/models/user';
import { WalletHistory } from '../../../core/models/wallet-history';
import { WalletHistoryService } from '../../../core/services/wallet-history.service';
import { StripeCustomer } from '../../../core/models/stripe-customer';
import { StripeCustomerService } from '../../../core/services/stripe-customer.service';

@Component({
  selector: 'app-wallet',
  host: { ngSkipHydration: 'true' },
  templateUrl: './wallet.component.html',
  styleUrls: ['./wallet.component.scss'],
})
export class WalletComponent implements OnInit, OnDestroy {

  subs = new Subscription();

  user: User;
  WalletHistories: WalletHistory[] = [];
  walletHistory: WalletHistory = new WalletHistory();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  modalOptions: NgbModalOptions;
  previousPage: any;

  totalWalletHistories = 0;
  addAmount: number = 0;
  cardId: string;
  last_page = 0;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isModelPaymentLoading = false; ModelPaymenterror = null;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    public userService: UserService,
    private walletHistoryService: WalletHistoryService,
    private stripeCustomerService: StripeCustomerService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    // this.fetchRestaurant();
  }

  // fetchRestaurant() {
  //   this.isLoading = true;

  //   this.subs.add(this.restaurantService.show(this.restaurant_id)
  //     .pipe(finalize(() => this.isLoading = false))
  //     .subscribe(res => {
  //       this.restaurant = res;
  //       if (this.restaurant?.site_setting?.stripe_mode == 'Test') {
  //         var publishKey = this.restaurant?.site_setting?.stripe_publishkeyTest
  //       } else {
  //         var publishKey = this.restaurant?.site_setting?.stripe_publishkey
  //       }
  //       this.stripe = Stripe(publishKey);
  //     }, err => this.error = err)
  //   );
  // }

  fetchWallletHistory() {
    this.isLoading = true; this.Modelerror = null;

    this.subs.add(this.walletHistoryService.get(this.options)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.WalletHistories = res.data;
        this.totalWalletHistories = res.total;
        this.last_page = res.last_page;
      }, err => { this.WalletHistories = []; this.totalWalletHistories = 0; this.Modelerror = err })
    );
  }

  fetchCards() {
    this.subs.add(
      this.stripeCustomerService
        .get({ service_type: 'normal', customer_id: this.user.id, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
          },
          (err) => {
            this.stripeCustomers = [];
          }
        )
    );
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.fetchWallletHistory();
    }
  }

  walletView(model) {
    this.fetchWallletHistory();
    this.openModal(model);
  }

  loadWalllet(model) {
    this.fetchCards();
    this.openModal(model);
  }

  addMoney(amount) {
    this.addAmount = amount
  }

  loadMoney() {
    this.isModelPaymentLoading = true;
    this.ModelPaymenterror = null;

    if (!this.addAmount || this.addAmount <= 0) {
      this.ModelPaymenterror = 'Please enter amount';
      this.isModelPaymentLoading = false;
      return;
    }

    if (!this.cardId) {
      this.ModelPaymenterror = 'Please select card';
      this.isModelPaymentLoading = false;
      return;
    }

    if (!this.ModelPaymenterror) {
      this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.cardId);
      this.createPaymentIntent(this.stripeCustomer);
    }

  }

  async createPaymentIntent(stripeCustomerNew) {
    this.stripeCustomer = stripeCustomerNew;
    this.stripeCustomer.amount = this.addAmount;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.ModelPaymenterror = err;
            this.isModelPaymentLoading = false;
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isModelPaymentLoading = false;
        this.ModelPaymenterror = 'Sorry your Payment Faild! Please try again';
      } else {
        if (result.paymentIntent.status === 'succeeded') {
          this.walletHistory.customer_id = this.user.id;
          this.walletHistory.purpose = 'Money added in wallet';
          this.walletHistory.transaction_type = 'Credited';
          this.walletHistory.amount = this.addAmount;
          this.subs.add(this.walletHistoryService.create(this.walletHistory)
            .pipe(finalize(() => this.isModelPaymentLoading = false))
            .subscribe(res => {
              this.fetchMe();
            }, err => this.ModelPaymenterror = err)
          );
        }
      }
    });
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelPaymentLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
        this.notificationService.showSuccess("Money added into wallet successfully!!", "Gogrubz")
        this.modalService.dismissAll();
      }, err => this.ModelPaymenterror = err)
    );
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
