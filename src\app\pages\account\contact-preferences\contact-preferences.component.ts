import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { NotificationService } from '../../../core/services/notification.service';
import { User } from '../../../core/models/user';
import { UserService } from '../../../core/services/user.service';

@Component({
  selector: 'app-contact-preferences',
  host: { ngSkipHydration: 'true' },
  templateUrl: './contact-preferences.component.html',
  styleUrls: ['./contact-preferences.component.scss'],
})
export class ContactPreferenceComponent implements OnInit, OnDestroy {

  subs = new Subscription();

  user: User;

  isLoading = false; error = null;

  options = { query: null, page: 1, per_page: 10 };

  constructor(
    public userService: UserService,
    private router: Router,
    // public activeModal: NgbActiveModal,
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
