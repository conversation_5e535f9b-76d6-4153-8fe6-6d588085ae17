$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.dropdown-toggle:after {
  display: none;
}

.grubz-loader.main-loader {
  margin: 0 !important;
  z-index: 999;
}

.popular-cuisines-section {
  padding-bottom: 40px;
}

.popular-cuisines-section .main-heading {
  margin-bottom: 20px;
}

.popular-cuisines-section .main-heading h5 {
  color: $primary;
  font-family: 'Fredoka';
  font-weight: 600;
  margin-bottom: 0;
}

.product-box .product-image .favourite-icon {
  position: absolute;
  z-index: 1;
  top: 15px;
  right: 22px;
}

.product-box .product-image .favourite-icon button {
  padding: 0;
  border: 0;
  border-radius: 0;
  background-color: transparent;
}

.product-box .product-image .favourite-icon button img {
  width: 24px;
  min-height: initial;
  max-height: initial;
  border-radius: 0;
  object-fit: initial;
}

.popular-slider-item {
  padding-top: 40px;
  padding-bottom: 15px;
}

.popular-item {
  position: relative;
  width: 146px;
  height: 110px;
  text-align: center;
}

.popular-item .popular-item-bg-one {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 146px;
  height: 110px;
  content: '';
  border-radius: 15px;
}

.popular-item .popular-item-bg-two {
  position: absolute;
  top: 7px;
  left: 7px;
  width: 146px;
  height: 110px;
  position: absolute;
  content: '';
  border-radius: 15px;
}

.popular-item.chicken-dishes::after {
  background: #FCAC35;
}

.popular-item.chicken-dishes::before {
  background: #CB8D2F;
}

.popular-item.pizza-dishes::after {
  background: #A1CBE2;
}

.popular-item.pizza-dishes::before {
  background: #78A5BD;
}

.popular-item.chinese-dishes::after {
  background: #654762;
}

.popular-item.chinese-dishes::before {
  background: #3E2A3C;
}

.popular-item.indian-dishes::after {
  background: $primary;
}

.popular-item.indian-dishes::before {
  background: #A72F14;
}

.popular-item .popular-item-name {
  position: relative;
  z-index: 1;
}

.popular-item .popular-item-name img {
  display: inline-block;
  margin-top: -25px;
  margin-bottom: 15px;
  max-width: 120px;
  min-height: 90px;
  max-height: 90px;
  object-fit: cover;
  border-radius: 20px;
  transition: all 0.5s;
}

.popular-item:hover .popular-item-name img {
  transform: rotate(20deg);
}

.popular-item .popular-item-name p {
  color: #fff;
  font-family: 'Fredoka';
  font-size: 18px;
  font-weight: normal;
  line-height: 16px;
  text-align: center;
  margin: 0;
}
.popular-item .popular-item-name p img{
  width: 20px;
  min-height: initial;
  max-height: initial;
  margin: -4px 0 0 2px;
  transform: initial;
}
.filter-section-bg{
  padding-bottom: 30px;  
  margin: 0 -40px;
  position: sticky;
  top:0;
  z-index:12;
}
.filter-section {
  padding-top: 15px;
  padding-bottom: 15px;  
  background-color: #fff;  
}

.filter-list {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
}

.filter-list li {
  margin-right: 10px;
}

.filter-list li:last-child {
  margin-right: 0;
}

.filter-list li button {
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  cursor: pointer;
  padding: 12px 18px;
  border-radius: 35px;
  line-height: 20px;
  text-decoration: none;
  display: inline-block;
  background-color: #F9F9F9;
  border: 1px solid #F4F3F3;
}

.filter-list li button img {
  width: 23px;
  margin-right: 6px;
  margin-top: -4px;
}

.filter-list li button.active {
  color: #fff !important;
  background-color: #000;
}

.filter-list li button svg.fa-sterling-sign {
  font-size: 23px;
  margin-right: 6px;
}

.filter-list li button img.white-icon {
  display: none;
}

.filter-list li button.active img.white-icon {
  display: inline-block;
}

.filter-list li button.active img.black-icon {
  display: none;
}

.filter-list li button svg.fa-chevron-down {
  font-size: 19px;
  margin-left: 6px;
  transition: all 0.5s;
}
.filter-list li.show button svg.fa-chevron-down{
  transform:rotate(180deg);
}
.filter-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
}

.filter-box p {
  font-family: 'Visby CF';
  font-size: 25px;
  font-weight: 700;
  line-height: 29px;
  text-align: right;
  margin-bottom: 0;
}

.filter-box button.filter-btn {
  padding: 0;
  margin-left: 50px;
  border: 0;
  outline: none;
  box-shadow: none;
  background-color: transparent;
}

.filter-box .dropdown-menu {
  width: 290px;
  border: 0;
  padding: 5px 0;
  border-radius: 15px;
  margin-top: 75px !important;
  left: auto !important;
  right: 0 !important;
  transform: inherit !important;
  box-shadow: 0px 0px 50px 0px #00000026;
}

.filter-box .dropdown-menu:before {
  position: absolute;
  top: -16px;
  right: 12px;
  content: '';
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 16px solid #fff;
}

.filter-box .dropdown-menu ul {
  margin-bottom: 0;
}

.filter-box .dropdown-menu ul li {
  font-size: 18px;
  color: #000;
  padding: 11px 28px;
  font-weight: bold;
  margin: 0;
  cursor: pointer;
  border-radius: 5px;
  background-color: transparent;
}

.filter-box .dropdown-menu ul li .form-check {
  position: relative;
  padding-left: 40px;
}
.filter-box .dropdown-menu ul li .form-check label.form-check-label{
  width: 100%;
}
.filter-box .dropdown-menu ul li .form-check input.form-check-input {
  border: 2px solid #8F8F8A;
  position: absolute;
  left: 0;
  margin-top: 7px;
  margin-left: 0;
}

.filter-list .filter-box button.filter-btn {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  margin-left: 0;
}

.filter-list .filter-box button.filter-btn img {
  width: 34px;
  padding: 2px;
  margin-left: 5px;
}
.filter-mobile-view{
  display: none;
}
.dietary-dropdown {
  position: relative;
}

.dietary-dropdown .dropdown-menu {
  width: 242px;
  background-color: #fff;
  border-radius: 15px;
  border: 0;
  padding: 15px 0;
  box-shadow: 0px 0px 50px 0px #00000026;
  margin-top: 15px !important;
  right: 0 !important;
  left: auto !important;
}

.dietary-dropdown .dropdown-menu::before {
  position: absolute;
  top: -16px;
  right: 18px;
  content: '';
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 16px solid #fff;
}

.dietary-dropdown .dropdown-menu ul li {
  margin: 0;
  padding: 0;
  background-color: transparent;
}

.dietary-dropdown .dropdown-menu ul li button {
  font-size: 18px;  
  font-weight: 700;
  color: #000000;
  line-height: 30px;
  width: 100%;
  border: 0;
  border-radius: 0;
  padding: 12px 30px;
  background-color: transparent;
  position: relative;
  display: flex;
  justify-content: space-between;
}
.dietary-dropdown .dropdown-menu ul li button span{
  float: right;
}
.dietary-dropdown .dropdown-menu ul li button img {
  display: none;
  margin: 2px 0 0 10px;
  float: right;
}

.dietary-dropdown .dropdown-menu ul li.active button img {
  display: inline-block;
}

.filter-section-bg.fixed .filter-section {
  box-shadow: 0 2px 3px 0 #e1e1e1;
}

.product-list-heading {
  margin-bottom: 36px;
}

.product-list-heading h5 {
  font-size: 30px;
  font-weight: 800;
  font-family: 'Visby CF';
}

.most-popular-section {
  padding-bottom: 40px;
}

.offer-for-you-slider,
.most-popular-slider {
  margin-bottom: 30px;
  border-bottom: 2px solid #E2DFDF;
}

.most-popular-item {
  padding: 0 12px;
}

.offer-for-you-slider .slick-list,
.most-popular-slider .slick-list {
  margin: 0 -12px;
}

.offer-for-you-slider button.slick-arrow,
.most-popular-slider button.slick-arrow {
  background-color: #F4F3F3;
  box-shadow: none;
  top: -70px;
}

.offer-for-you-slider button.slick-arrow.slick-prev,
.most-popular-slider button.slick-arrow.slick-prev {
  margin-right: 60px;
}

.offer-for-you-slider button.slick-arrow:hover,
.most-popular-slider button.slick-arrow:hover {
  background-color: #D3D1D1;
}

.discount-offer {
  font-size: 15px;
  font-weight: 800;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.discount-offer img {
  margin-top: -2px;
  margin-right: 10px;
  display: inline-block;
}

#FilterModal .modal-dialog {
  max-width: 1000px;
}

.modal-content .modal-header {
  padding: 10px 44px;
  border-bottom: 2px solid #F4F3F3;
}

.modal-content .modal-header h5.modal-title {
  font-size: 40px;
  color: #000;
  font-family: 'Visby CF';
  font-weight: 800;
}

.modal-header .btn-close {
  padding: 0;
  margin: 0;
  width: 30px;
  height: 30px;
  opacity: 1;
  border-radius: 50%;
  text-align: center;
  background-color: #F4F3F3;
  background-image: none;
}

.modal-dialog .modal-content {
  border: 0;
  border-radius: 20px;
}

.modal-dialog .modal-content button.btn-close {
  font-size: 20px;
  padding: 0;
  color: #000000;
  background-image: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  background-color: #F4F3F3;
  opacity: 1;
  position: absolute;
  top: 25px;
  left: 25px;
  z-index: 12;
  box-shadow: none;
}

#FilterModal .modal-dialog .modal-content button.btn-close {
  left: auto;
  right: 43px;
}

#FilterModal .modal-content .modal-body {
  display: flex;
  padding: 0 45px;
}

.filter-popup-list {
  padding: 15px 20px 30px 0;
  min-width: 238px;
  max-width: 238px;
  padding-right: 20px;
  border-right: 2px solid #F4F3F3;
}

.filter-popup-list ul {
  border: 0;
  border-radius: 0;
}

.filter-popup-list ul li {
  width: 100%;
  padding-bottom: 30px;
}

.filter-popup-list ul li:last-child {
  padding-bottom: 0;
}

.filter-popup-list ul li a {
  font-size: 20px;
  color: #000;
  width:100%;
  font-weight: 700;
  line-height: 40px;
  display: inline-block;
  text-decoration: none;
  position: relative;
  padding-left: 25px;
}
.filter-popup-list ul li a.active{
  color:#fc353a;
}
.filter-popup-list ul li a::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  content: '';
  width: 7px;
  height: 40px;
  background-color: #fc353a;
  border-radius: 0 10px 10px 0;
  display: none;
}

.filter-popup-list ul li a:hover::before,
.filter-popup-list ul li a.active::before {
  display: inline-block;
}

.modal-content .modal-body .tab-content {
  position: relative;
  width: 100%;
  padding-bottom: 80px;
}

.filter-content {
  width: 100%;
  padding: 25px 25px 0 25px;
}

.filter-category-list {
  margin-bottom: 55px;
}

.filter-category-list ul li {
  font-size: 20px;
  color: #000;
  font-weight: bold;
  padding-bottom: 38px;
}

.filter-category-list ul li .form-check {
  position: relative;
  padding-left: 60px;
}
.filter-category-list ul li .form-check label.form-check-label{
  width: 100%;
}
.filter-category-list ul li .form-check input.form-check-input {
  width: 25px;
  height: 25px;
  border: 2px solid #8F8F8A;
  position: absolute;
  left: 0;
  margin-top: 5px;
  margin-left: 0;
}

.modal-footer {
  padding: 0;
  justify-content: center;
  border: 0;
}

.modal-footer button.btn {
  width: 200px;
  height: 50px;
  font-weight: 700;
  margin: 0 30px;
}

.modal-footer button.btn.btn-secondary {
  color: #EA3323 !important;
  border: 2px solid #EA3323 !important;
  background-color: transparent !important;
}

.section-seprate-line {
  width: 100%;
  height: 2px;
  background-color: #E2DFDF;
  margin-top: 65px;
  margin-bottom: 50px;
}

.restaurant-not-open-status-bg {
  padding: 20px 80px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 10px;
  background: rgb(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.restaurant-not-open-status-bg h6 {
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  text-align: center;
  line-height: 20px;
  margin-bottom: 0;
  font-family: 'Visby CF';
}

.restaurant-not-open-status-bg button.btn {
  font-size: 16px;
  color: #000000 !important;
  font-weight: 700;
  padding: 7px 15px;
  width: 160px;
  border-color: #fff !important;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 13px;
  margin: auto;
  background-color: #fff !important;
  transition: all 0.5s;
}
.restaurant-not-open-status-bg button.btn:hover{
  color: #fc353a !important;
  border-color: #fc353a !important;
  background-color: #fff !important;
}
.dietary-dropdown .dropdown-menu .star-icons {
  line-height: 26px;
  padding-left: 10px;
}

.dietary-dropdown .dropdown-menu .star-icons li {
  display: inline-block;
  margin-right: 4px;
}

.dietary-dropdown .dropdown-menu .star-icons li svg {
  font-size: 15px;
  color: #fd811f;
}

.filter-rating-list {
  position: relative;
  display: flex;
  align-items: center;
  padding-right: 30px;
  margin-bottom: 20px;
  cursor: pointer;
}

.filter-rating-list ul.star-icons {
  margin-bottom: 0;
}

.filter-rating-list ul.star-icons li {
  display: inline-block;
  margin-right: 12px;
}

.filter-rating-list ul.star-icons li span {
  font-size: 20px;
  font-weight: 700;
  color: #000;
  position: relative;
  top: 2px;
}

.filter-rating-list ul.star-icons li svg {
  font-size: 16px;
  color: #fd811f;
}

.filter-rating-list img.check-icon {
  position: absolute;
  right: 0;
}

#FilterModal .modal-dialog .modal-footer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20px;
}

.delivery-fee-list ul {
  margin-bottom: 0;
}

.delivery-fee-list ul li button {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  line-height: 30px;
  width: 100%;
  text-decoration: none;
  display: inline-block;
  padding: 10px 0;
  position: relative;
  background-color: transparent;
  border:0;
  text-align: left;
}
.delivery-fee-list ul li:first-child button {
  padding-top: 0;
}
.delivery-fee-list ul li button span{
  float: right;
}
.delivery-fee-list ul li button img.check-icon {
  float: right;
  margin: 3px 0 0 10px;
}
.search-close {
  position: absolute;
  right: 15px;
  top: 18px;
  left: auto;
  font-size: 14px !important;
}
.location-search-box {
  position: absolute;
  left: 0;
  right: 0;
  top: 40px;
  background-color: #fff;
  padding: 7px;
  box-shadow: 0 0 10px 0 #dfdfdf;
  border-radius: 6px;
  width: 100%;
  z-index:200;
}
.location-search-box .search-for-restaurant svg {
  font-size: 14px;
  left:13px;
}

.location-search-box .search-for-restaurant input.form-control {
  font-size: 12px;
  width: 100%;
  height: 34px;
  padding: 0 45px 0 35px;
}
.location-search-box .search-for-restaurant button.btn {
  width: 40px;
  height: 34px;
  padding: 0;
  text-align: center;
  right: 0;
  border-radius: 0 20px 20px 0;
}

.location-search-box .search-for-restaurant button.btn svg {
  left: 8px;
}
.location-search-box-close-bg{
  z-index:150;
}

@media screen and (max-width:1800px) {
.filter-section-bg{
    margin: 0 -30px;
}

}

@media screen and (max-width:1750px) {
  .popular-cuisines-section {
    padding-left: 90px;
    padding-right: 90px;
  }
  .filter-box p {
    font-size: 22px;
  }
  .filter-box button.filter-btn {
    margin-left: 25px;
  }  
  .most-popular-section {
    padding-left: 90px;
    padding-right: 90px;
  }

}

@media screen and (max-width:1500px) {
  .popular-cuisines-section {
    padding: 0 92px 25px 92px;
  }

  .popular-slider-item {
    padding-top: 25px;
  }

  .popular-item {
    width: 100px;
    height: 75px;
  }

  .popular-item .popular-item-bg-one,
  .popular-item .popular-item-bg-two {
    width: 100px;
    height: 75px;
    border-radius: 10px;
  }

  .popular-item .popular-item-bg-two {
    top: 4px;
    left: 4px;
  }

  .popular-item .popular-item-name img {
    max-width: 80px;
    min-height: 60px;
    max-height: 60px;
    border-radius: 10px;
    margin-top: -18px;
    margin-bottom: 10px;
  }

  .popular-item .popular-item-name p {
    font-size: 13px;
    line-height: 11px;
  }
  
  .filter-section-bg{
    margin: 0 -28px;
    padding-bottom: 20px;
  }
  .filter-section{
    padding: 15px 120px;
  }    
  .location-box ul li{
    font-size: 14px;
  }
  .filter-list li button {
    font-size: 14px;
    padding: 9px 14px;
  }

  .filter-list li button img {
    width: 17px;
    margin-right: 5px;
  }

  .filter-list li button svg.fa-sterling-sign {
    font-size: 17px;
    margin-right: 5px;
    position: relative;
    top: 2px;
  }

  .filter-list li button svg.fa-chevron-down {
    font-size: 15px;
    margin-left: 5px;
  }

  .product-list-heading {
    margin-bottom: 25px;
  }

  .product-list-heading h5 {
    font-size: 22px;
  }

  .most-popular-section {
    padding-left: 92px;
    padding-right: 92px;
  }

  #FilterModal .modal-dialog {
    max-width: 750px;
  }

  .modal-content .modal-header {
    position: relative;
    padding: 7px 35px;
  }

  .modal-dialog .modal-content {
    border-radius: 15px;
  }

  .modal-content .modal-body .tab-content {
    padding-bottom: 65px;
  }

  .modal-content .modal-header h5.modal-title {
    font-size: 30px;
  }

  #FilterModal .modal-dialog .modal-content button.btn-close {
    font-size: 14px;
    width: 22px;
    height: 22px;
    top: 50%;
    right: 35px;
    transform: translate(0, -50%);
  }

  #FilterModal .modal-content .modal-body {
    padding: 0 35px;
  }

  .filter-popup-list {
    padding: 10px 10px 20px 0;
  }

  .filter-popup-list ul li {
    padding-bottom: 24px;
  }

  .filter-popup-list ul li a {
    font-size: 15px;
    line-height: 30px;
    padding-left: 20px;
  }

  .filter-popup-list ul li a:before {
    width: 5px;
    height: 30px;
  }

  .filter-content {
    padding: 19px 19px 0 19px;
  }  

  .filter-category-list {
    margin-bottom: 0px;
  }
  
  .filter-category-list ul li {
    font-size: 15px;
    padding-bottom: 26px;
  }

  .filter-category-list ul li .form-check {
    padding-left: 45px;
  }

  .filter-category-list ul li .form-check input.form-check-input {
    width: 18px;
    height: 18px;
    margin-top: 3px;
  }

  .modal-footer button.btn {
    padding: 5px 15px;
    width: 150px;
    height: 37px;
    margin: 0 22px;
  }

  .restaurant-not-open-status-bg h6 {
    font-size: 15px;
  }

  .restaurant-not-open-status-bg a.btn {
    font-size: 12px;
    padding: 2px 15px;
    width: 130px;
  }

  .section-seprate-line {
    margin-top: 45px;
    margin-bottom: 40px;
  }

  .filter-box .dropdown-menu {
    width: 218px;
    margin-top: 60px !important;
    border-radius: 12px;
    padding: 10px 0;
  }

  .filter-box .dropdown-menu ul li {
    font-size: 15px;
    padding: 9px 20px;
  }

  .dietary-dropdown .dropdown-menu {
    width: 168px;
    border-radius: 12px;
    padding: 10px 0;
  }

  .dietary-dropdown .dropdown-menu ul li button {
    font-size: 14px;
    line-height: 18px;
    padding: 10px 20px;
  }

  .dietary-dropdown .dropdown-menu ul li button img {
    right: 20px;
  }

  .dietary-dropdown .dropdown-menu .star-icons li svg {
    font-size: 12px;
  }

  .dietary-dropdown .dropdown-menu .star-icons {
    padding-left: 8px;
    line-height: 16px;
  }

  .filter-rating-list ul.star-icons li span {
    font-size: 16px;
  }

  .filter-rating-list ul.star-icons li {
    margin-right: 10px;
  }

  .filter-rating-list ul.star-icons li svg {
    font-size: 14px;
  }

  .filter-rating-list img.check-icon {
    width: 20px;
  }

  .delivery-fee-list ul li button {
    font-size: 15px;
    padding: 5px 0;
  }

  .delivery-fee-list ul li:first-child button {
    padding-top: 0;
  }

  .delivery-fee-list ul li button img.check-icon {
    width: 20px;
    top: 10px;
  }  
  .popular-item .popular-item-name p img {
    width: 14px;
    margin: -2px 0 0 1px;
  }

}

@media screen and (max-width:1330px) {
  .popular-slider-item {
    margin-left: 17px;
  }  

}

@media screen and (max-width:1199px) {
  .popular-cuisines-section {
    padding-left: 40px;
    padding-right: 40px;
  }

  .popular-slider-item {
    margin-left: 15px;
  }

  .popular-slider-item:first-child {
    margin-left: 0;
  }

  .popular-slider ul.slick-dots {
    display: none !important;
  }
  .filter-section{
    padding-left: 60px;
    padding-right: 60px;
  }
  .filter-box button.filter-btn svg {
    font-size: 35px;
  }
  .most-popular-section {
    padding-left: 40px;
    padding-right: 40px;
  }  
  .filter-section-bg{
    margin: 0 -20px;
  }
  .filter-list li {
    margin-right: 10px;
  }

  .filter-list li button img {
    width: 16px;
    margin-right: 4px;
  }

  .filter-list li button svg.fa-chevron-down {
    font-size: 14px;
    margin-left: 4px;
  }

  .discount-offer {
    font-size: 13px;
  }

  .section-seprate-line {
    margin-top: 35px;
    margin-bottom: 30px;
  }  

}

@media screen and (min-width:992px) and (max-width:1199px) {
.filter-section-bg{
    padding-bottom: 15px;
}  
.filter-list li{
    margin-right: 5px;
}  
.filter-list li button, 
.filter-section-bg.fixed .filter-list li button {  
    font-size: 13px;
    padding:9px 12px;
}

}

@media screen and (max-width:991px) {
  .popular-cuisines-section {
    padding-left: 30px;
    padding-right: 30px;
  }

  #FilterModal .modal-dialog {
    width: calc(100% - 20px);
  }

  .filter-box p {
    font-size: 18px;
  }
  .filter-section-bg{
    position: sticky;
    top:0;
    min-height: 118px;
  }
  .filter-section-bg.fixed{
    position: sticky;
  }
  .filter-section-bg.fixed .filter-section .container {
    max-width: 720px;
    margin: auto;
    display: table;
  }

  .filter-list li button {
    white-space: nowrap;
  }  
  .filter-section-bg.fixed .filter-list {
    padding-bottom: 0;
  }
  .filter-mobile-view .search-for-restaurant {
    margin-bottom: 10px;
  }
  .filter-section, 
  .filter-section-bg.fixed .filter-section {
    padding: 15px 28px;
  }  
  .filter-section-bg.fixed .filter-section .container {
    padding:0px;
  }
  .filter-section-bg .filter-section .filter-slide-close-bg,
  .filter-section-bg .filter-section .filter-slide{
    display: none;
  }
  .filter-slide {
    position: fixed;
    min-width: 250px;
    max-width: 250px;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 126;
    padding: 20px;
    box-shadow: 0 0 5px 0 #e1e1e1;
    background-color: #fff;
    left: -100px;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
  }

  .filter-slide::-webkit-scrollbar {
    width: 5px;
  }

  .filter-slide::-webkit-scrollbar-track {
    background: #ffe6e7;
  }

  .filter-slide::-webkit-scrollbar-thumb {
    background: $primary;
  }

  .filter-slide::-webkit-scrollbar-thumb:hover {
    background: $primary;
  }

  .filter-slide button.filter-slide-button {
    font-size: 28px;
    color: $primary;
    padding: 0;
    border: 0;
    border-radius: 0;
    background-color: transparent;
    margin-left: 5px;
    margin-bottom: 20px;
  }

  .filter-slide .location-box {
    padding: 7px 15px;
    position: relative;
    margin-bottom: 15px;
  }

  .filter-slide ul.filter-list {
    flex-wrap: wrap;
  }

  .filter-slide ul.filter-list li {
    width: 100%;
    margin: 0;
    margin-bottom: 15px;
  }

  .filter-slide ul.filter-list li button {
    font-size: 14px;
    padding: 7px 15px;
    width: 100%;
    position: relative;
    text-align: left;
  }

  .filter-slide ul.filter-list li button img {
    margin-top: -3px;
  }

  .filter-slide .filter-list li button svg.fa-chevron-down {
    position: absolute;
    top: 10px;
    right: 15px;
  }

  .filter-mobile-view {
    display: block;    
  }
  .filter-section .search-for-restaurant input.form-control {
    font-size: 14px;
    width: 100%;
    height: 36px;
    padding: 0 40px 0 45px;
  }
  .filter-mobile-view .filter-btn button.filter-slide-button {
    font-size: 16px;
    color: #000;
    text-decoration: none;
    font-weight: 600;
    background-color: transparent;
    border: 0;
    padding: 0;
  }
  .filter-mobile-view .filter-btn button.filter-slide-button img {
    height: 22px;
    margin-left: 5px;
  }
  .filter-mobile-view .search-for-restaurant{
    margin-bottom: 15px;
  }
  .filter-mobile-view .location-box{
    padding:7px 15px;
    width: 210px;    
    margin-bottom:0;
    position: relative;
  }  
  .filter-mobile-view .location-box ul li svg.fa-circle{
    top:-2px;
  }  
  .filter-mobile-view .location-box ul li button{
    font-size: 15px;
    color: #8b8f8f;
    font-weight: 600;
    padding: 0;
    border: 0;
    text-decoration: underline;
  }
  .filter-mobile-view .location-search-box .search-for-restaurant{
    margin-bottom:0;
  }  
  .filter-mobile-view .location-search-box .search-for-restaurant input.form-control{
    font-size: 13px;
    padding: 0 45px 0 35px;
  }
  .filter-box .dropdown-menu {
    border-radius: 10px;
  }
  .filter-box .dropdown-menu ul li {
    font-size: 14px;
    padding: 6px 20px;
  }

  .filter-box .dropdown-menu ul li .form-check {
    padding-left: 30px;
  }

  .filter-box .dropdown-menu ul li .form-check input.form-check-input {
    margin-top: 5px;
  }

  .dietary-dropdown .dropdown-menu {
    border-radius: 10px;
  }

  .dietary-dropdown .dropdown-menu ul li {
    margin-bottom: 0;
  }

  .dietary-dropdown .dropdown-menu ul li button {
    padding: 6px 20px;
  }

  .dietary-dropdown .dropdown-menu ul.star-icons li {
    width: auto;
  }

  .dietary-dropdown .dropdown-menu .star-icons li svg {
    font-size: 10px;
  }

  .most-popular-section {
    padding-left: 30px;
    padding-right: 30px;
  }

  .filter-popup-list {
    min-width: 190px;
    max-width: 190px;
  }
  .location-box ul li{
    max-width: 75px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

}

@media screen and (max-width:800px) {
  .filter-section-bg .filter-section .container,
  .filter-section-bg.fixed .filter-section .container {
    padding:0 20px;
  }
}

@media screen and (max-width:767px) {
  .popular-cuisines-section {
    padding-left: 10px;
    padding-right: 10px;
  }
  .filter-box p {
    display: none;
  }  
  .most-popular-section {
    padding-left: 10px;
    padding-right: 10px;
  }
  .filter-section-bg{
    min-height: 112px;
  }
  .filter-section, 
  .filter-section-bg.fixed .filter-section {
    padding: 12px 0;
  }
  .filter-section-bg.fixed .filter-section .container {
    max-width: 540px;
    padding:0;
  }
  .modal-content .modal-body .tab-content {
    padding-bottom: 60px;
  }

  #FilterModal .modal-dialog .modal-footer {
    bottom: 0px;
  }

  #FilterModal .modal-footer button.btn {
    margin-bottom: 15px;
  }

  #FilterModal .modal-dialog .modal-content button.btn-close {
    right: 25px;
  }

  .filter-popup-list ul li {
    padding-bottom: 20px;
  }

  .filter-category-list ul li {
    padding-bottom: 20px;
  }

  .filter-category-list ul li .form-check {
    padding-left: 40px;
  }
  .delivery-fee-list ul {
    margin-bottom: 10px;
  }
  .modal-footer button.btn {
    width: 130px;
    line-height: 14px;
    margin: 0 5px;
  }
  .filter-section-bg .filter-section .container{
    padding: 0;
  }  

}

@media screen and (max-width:600px) {
.filter-category-list {
  margin-bottom: 0px;
}
.filter-category-list ul{
  margin-bottom:0;
}

}
@media screen and (max-width:575px) {
  .popular-cuisines-section .main-heading h5 {
    text-align: center;
  }

  .popular-cuisines-section .main-heading {
    margin-bottom: 50px;
  }

  .offer-for-you-slider,
  .most-popular-slider {
    margin-bottom: 25px;
  }

  .offer-for-you-slider button.slick-arrow,
  .most-popular-slider button.slick-arrow {
    top: -40px;
  }

  .modal-content .modal-header {
    padding: 7px 30px;
  }

  #FilterModal .modal-content .modal-body {
    padding: 0 30px;
    flex-wrap: wrap;
  }
  .filter-popup-list {
    max-width: 100%;
    width: 100%;
    border: 0;
    border-bottom: 2px solid #F4F3F3;
  }
  .filter-category-list {
    margin-bottom: 0px;
  }
  .delivery-fee-list {
    margin-bottom: 10px;
  }
  .most-popular-item{
    padding: 0;
  }
  .filter-section-bg.fixed .filter-section .container,
  .filter-section-bg .filter-section .container {
    padding: 0 15px;
    max-width: 540px;
  }

}

@media screen and (max-width:480px) {
  .filter-slide{
    padding: 15px;
  }
  .filter-slide button.filter-slide-button {
    font-size: 25px;
    margin-bottom: 15px;
  }
  .filter-slide ul.filter-list li {
    margin: 0 0 12px;
  }
  .popular-cuisines-section {
    padding:0 10px 10px 10px !important;
  }
  .popular-item {
    width: 80px;
    height: 65px;
  }

  .popular-item .popular-item-bg-one,
  .popular-item .popular-item-bg-two {
    width: 80px;
    height: 65px;
  }

  .popular-item .popular-item-name img {
    max-width: 60px;
    min-height: 55px;
    max-height: 55px;
    border-radius: 10px;
    margin-bottom:6px;
  }
  .popular-item .popular-item-name p{
    font-size: 11px;
    line-height: 10px;
  }
  .popular-item .popular-item-name p img{
    width: 12px;
    margin: -2px 0 0 0;
  }
  .filter-mobile-view .search-for-restaurant{
    margin-bottom: 8px;
  }
  .filter-section .search-for-restaurant svg {
    font-size: 14px;
  }  
  .filter-section .search-for-restaurant input.form-control {
    font-size: 12px;
    height: 32px;
    padding-left: 38px;
    padding-right: 38px;
  }
  .filter-mobile-view .location-search-box .search-for-restaurant input.form-control {
    font-size: 11px;
    padding: 0 45px 0 30px;
  }
  .filter-section .location-search-box .search-for-restaurant svg {
    font-size: 12px;
    left: 11px;
  }
  .filter-mobile-view .search-for-restaurant svg.search-close{
    top:16px;
    font-size:13px !important;
  }
  .filter-section .location-search-box .search-for-restaurant button.btn {
    height: 32px;
  }  
  .filter-section .location-search-box .search-for-restaurant button.btn svg{
    font-size: 14px;
  }
  .filter-mobile-view .location-box {
    width: 190px;
    padding: 6px 15px;
  }  
  .filter-mobile-view .location-box ul li {
    font-size: 13px;
  }
  .filter-section .location-box ul li svg.fa-location-dot {
    top: 2px;
    font-size: 18px;
  }  
  .filter-section-bg{
    margin: 0 -12px;
    min-height: 93px;
  }
  .filter-section, 
  .filter-section-bg.fixed .filter-section {
    padding: 10px 0;
  }
  .filter-section-bg.fixed .filter-section .container,
  .filter-section-bg .filter-section .container {
    padding:0;
    max-width:calc(100% - 44px) ;
  }
  .filter-box button.filter-btn {
    margin-left: 15px;
  }

  .filter-box button.filter-btn svg {
    font-size: 28px;
  }

  .filter-box button.filter-btn img {
    width: 28px;
  }

  .offer-for-you-slider button.slick-arrow.slick-prev,
  .most-popular-slider button.slick-arrow.slick-prev {
    margin-right: 50px;
  }

  .modal-content .modal-header {
    padding: 7px 20px;
  }

  #FilterModal .modal-content .modal-header h5.modal-title {
    font-size: 28px;
  }

  #FilterModal .modal-content .modal-body {
    padding: 0 20px;
  }

  .filter-category-list ul li .form-check {
    padding-left: 35px;
  }

  .modal-footer button.btn {
    width: 100px;
    height: 35px;
    font-size: 13px;
    padding: 5px 10px;
    margin: 0 5px;
  }

  .filter-mobile-view .filter-btn button.filter-slide-button {
    font-size: 14px;
  }

  .filter-mobile-view .filter-btn button.filter-slide-button img {
    height: 20px;
  }
  .location-box ul li{
    max-width: 60px;
  }
  .filter-mobile-view .location-box ul li button{
    font-size: 14px;
  }

}
