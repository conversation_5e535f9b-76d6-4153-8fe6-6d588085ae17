.order-history-box {
    display: flex;
    margin-bottom: 75px;
}

.order-image {
    max-width: 280px;
    width: 100%;
    min-height: 170px;
    max-height: 170px;
    position: relative;
}

.order-image img.order-main-image {
    width: 100%;
    height:100%;
    object-fit:cover;
    border-radius: 10px;
    box-shadow: 0px 0px 25px 5px #00000033;
}
.order-image .order-logo {
    width: 74px;
    height: 74px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    position: absolute;
    top: 16px;
    left: 16px;
    border-radius: 50%;
    box-shadow: 0px 4px 15px 0px #00000080;
    background-color: #FFFFFF;
}
.order-image .order-logo img {
    width:100%;
    box-shadow: none;
}

.order-details {
    width: 100%;
    padding-left: 45px;
}

.order-title {
    display: flex;
    margin-bottom: 15px;
}

.order-title h6 {
    color: #000000;
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 0;
}

.order-title ul {
    padding-left: 30px;
    padding-top: 3px;
    margin-bottom: 0;
}

.order-title ul li {
    color: #8F8F8A;
    font-family: 'Visby CF';
    font-size: 15px;
    font-weight: 700;
    display: inline-block;
    position: relative;
    padding-right: 10px;
    margin-right: 10px;
}

.order-title ul li::after {
    width: 4px;
    height: 4px;
    content: '';
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    right: -2px;
    background-color: #8F8F8A;
    border-radius: 50%;
}

.order-title ul li:last-child:after {
    display: none;
}

.order-title ul li button {
    color: #000000;
    font-size: 15px;
    text-decoration: underline;
    font-weight: 700;
    padding: 0;
    background-color: transparent;
    border:0;
}

.order-addon-list ul {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
}

.order-addon-list ul li {
    display: flex;
    width: 33.3%;
    padding-bottom: 20px;
    padding-right: 30px;
}
.order-count {
    font-family: 'Fredoka';
    font-size: 15px;
    font-weight: 500;
    line-height: 30px;
    width: 30px;
    min-width: 30px;
    height: 30px;
    min-height: 30px;
    margin-top: 3px;
    text-align: center;
    border-radius: 50%;
    background-color: #F4F3F3;
    box-shadow: 0px 0px 5px 0px #00000033;
}
span.multiple-symbol{    
    height: 25px;
    padding: 0;
    border: 0;
    line-height: 20px;
    margin-left: 10px;
    background-color: transparent;
    color: #000;
    font-weight: bold;
    font-size: 18px;    
}
.addon-order-detail {
    padding-left:10px;
}

.addon-order-detail p {
    font-family: 'Visby CF';
    font-size: 18px;
    color: #000000;
    line-height: normal;
    font-weight: 800;
    margin-bottom: 2px;
}

.order-addon-list ul li .addon-order-detail ul li {
    width: 100%;
    padding-bottom: 0;
}

.addon-order-detail ul li {
    color: #8F8F8A;
    font-family: 'Visby CF';
    font-size: 18px;
    font-weight: 600;
    position: relative;
    padding-right: 10px;
    margin-right: 10px;
}

.addon-order-detail ul li::before {
    width: 4px;
    height: 4px;
    content: '';
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    right: -2px;
    background-color: #8F8F8A;
    border-radius: 50%;
}

.addon-order-detail ul li:last-child {
    padding-right: 0;
    margin-right: 0;
}

.addon-order-detail ul li:last-child:before {
    display: none;
}

.view-store ul {
    margin-bottom: 0;
}

.view-store ul li {
    margin-bottom: 10px;
}

.view-store ul li:last-child {
    margin-bottom: 0;
}

.view-store button.btn {
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    padding: 7px 15px;
    width: 150px;
    height: 40px;
    white-space: nowrap;
}

.view-store button.btn img {
    width: 16px;
    position: relative;
    top: -1px;
    margin-right: 2px;
}

.view-store button.btn img.red-reorder,
.view-store button.btn img.red-review {
    display: none;
}

.view-store button.btn:hover img.white-reorder,
.view-store button.btn:hover img.white-review {
    display: none;
}

.view-store button.btn:hover img.red-reorder,
.view-store button.btn:hover img.red-review {
    display: inline-block;
}
.view-store button.btn.raise-issue-btn{
    border-color:#F38F33 !important;
    background-color:#F38F33 !important;
}
.view-store button.btn.raise-issue-btn:hover{
    color:#F38F33 !important;
    background-color: transparent !important;
}
.dont-have-order {
    padding-top: 325px;
    padding-bottom: 50px;
}

.dont-have-order p {
    font-size: 30px;
    color: #000000;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2px;
}

.dont-have-order span {
    font-size: 20px;
    color: #000000;
    font-weight: 700;
    display: block;
    padding-bottom: 40px;
}

.dont-have-order button.btn {
    font-size: 18px;
    font-weight: 700;
    padding: 7px 15px;
    width: 150px;
    height: 40px;
}

/*---review-popup-css---*/
.modal-body {
    padding: 20px 30px 30px 30px;
}  
.modal-body .login-title {
    margin-bottom: 30px;
}
.modal-body .form-group {
    margin-bottom: 20px;
}
.modal-body .form-group textarea.form-control {
    font-size: 18px;
    padding: 10px 20px;
    height: 100px;
    resize:none;
}  
.modal-body button.btn {
    width: 100%;
}  

/*-----Ratings-Star----*/  
.star-ratings{        
    height: 34px;
    max-width: fit-content;
    margin: auto;
    margin-bottom:10px;
}
.star-ratings > label {
    color: #000; 
    float: right; 
}

.star-ratings > input { display: none; } 
.star-ratings > label:before {   
    width:24px;
    height:24px;
    content:'';
    background: url('/assets/images/outline-star.svg') no-repeat;
    background-size:25px;
    display: inline-block;  
    margin: 3px 5px;
}

.star-ratings > input:checked ~ label, 
.star-ratings:not(:checked) > label:hover, 
.star-ratings:not(:checked) > label:hover ~ label { 
    background: url('/assets/images/fill-star.svg') no-repeat;    
    background-size:25px;
    background-position:5px 3px;
}
.btn.review-btn{
    position: absolute;
    left: 0;
    right: 0;
    bottom: 12px;
    margin: auto;
    width: 120px;
    color: #000!important;
    font-size: 16px;
    padding: 5px 10px;
    line-height: 16px;
    font-weight: 700;
    background-color: #fff!important;
    border-color: #fff!important;
    box-shadow: 0 0 22px 0.75px #00000026;    
}
.btn.review-btn:hover{
    color:#FFFFFF !important;
    background-color:#000 !important;
    border-color:#000 !important;
}
.reservation-id{
    margin-bottom: 20px;
}
.reservation-id p{
    font-size: 20px; 
    color:#8f8f8a;
    font-weight:bold; 
    margin-bottom: 0;
}
.reservation-id p strong{
    color:#000;
}

@media screen and (max-width:1800px) {
.order-title h6 {
    min-width: 220px;
}

.order-details {
    padding-left: 40px;
}

.order-title ul {
    padding-left: 20px;
}
.reservation-id p{
    font-size: 18px;
}
  
}

@media screen and (max-width:1500px) {     
.order-history-box{
    margin-bottom: 70px;
}
.order-image {
    max-width: 210px;
    min-height: 127px;
    max-height: 127px;
}
.order-image .order-logo {
    width: 55px;
    height: 55px;
    padding: 6px;
    top: 15px;
    left: 15px;
}
.order-title h6{
    font-size: 22px;
}
.order-title .order-date-time {
    padding-top: 0;
}
.order-title ul {
    padding-left: 10px;
}
.order-title ul li {
    font-size: 12px;
}
.order-title ul li button{
    font-size: 12px;
}
.order-details{
    padding-left: 35px;
}
.order-title{
    margin-bottom: 10px;
}
.order-count{
    font-size: 12px;
    line-height: 22px;
    width: 22px;
    min-width: 22px;
    height: 22px;
    min-height: 22px;
    box-shadow: 0px 0px 3.75px 0px #00000033;
}
span.multiple-symbol{
    font-size: 14px;
}
.addon-order-detail p {
    font-size: 14px;
}
.order-addon-list ul li {    
    padding-right: 20px;
    padding-bottom: 20px;
}
.order-addon-list ul li .addon-order-detail ul li {
    font-size: 14px;
    margin-right: 5px;
    padding-right: 5px;
}
.view-store button.btn {
    font-size: 14px;
    padding: 1px 10px;
    width: 112px;
    height: 30px;
    line-height: 24px;
}
.dont-have-order{
    padding-top: 250px;
}
.dont-have-order p {
    font-size: 22px;
}
.dont-have-order span{
    font-size: 15px;
    padding-bottom: 25px;
}
.dont-have-order button.btn {
    font-size: 14px;
    font-weight: 700;
    padding: 2px 10px;
    width: 112px;
    height: 30px;
}
.modal-body{
    padding: 20px;
}
.modal-body .login-title {
    margin-bottom: 20px;
}
.star-ratings > label:before {
    width: 20px;
    height: 20px;
    background-size: 20px;
    margin: 2px 3px;
}
.star-ratings:not(:checked) > label:hover, 
.star-ratings:not(:checked) > label:hover ~ label, 
.star-ratings > input:checked ~ label {
    background-size: 20px;
    background-position: 3px 2px;
}
.star-ratings{
    margin-bottom: 5px;
}
.modal-body .form-group textarea.form-control {
    font-size: 14px;
    padding: 10px 15px;
    height: 90px;
}
.modal-body button.btn {
    padding: 5px 15px;
}
.btn.review-btn{
    font-size: 14px;
    width:110px;
    padding:3px 10px;
    bottom:10px;
}
.reservation-id{
    margin-bottom: 15px;
}
.reservation-id p {
    font-size: 15px;
}

}

@media screen and (max-width:1300px) {
.order-addon-list ul li{
    width:50%;
    padding-right: 15px;
}

}

@media screen and (max-width:1199px) {          
.order-history-box {
    margin-bottom: 55px;
}
.order-title {
    flex-wrap: wrap;
}
.order-title .order-date-time {
    padding: 0;
}
.order-details {
    padding-left: 25px;
}

}


@media screen and (max-width:991px) {    
.order-history-box {
    flex-wrap: wrap;
}
.order-image{
    margin-bottom: 25px;
}
.order-history-box .order-details {
    max-width: 100%;
    width: 100%;
    margin-bottom: 15px;
    padding-left: 0;
}

}

@media screen and (max-width:767px) {
.order-history-box {
    justify-content: center;
}
.dont-have-order {
    padding-top: 125px;
    padding-bottom: 50px;
}

}

@media screen and (max-width:480px) {  
.dont-have-order {
    padding-top: 80px;
    padding-bottom: 40px;
}
.dont-have-order p{
    margin-bottom: 10px;
}
.dont-have-order span {
    padding-bottom: 10px;
}

}

@media screen and (max-width:400px) {
.order-history-box .order-details {
    margin-bottom: 0px;
}    
.order-addon-list ul li {
    width: 100%;
    padding-right: 0;
    padding-bottom: 20px;
}

}