<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>


<div class="invite-friends-box" *ngIf="!isLoading">

  <h6>Invite your friend to Gogrubz, you get {{convertNumber(referral.invite_amount)}} after your friend<br>
    registered with us. Your friend will also get {{convertNumber(referral.receive_amount)}}</h6>

  <div class="referral-code">
    <label>Referral Code</label>
    <div class="form-group">
      <input class="form-control" type="text" [(ngModel)]="user.referral_code" [placeholder]="user.referral_code"
        readonly>
      <button class="btn" (click)="copyMessage()">{{copyTextChange?'Copied':'Copy'}}</button>
    </div>
  </div>

  <div class="share-your-referral-code">
    <label>Share Your Referral Code</label>
    <ul class="socials-list">
      <li>
        <a class="facebook" [href]="'http://www.facebook.com/sharer.php?u='+this.refferUrl" target="_blank">
          <svg class="fa-brands fa-facebook-f"></svg>
        </a>
        <span>Facebook</span>
      </li>
      <li>
        <a class="twitter" [href]="'https://twitter.com/share?url='+this.refferUrl" target="_blank">
          <svg class="fa-brands fa-x-twitter"></svg>
        </a>
        <span>Twitter</span>
      </li>
      <li>
        <button class="google cursor" (click)="smsReferral(phoneModal)">
          <svg class="fa-solid fa-comment-sms"></svg>
        </button>
        <span>Share on SMS</span>
      </li>
      <li>
        <a class="whatsapp" [href]="'https://api.whatsapp.com/send?text='+this.refferUrl" target="_blank">
          <svg class="fa-brands fa-whatsapp"></svg>
        </a>
        <span>Whatsapp</span>
      </li>
      <li>
        <button class="gmail cursor" (click)="emailReferral(emailModal)">
          <svg class="fa-regular fa-envelope"></svg>
        </button>
        <span>Gmail</span>
      </li>
    </ul>
  </div>

</div>

<div class="referral-friend-work">

  <div class="row">
    <div class="col-lg-12">
      <h6>How does invite friend work</h6>
    </div>

    <div class="col-lg-4">
      <div class="work-friend-work-box">
        <span class="counter">1</span>
        <svg class="fa-solid fa-user-group"></svg>
        <p>Invite your Friends</p>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="work-friend-work-box">
        <span class="counter">2</span>
        <svg class="fa-solid fa-thumbs-up"></svg>
        <p>Successful Registration</p>
      </div>
    </div>

    <div class="col-lg-4">
      <div class="work-friend-work-box">
        <span class="counter">3</span>
        <svg class="fa-solid fa-sterling-sign"></svg>
        <p>You will get {{convertNumber(referral.invite_amount)}}</p>
      </div>
    </div>

  </div>
</div>

<div class="successful-invite-friends-list">
  <h6>Your Successful Invite Friends</h6>

  <div class="table-responsive">
    <table class="table" *ngIf="referralPendings.length > 0">
      <thead>
        <tr>
          <th>Sr. No</th>
          <th>Referral Name</th>
          <th>Email ID</th>
          <th>Amount</th>
          <th>Date</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of referralPendings;let i = index">
          <td>{{i+1}}</td>
          <td>{{item.first_name}} {{item.last_name}}</td>
          <td>{{item.username}}</td>
          <td>{{item.invite_amount}}</td>
          <td>{{convertToDate(item.created)}}</td>
          <td>{{(item.status == 'Eligible')?'Pending':item.status}}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>


<ng-template #phoneModal let-modal>
  <div id="share-sms-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body">
      <h5 class="login-title">Share On SMS</h5>
      <form>
        <div class="row">
          <form nz-form #phoneForm="ngForm" (ngSubmit)="validatePhone(phoneForm)">
            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="phoneErrorTpl">
                    <nz-input-group>
                      <input type="text" inputmode="numeric" nz-input minlength="10" maxlength="11" id="phoneNumber"
                        name="phoneNumber" (keypress)="validateMobile($event)" [(ngModel)]="user.phone_number"
                        required="" placeholder="Phone Number" class="form-control">
                    </nz-input-group>
                    <ng-template #phoneErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter mobile number!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('minlength')">
                        mobile number at least 10 digit!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('maxlength')">
                        mobile number maximum 11 digits long!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <nz-form-item *ngIf="Modelerror">
              <span class="text-danger fw-bold">{{ Modelerror }}</span>
            </nz-form-item>

            <div class="col-md-12">
              <button class="btn modal-black-btn" nz-button>
                <i class="spinner-border" *ngIf="isModelLoading"></i>
                Send
              </button>
            </div>
          </form>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template #emailModal let-modal>
  <div id="share-email-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body">
      <h5 class="login-title">Share On Email</h5>
      <form>
        <div class="row">
          <form nz-form #emailForm="ngForm" (ngSubmit)="validateEmail(emailForm)">

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="emailErrorTpl">
                    <nz-input-group>
                      <input type="email" nz-input email="true" pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
                        #reffEmail="ngModel" id="emailAddress" name="emailAddress" [(ngModel)]="user.username"
                        placeholder="Enter email" class="form-control" required="">
                    </nz-input-group>
                    <ng-template #emailErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your email!
                      </ng-container>
                      <ng-container
                        *ngIf="control.hasError('email') || (!control.hasError('required') && reffEmail.touched) || (!control.hasError('required') && !reffEmail.valid)">
                        Email must be a valid email address
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <nz-form-item *ngIf="ModelEmailerror">
              <span class="text-danger fw-bold">{{ ModelEmailerror }}</span>
            </nz-form-item>

            <div class="col-md-12">
              <button class="btn modal-black-btn" nz-button>
                <i class="spinner-border" *ngIf="isEmailModelLoading"></i>
                Send
              </button>
            </div>

          </form>
        </div>
      </form>
    </div>
  </div>
</ng-template>