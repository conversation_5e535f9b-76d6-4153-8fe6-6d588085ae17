<section class="product-main-box-section" (paste)="(false)" (copy)="(false)">

    <div class="loader-bg" *ngIf="isModelLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container loader-height" *ngIf="isLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container" *ngIf="!isLoading">

        <div class="product-details-left">
            <div class="row">
                <div class="col-md-12">
                    <div class="product-main-box">
                        <div class="favourite-icon" *ngIf="user?.id">
                            <button class="unlike-btn" *ngIf="restaurant?.is_favourite == '0'">
                                <img (click)="favourite(true)" src="assets/images/unlike.svg"
                                    alt="Go-Grubz-unlike-image" loading="lazy">
                            </button>
                            <button class="like-btn" *ngIf="restaurant?.is_favourite == '1'">
                                <img (click)="favourite(false)" src="assets/images/like.svg" alt="Go-Grubz-like-image"
                                    loading="lazy">
                            </button>
                        </div>
                        <div class="restaurant-status-title-bg"
                            *ngIf="restaurant?.currentStatus == 'Closed' && (restaurant?.restaurant_delivery == 'Yes' || restaurant?.restaurant_pickup == 'Yes') && restaurant?.online_order == 'Yes'">
                            <h5>Closed</h5>
                        </div>
                        <div class="restaurant-status-title-bg"
                            *ngIf="restaurant?.currentStatus == 'PreOrder' && (restaurant?.restaurant_delivery == 'Yes' || restaurant?.restaurant_pickup == 'Yes') && restaurant?.online_order == 'Yes'">
                            <h5>PreOrder</h5>
                        </div>
                        <div class="restaurant-status-title-bg"
                            *ngIf="(restaurant?.restaurant_delivery == 'No' && restaurant?.restaurant_pickup == 'No') || restaurant?.online_order == 'No'">
                            <h5>Currently Unavailable</h5>
                        </div>
                        <img class="product-main-image" [src]="promotions?.image_url"
                            [alt]="restaurant?.restaurant_name"
                            onerror="this.src='./assets/images/product-main-image.png';">
                        <a class="btn make-reservation-btn" [routerLink]="'/'+restaurant?.seo_url+'/reservation'"
                            *ngIf="restaurant?.booking_status == 'Yes' && restaurant?.restaurant_booktable == 'Yes'">
                            Make a reservation
                        </a>
                        <div class="product-logo">
                            <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
                                onerror="this.src='./assets/favicon.png';">
                        </div>
                    </div>
                </div>
            </div>

            <div class="product-rating-section">
                <div class="row">
                    <div class="col-xl-8 col-lg-8">
                        <div class="rating-main-box">
                            <div class="product-name">
                                <h5>{{ restaurant?.restaurant_name }}</h5>
                            </div>
                            <div class="product-rating">
                                <ul>
                                    <li>
                                        <span>
                                            {{restaurant?.average_rating > 0 ? (restaurant?.average_rating | number :
                                            '1.2-2') : 0.00
                                            }}
                                        </span>
                                        <svg class="fa-solid fa-star"></svg> ({{restaurant?.total_reviews > 0 ?
                                        restaurant?.total_reviews:'0'}} Reviews)
                                    </li>
                                    <li class="pt-1">
                                        <div *ngFor="let cuisines of restaurant?.cuisine_names; let i=index">
                                            <svg class="fa-solid fa-circle" *ngIf="i != 0"></svg>
                                            {{ cuisines.cuisine_name }}
                                        </div>
                                    </li>
                                    <li class="pt-1">
                                        <svg class="fa-solid fa-location-dot"></svg>
                                        {{ restaurant?.distance }} miles
                                    </li>
                                </ul>
                            </div>
                            <div class="open-close">
                                <ul>
                                    <li class="green-text" *ngIf="restaurant?.currentStatus != 'Closed'">
                                        <svg class=" fa-regular fa-clock"></svg>
                                        {{ restaurant?.currentStatus }} Now
                                    </li>
                                    <li class="red-text" *ngIf="restaurant?.currentStatus == 'Closed'">
                                        <svg class="fa-regular fa-clock"></svg>
                                        {{ restaurant?.currentStatus }} Now
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-circle"></svg>
                                    </li>
                                    <li *ngIf="restaurant?.currentStatus != 'Open'">
                                        Open at {{ openTime }}
                                    </li>
                                    <li *ngIf="restaurant?.currentStatus == 'Open'">
                                        Closes at {{ closeTime }}
                                    </li>
                                </ul>
                            </div>
                            <div class="reviews-rating-main">
                                <h6>Reviews & Ratings</h6>
                                <div class="reviews-rating-box">
                                    <a [routerLink]="'/'+restaurant.seo_url+'/reviews'">
                                        <div class="reviews-rating-detail">
                                            <div class="reviews-show">
                                                <a [routerLink]="'/'+restaurant.seo_url+'/reviews'">
                                                    <h4> {{restaurant?.average_rating > 0 ? (restaurant?.average_rating
                                                        |
                                                        number : '1.2-2') : 0.00 }}</h4>
                                                </a>
                                                <ul class="reviews-list">
                                                    <li *ngFor="let item of [1, 2, 3, 4, 5];">
                                                        <a [routerLink]="'/'+restaurant.seo_url+'/reviews'"
                                                            *ngIf="restaurant?.average_rating > item"><svg
                                                                class="fa-solid fa-star"></svg></a>
                                                        <a [routerLink]="'/'+restaurant.seo_url+'/reviews'"
                                                            *ngIf="restaurant?.average_rating <= item"><svg
                                                                class="fa-regular fa-star"></svg></a>
                                                    </li>
                                                </ul>
                                                <a [routerLink]="'/'+restaurant.seo_url+'/reviews'">
                                                    <span>{{restaurant?.total_reviews > 0 ?
                                                        restaurant?.total_reviews:'0'}}
                                                        Reviews</span>
                                                </a>
                                            </div>
                                            <div class="rating-show" *ngIf="!isReviewLoading">
                                                <div class="progress-box">
                                                    <span>5</span>
                                                    <div class="progress" role="progressbar" aria-valuenow="85"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        <div class="progress-bar"
                                                            [ngStyle]="{ 'width.%': calculateAverageRating(5) }"></div>
                                                    </div>
                                                </div>
                                                <div class="progress-box">
                                                    <span>4</span>
                                                    <div class="progress" role="progressbar" aria-valuenow="75"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        <div class="progress-bar"
                                                            [ngStyle]="{ 'width.%': calculateAverageRating(4) }"></div>
                                                    </div>
                                                </div>
                                                <div class="progress-box">
                                                    <span>3</span>
                                                    <div class="progress" role="progressbar" aria-valuenow="15"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        <div class="progress-bar"
                                                            [ngStyle]="{ 'width.%': calculateAverageRating(3) }"></div>
                                                    </div>
                                                </div>
                                                <div class="progress-box">
                                                    <span>2</span>
                                                    <div class="progress" role="progressbar" aria-valuenow="20"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        <div class="progress-bar"
                                                            [ngStyle]="{ 'width.%': calculateAverageRating(2) }"></div>
                                                    </div>
                                                </div>
                                                <div class="progress-box">
                                                    <span>1</span>
                                                    <div class="progress" role="progressbar" aria-valuenow="5"
                                                        aria-valuemin="0" aria-valuemax="100">
                                                        <div class="progress-bar"
                                                            [ngStyle]="{ 'width.%': calculateAverageRating(1) }"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                    <div class="rating-list" *ngIf="reviews?.length > 0">
                                        <div class="rating-box" *ngFor="let review of reviews">
                                            <p>“{{review.message}}”</p>
                                            <div class="rating-details">
                                                <ul class="rating-star">
                                                    <!-- rating -->
                                                    <li *ngFor="let item of [1, 2, 3, 4, 5]; let i=index">
                                                        <span class="cursor" *ngIf="review.rating > i"><svg
                                                                class="fa-solid fa-star"></svg></span>
                                                        <span class="cursor" *ngIf="review.rating <= i"><svg
                                                                class="fa-regular fa-star"></svg></span>
                                                    </li>
                                                </ul>
                                                <span>{{ review.customer_name }}</span>
                                                <svg class="fa-solid fa-circle"></svg>
                                                <span>{{convertToDate(review.created)}}</span>
                                            </div>
                                        </div>

                                        <div class="see-all-review">
                                            <a [routerLink]="'/'+restaurant.seo_url+'/reviews'">
                                                See all reviews <svg class="fa-solid fa-arrow-down"></svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-lg-4 text-end">
                        <div class="restaurant-location-box">
                            <div class="location-map" *ngIf="streetAddress && restaurant.street_address">
                                <iframe class="border-0" [src]="streetAddress" allowfullscreen="" loading="lazy"
                                    referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                            <div class="location-details">
                                <ul>
                                    <li *ngIf="streetRedirectAddress">
                                        <a [href]="streetRedirectAddress" target="_blank">
                                            <img src="assets/images/location.svg" alt="Go-Grubz-location-image"
                                                loading="lazy">
                                            {{ restaurant?.street_address }}
                                            <span class="arrow-right">
                                                <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Right-Image"
                                                    loading="lazy">
                                            </span>
                                        </a>
                                    </li>
                                    <li>
                                        <a class="collapsed" data-bs-toggle="collapse" href="#opening-times">
                                            <img src="assets/images/clock.svg" alt="Go-Grubz-Clock-Image"
                                                loading="lazy">
                                            Opening Times
                                            <span class="arrow-right">
                                                <svg class="fa-solid fa-chevron-down"></svg>
                                            </span>
                                        </a>
                                        <div id="opening-times" class="collapse">
                                            <ul class="nav nav-tabs">
                                                <li [ngClass]="{'active' : timingOrderType == 'delivery'}">
                                                    <button (click)="timingOrderType = 'delivery'" data-toggle="tab"
                                                        class="cursor">Delivery</button>
                                                </li>
                                                <li [ngClass]="{'active' : timingOrderType == 'pickup'}">
                                                    <button (click)="timingOrderType = 'pickup'" data-toggle="tab"
                                                        class="cursor">Pickup</button>
                                                </li>
                                            </ul>
                                            <div class="tab-content">
                                                <div id="delivery" class="tab-pane"
                                                    [ngClass]="{'in active' : timingOrderType == 'delivery'}">
                                                    <ul>
                                                        <li>
                                                            <span>Monday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && (restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && estaurant?.monday_status != 'Close' && (restaurant?.monday_first_opentime != '12:00 PM' || restaurant?.monday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.monday_first_opentime}} -
                                                                {{restaurant?.monday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.monday_status != 'Close' && (restaurant?.monday_second_opentime != '12:00 PM' || restaurant?.monday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.monday_second_opentime}} -
                                                                {{restaurant?.monday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.monday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Tuesday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.tuesday_status != 'Close' && (restaurant?.tuesday_first_opentime != '12:00 PM' || restaurant?.tuesday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.tuesday_first_opentime}} -
                                                                {{restaurant?.tuesday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.tuesday_status != 'Close' && (restaurant?.tuesday_second_opentime != '12:00 PM' || restaurant?.tuesday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.tuesday_second_opentime}} -
                                                                {{restaurant?.tuesday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.tuesday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Wednesday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.wednesday_status != 'Close' && (restaurant?.wednesday_first_opentime != '12:00 PM' || restaurant?.wednesday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.wednesday_first_opentime}} -
                                                                {{restaurant?.wednesday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.wednesday_status != 'Close' && (restaurant?.wednesday_second_opentime != '12:00 PM' || restaurant?.wednesday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.wednesday_second_opentime}} -
                                                                {{restaurant?.wednesday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.wednesday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Thursday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.wednesday_status != 'Close' && (restaurant?.thursday_first_opentime != '12:00 PM' || restaurant?.thursday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.thursday_first_opentime}} -
                                                                {{restaurant?.thursday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.thursday_status != 'Close' && (restaurant?.thursday_second_opentime != '12:00 PM' || restaurant?.thursday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.thursday_second_opentime}} -
                                                                {{restaurant?.thursday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.thursday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Friday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.friday_status != 'Close' && (restaurant?.friday_first_opentime != '12:00 PM' || restaurant?.friday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.friday_first_opentime}} -
                                                                {{restaurant?.friday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.friday_status != 'Close' && (restaurant?.friday_second_opentime != '12:00 PM' || restaurant?.friday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.friday_second_opentime}} -
                                                                {{restaurant?.friday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.friday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Saturday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.saturday_status != 'Close' && (restaurant?.saturday_first_opentime != '12:00 PM' || restaurant?.saturday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.saturday_first_opentime}} -
                                                                {{restaurant?.saturday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.saturday_status != 'Close' && (restaurant?.saturday_second_opentime != '12:00 PM' || restaurant?.saturday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.saturday_second_opentime}} -
                                                                {{restaurant?.saturday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.saturday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Sunday</span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'morning' || restaurant?.working_time == 'both') && restaurant?.sunday_status != 'Close' && (restaurant?.sunday_first_opentime != '12:00 PM' || restaurant?.sunday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.sunday_first_opentime}} -
                                                                {{restaurant?.sunday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.working_time == 'evening' || restaurant?.working_time == 'both') && restaurant?.sunday_status != 'Close' && (restaurant?.sunday_second_opentime != '12:00 PM' || restaurant?.sunday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.sunday_second_opentime}} -
                                                                {{restaurant?.sunday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.sunday_status == 'Close'">Closed</span>
                                                        </li>
                                                    </ul>
                                                </div>

                                                <div id="pickup" class="tab-pane"
                                                    [ngClass]="{'in active' : timingOrderType == 'pickup'}">
                                                    <ul>
                                                        <li>
                                                            <span>Monday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_monday_status != 'Close' && (restaurant?.restaurant_timing?.pick_monday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_monday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_monday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_monday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_monday_status != 'Close' && (restaurant?.restaurant_timing?.pick_monday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_monday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_monday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_monday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_monday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Tuesday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_tuesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_tuesday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_tuesday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_tuesday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_tuesday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_tuesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_tuesday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_tuesday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_tuesday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_tuesday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_tuesday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Wednesday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_wednesday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_wednesday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_wednesday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_wednesday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_wednesday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_wednesday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_wednesday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_wednesday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_wednesday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Thursday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_thursday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_thursday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_thursday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_thursday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_thursday_status != 'Close' && (restaurant?.restaurant_timing?.pick_thursday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_thursday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_thursday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_thursday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_thursday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Friday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_friday_status != 'Close' && (restaurant?.restaurant_timing?.pick_friday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_friday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_friday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_friday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_friday_status != 'Close' && (restaurant?.restaurant_timing?.pick_friday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_friday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_friday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_friday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_friday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Saturday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_saturday_status != 'Close' && (restaurant?.restaurant_timing?.pick_saturday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_saturday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_saturday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_saturday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_saturday_status != 'Close' && (restaurant?.restaurant_timing?.pick_saturday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_saturday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_saturday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_saturday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_saturday_status == 'Close'">Closed</span>
                                                        </li>
                                                        <li>
                                                            <span>Sunday</span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'morning' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_sunday_status != 'Close' && (restaurant?.restaurant_timing?.pick_sunday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_sunday_first_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_sunday_first_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_sunday_first_closetime}}
                                                            </span>
                                                            <span
                                                                *ngIf="(restaurant?.restaurant_timing.pick_working_time == 'evening' || restaurant?.restaurant_timing.pick_working_time == 'both') && restaurant?.restaurant_timing?.pick_sunday_status != 'Close' && (restaurant?.restaurant_timing?.pick_sunday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_sunday_second_closetime != '12:00 PM')">
                                                                {{restaurant?.restaurant_timing?.pick_sunday_second_opentime}}
                                                                -
                                                                {{restaurant?.restaurant_timing?.pick_sunday_second_closetime}}
                                                            </span>
                                                            <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                                                *ngIf="restaurant?.restaurant_timing?.pick_sunday_status == 'Close'">Closed</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>

                                        </div>
                                    </li>
                                    <li>
                                        <a href="tel:(+44) {{ restaurant?.restaurant_phone }};">
                                            <img src="assets/images/phone.svg" alt="Go-Grubz-Phone-Image"
                                                loading="lazy">
                                            (+44) {{ restaurant?.restaurant_phone }}
                                            <span class="arrow-right">
                                                <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Right-Image"
                                                    loading="lazy">
                                            </span>
                                        </a>
                                    </li>
                                    <li *ngIf="restaurant.hygiene_link">
                                        <a class="d-flex" [href]="restaurant.hygiene_link" target="_blank">
                                            <img src="assets/images/hygiene.svg" alt="hygiene" loading="lazy">
                                            Hygiene Rating
                                            <img class="hygiene-rating-img" src="assets/images/hygiene-rating.svg"
                                                alt="Go-Grubz-Hygiene-Image" loading="lazy">
                                            <span class="arrow-right">
                                                <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Right-Image"
                                                    loading="lazy">
                                            </span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="delivery-pick-up-box">
                            <ul class="delivery-pick-up-list">
                                <li [style.pointer-events]="restaurant?.restaurant_delivery != 'Yes' ?'none':'auto'"
                                    [ngClass]="{'active' : order.order_type == 'delivery','disabled' : restaurant?.restaurant_delivery != 'Yes'}"
                                    (click)="orderType('delivery')">
                                    <p>Delivery</p>
                                    <span *ngIf="restaurant?.restaurant_delivery == 'Yes'"> {{ restaurant?.estimate_time
                                        }} mins </span>
                                    <span *ngIf="restaurant?.restaurant_delivery != 'Yes'"> Unavailable </span>
                                </li>
                                <li [style.pointer-events]="restaurant?.restaurant_pickup != 'Yes' ?'none':'auto'"
                                    [ngClass]="{'active' : order.order_type == 'pickup','disabled' : restaurant?.restaurant_pickup != 'Yes'}"
                                    (click)="orderType('pickup')">
                                    <p>Pick Up</p>
                                    <span *ngIf="restaurant?.restaurant_pickup == 'Yes'"> {{
                                        restaurant?.pickup_estimate_time }} mins </span>
                                    <span *ngIf="restaurant?.restaurant_pickup != 'Yes'"> Unavailable </span>
                                </li>
                            </ul>
                            <ul class="order-free-delivery-list">
                                <li class="active">
                                    <span>
                                        <img src="assets/images/money.svg" alt="Go-Grubz-money-image" loading="lazy">
                                        £{{ restaurant?.minimum_order }} Min. Order
                                    </span>
                                </li>
                                <li *ngIf="restaurant?.restaurant_delivery == 'Yes'">
                                    <span>
                                        <img src="assets/images/delivery.svg" alt="Go-Grubz-Delivery-Image"
                                            loading="lazy">
                                        £{{ restaurant?.deliveryCharges }} Delivery Fee
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="category-list" class="menu-category-list" id="allMenu">
                <div class="menu-section" id="category-menu">
                    <div class="category-inner-section">
                        <!--<div class="menu-top-line"></div>-->
                        <div class="row">
                            <div class="col-4">
                                <h5 class="menu-title">Menu</h5>
                            </div>
                            <div class="col-8 text-right">
                                <div class="d-flex justify-content-end">
                                    <div class="search-for-restaurant">
                                        <svg class="fa-solid fa-magnifying-glass"></svg>
                                        <input class="form-control" type="text" aria-label="Search"
                                            (keyup)='search($event.target.value)' [(ngModel)]="searchData"
                                            placeholder="Search items">
                                        <span (click)="search()" *ngIf="searchData">
                                            <svg class="fa-regular fa-xmark-circle search-close"></svg>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="menu-inner-sec" *ngIf="categories?.length > 0">
                            <div class="menu-list-arrow">
                                <button class="view-menu-btn" (click)="menuOpen()">
                                    <img src="assets/images/list.png" alt="Go-Grubz-list-image" loading="lazy">
                                </button>
                                <div class="view-menu-dropdown-bg" *ngIf="visible">
                                    <div class="view-menu-dropdown">
                                        <div class="view-menu-title">
                                            <p>View Menu</p>
                                            <button class="close view-menu-btn" (click)="menuClose()">
                                                <svg class="fa-solid fa-xmark"></svg>
                                            </button>
                                        </div>
                                        <div class="view-menu-list">
                                            <ul>
                                                <li [ngClass]="{'active' : scrollingCategoryId == category.id}"
                                                    *ngFor="let category of categories">
                                                    <button (click)="clickOnAccordion(category.id)">
                                                        {{ category.category_name }} <span>{{ category?.menu?.length
                                                            }}</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="view-menu-close-dropdown-bg" (click)="menuClose()"></div>
                            </div>
                            <div class="left-arrow">
                                <button (click)="prevSlide()"><svg class="fa-solid fa-chevron-left"></svg></button>
                            </div>
                            <ul class="menu-list">
                                <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfig"
                                    (init)="slickInit($event)" (breakpoint)="breakpoint($event)"
                                    (afterChange)="afterChange($event)" (beforeChange)="beforeChange($event)">
                                    <li ngxSlickItem [ngClass]="{'active slide' : scrollingCategoryId == category.id}"
                                        *ngFor="let category of categories">
                                        <button (click)="clickOnAccordion(category.id)">
                                            {{ category.category_name }}
                                        </button>
                                    </li>
                                </ngx-slick-carousel>
                            </ul>
                            <div class="right-arrow">
                                <button (click)="nextSlide()"><svg class="fa-solid fa-chevron-right"></svg></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="menu-section" *ngIf="categories?.length <= 0">
                <div class="dont-have-order text-center">
                    <p>No items found.</p>
                </div>
            </div>

            <div class="customer-favourites-section" *ngIf="categories?.length > 0">
                <div class="row" *ngIf="!searchData">
                    <div class="col-8">
                        <div class="main-heading" *ngIf="menuRecommended?.length > 0">
                            <h6>Customer Favourites</h6>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="allergy-info" *ngIf="restaurant?.is_allergy">
                            <button class="cursor" (click)="allergyShow()">
                                <img src="assets/images/allergy.png" alt="Go-Grubz-Allergy-Image" loading="lazy">Allergy
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row" *ngIf="menuRecommended?.length > 0 && !searchData">
                    <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6" *ngFor="let menu of menuRecommended;">
                        <div class="favourite-box">
                            <div class="favourite-image">
                                <span class="product-counter" *ngIf="menu?.qty > 0">{{ menu?.qty }}</span>
                                <img [src]="menu?.image_url" [alt]="menu?.menu_name"
                                    onerror="this.src='./assets/images/favourite-image-1.png';">
                                <button class="add-option" *ngIf="restaurant?.online_order == 'Yes'"
                                    [style.pointer-events]="isModelLoading || isAddonModelLoading ?'none':'auto'"
                                    (click)="addItemToCart(menuModal,menu,menu?.id)">Add</button>
                            </div>
                            <div class="favourite-content">
                                <h6 [title]="menu?.menu_name">{{ menu?.menu_name }}</h6>
                                <span class="price" *ngIf="menu.product_percentage <= 0">
                                    <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                    <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                    {{menu?.variants[0]?.orginal_price | currency: "GBP" }}
                                </span>
                                <span class="price" *ngIf="menu.product_percentage > 0">
                                    <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                    <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                    <span class="px-1 text-decoration-line-through">{{
                                        menu?.variants[0]?.orginal_price | currency: "GBP" }}</span>
                                    {{ (menu?.variants[0]?.orginal_price - (menu?.variants[0]?.orginal_price *
                                    menu.product_percentage / 100)) | currency: "GBP" }}
                                </span>
                                <p>{{ menu?.menu_description }} </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="row" [id]="'category_'+category.id" *ngFor="let category of categories">
                        <div class="col-lg-12">
                            <div class="main-heading">
                                <h6>{{ category.category_name }}</h6>
                            </div>
                        </div>

                        <div class="col-md-6 category-section" [attr.data-category-id]="category.id"
                            *ngFor="let menu of category.menu; trackBy: trackByFn">
                            <div class="horizontal-favourite-box">
                                <div class="horizontal-content-box">
                                    <h6 [title]="menu?.menu_name">{{ menu?.menu_name }}</h6>
                                    <span class="price" *ngIf="menu.product_percentage <= 0">
                                        <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                        <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                        {{ menu?.variants[0]?.orginal_price | currency: "GBP" }}
                                    </span>
                                    <span class="price" *ngIf="menu.product_percentage > 0">
                                        <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                        <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                        <span class="px-1 text-decoration-line-through">
                                            {{ menu?.variants[0]?.orginal_price | currency: "GBP" }}</span>
                                        {{ (menu?.variants[0]?.orginal_price - (menu?.variants[0]?.orginal_price *
                                        menu.product_percentage / 100)) | currency: "GBP" }}
                                    </span>
                                    <p>{{ menu?.menu_description }} </p>
                                    <div class="addon-items">
                                        <ul
                                            *ngIf="menu?.popular_dish =='Yes' || menu?.vegetarian || menu?.contains_nuts || menu?.milk || menu?.mustard || menu?.eggs || menu?.fish || menu?.gluten_free || menu?.vegan">
                                            <li>May contain:</li>
                                            <li *ngIf="menu?.popular_dish =='Yes'" title="Popular Dish">Popular Dish,
                                            </li>
                                            <li *ngIf="menu?.vegetarian" title="Vegetarian">Vegetarian,</li>
                                            <li *ngIf="menu?.contains_nuts" title="Contains Nuts">Contains Nuts,</li>
                                            <li *ngIf="menu?.milk" title="Milk">Milk,</li>
                                            <li *ngIf="menu?.mustard" title="Mustard">Mustard,</li>
                                            <li *ngIf="menu?.eggs" title="Eggs">Eggs,</li>
                                            <li *ngIf="menu?.fish" title="Fish">Fish,</li>
                                            <li *ngIf="menu?.gluten_free" title="Gluten-Free">Gluten-Free,</li>
                                            <li *ngIf="menu?.vegan" title="Vegan">Vegan,</li>
                                            <!--<li><img src="assets/images/info.svg" alt="Go-Grubz-info-image"
                                                    loading="lazy"></li>-->
                                        </ul>
                                    </div>
                                    <span class="product-counter" *ngIf="menu?.qty > 0">{{ menu?.qty }} </span>
                                    <ul class="spicy-list-images">
                                        <li *ngIf="menu?.spicy_dish =='extra_hot'">
                                            <img src="assets/images/extra-hot.png" alt="extra-hot">
                                        </li>
                                        <li *ngIf="menu?.spicy_dish =='hot'">
                                            <img src="assets/images/hot.png" alt="hot">
                                        </li>
                                        <li *ngIf="menu?.spicy_dish =='medium'">
                                            <img src="assets/images/medium.png" alt="medium">
                                        </li>
                                        <li *ngIf="menu?.spicy_dish =='mild'">
                                            <img src="assets/images/mild.png" alt="mild">
                                        </li>
                                        <li *ngIf="menu?.spicy_dish =='slightly_hot'">
                                            <img src="assets/images/slightlyhot.png" alt="slightlyhot">
                                        </li>
                                    </ul>
                                </div>
                                <div class="horizontal-image-box"
                                    *ngIf="menu?.image_url && restaurant?.online_order == 'Yes'">
                                    <img [src]="menu?.image_url" [alt]="menu?.menu_name"
                                        *ngIf="restaurant?.image_type == 'Yes'"
                                        onerror="this.src='./assets/images/fried-image.png';">
                                    <button class="add-option"
                                        [style.pointer-events]="isModelLoading || isAddonModelLoading ?'none':'auto'"
                                        (click)="addItemToCart(menuModal,menu,menu.id)">Add
                                    </button>
                                </div>
                                <div class="horizontal-image-box"
                                    *ngIf="!menu?.image_url && restaurant?.online_order == 'Yes'">
                                    <button class="add-option"
                                        [style.pointer-events]="isModelLoading || isAddonModelLoading ?'none':'auto'"
                                        (click)="addItemToCart(menuModal,menu,menu.id)">Add
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="add-to-cart-box-two d-none d-xl-block" *ngIf="carts?.length > 0">
            <div class="add-cart-box">
                <div class="cart-top">
                    <div class="top-cart-name">
                        <div class="cart-logo">
                            <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
                                onerror="this.src='./assets/favicon.png';">
                        </div>
                        <div class="cart-name">
                            <h6>Your Basket<img src="assets/images/cart.svg" alt="Go-Grubz-Cart-Image" loading="lazy">
                            </h6>
                            <p>{{ restaurant?.restaurant_name }}</p>
                        </div>
                    </div>
                    <div class="checkout-btn">
                        <button class="cursor" *ngIf="carts?.length <= 0 || restaurant.online_order == 'No'"
                            [class.disabled]="carts?.length <= 0 || restaurant.online_order == 'No'">
                            <span>Checkout <svg class="fa-solid fa-arrow-right"></svg></span>
                            <span>{{ convertNumber(getGrandTotal()) }}</span>
                        </button>
                        <button class="cursor" *ngIf="carts?.length > 0 && restaurant.online_order == 'Yes'"
                            (click)="goToCheckout()">
                            <span>Checkout <svg class="fa-solid fa-arrow-right"></svg></span>
                            <span>{{ convertNumber(getGrandTotal()) }}</span>
                        </button>
                    </div>
                </div>

                <div class="cart-middle" *ngIf="carts?.length == 0">
                    <div class="empty-cart-cls text-center py-5">
                        <img src="assets/cartitem.png" alt="GoGrubz-cart-item" loading="lazy">
                        <p><strong>No Item(s) Added</strong></p>
                    </div>
                </div>

                <div class="cart-middle" *ngIf="carts?.length > 0">
                    <div class="cart-item" *ngFor="let cart of carts; let i=index ; trackBy: trackByFn">
                        <div class="cart-image-item" *ngIf="cart?.image_url && restaurant?.image_type == 'Yes'">
                            <img [src]="cart?.image_url" [alt]="cart.menu_name">
                        </div>
                        <div class="cart-content">
                            <h6>{{cart.menu_name}}</h6>
                            <ul *ngIf="cart.subaddons_name">
                                <li>{{cart.subaddons_name}}</li>
                            </ul>
                            <p>{{convertNumber(cart?.total_price)}}</p>
                        </div>
                        <div class="cart-add-item">
                            <ul>
                                <li>
                                    <button class="cursor" (click)="updateToCart(cart,i,'remove')">
                                        <!-- <svg class="fa-solid fa-trash-can"></svg> -->
                                        <svg class="fa-solid fa-minus"></svg>
                                    </button>
                                </li>
                                <li>
                                    <span>{{ cart.quantity }}</span>
                                </li>
                                <li>
                                    <button class="cursor" (click)="updateToCart(cart,i,'add')">
                                        <svg class="fa-solid fa-plus"></svg>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>
<div class="clearfix"></div>

<div class="add-items d-xl-none" *ngIf="carts?.length > 0">
    <span (click)="goToCheckout()">
        <button class="btn" nz-button [disabled]="carts?.length <= 0 || restaurant.online_order == 'No'">
            <span>Checkout <svg class="fa-solid fa-arrow-right"></svg></span>
            <span>{{ convertNumber(getGrandTotal()) }}</span>
        </button>
    </span>
</div>

<!-- product-multiple-options-popup -->
<ng-template #menuModal let-modal>
    <div *ngIf="isAddonModelLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="modal-body product-body" *ngIf="!isModelLoading && !subAddonVisible && !isAddonModelLoading">
        <button type="button" class="btn-close product-close" data-bs-dismiss="modal" (click)="closeAll();">
            <svg class="fa-solid fa-xmark"></svg>
        </button>
        <div class="popup-product-box">
            <h6>{{ selectedMenu?.menu_name }}</h6>
            <p>{{ selectedMenu?.menu_description }}</p>
            <div class="product-image" *ngIf="selectedMenu?.image_url">
                <img [src]="selectedMenu?.image_url" [alt]="selectedMenu?.menu_name">
            </div>
        </div>
        <div class="choose-options">
            <h6>Choose Option</h6>
            <div class="required-select">
                <div class="required" *ngIf="selectedVariant?.required_error">
                    <svg class="fa-solid fa-circle-check"></svg>Required
                </div>
                <svg class="fa-solid fa-circle" *ngIf="selectedVariant?.required_error"></svg>
                <span *ngIf="selectedVariant?.required_error">Select 1</span>
            </div>
            <ul class="choose-list">
                <li *ngFor="let variant of selectedMenu?.variants; let i = index ; trackBy: trackByFn">
                    <div class="form-check">
                        <input type="radio" class="form-check-input radio-button" id="menu_{{ variant?.id }}"
                            name="menu_{{ selectedMenu?.id }}" [value]="variant?.id" [(ngModel)]="addVariant.id"
                            (ngModelChange)="fetchVartiantAddonItem(addonModal,selectedMenu?.id,variant.id)"
                            [attr.disabled]="isAddonModelLoading ? '' : null">
                        <label for="menu_{{ variant.id }}">
                            <div class="form-check-label">
                                <div class="d-flex">
                                    <div class="burger-name"> {{ variant?.sub_name }}</div>
                                    <button class="edit-addon-btn"
                                        *ngIf="selectedMenu?.menu_addon == 'Yes' && selectedSubAddonstring && variant?.id == selectedVariant?.id"
                                        (click)="editAddon()">
                                        Edit
                                    </button>
                                </div>
                                <ul class="extra-addon-list"
                                    *ngIf="selectedSubAddonstring && variant?.id == selectedVariant?.id">
                                    <li>
                                        {{ selectedSubAddonstring }}
                                        <svg class="fa-solid fa-circle"></svg>
                                    </li>
                                </ul>
                                <span class="price" *ngIf="selectedMenu.product_percentage <= 0">
                                    {{ variant.orginal_price | currency: "GBP" }}
                                </span>
                                <span class="price" *ngIf="selectedMenu.product_percentage > 0">
                                    <span class="px-1 text-decoration-line-through">
                                        {{ variant.orginal_price | currency: "GBP" }}
                                    </span>
                                    {{ (variant.orginal_price - (variant.orginal_price * selectedMenu.product_percentage
                                    /
                                    100)) | currency: "GBP" }}
                                </span>
                            </div>
                            <div class="right-arrow" *ngIf="selectedMenu?.menu_addon == 'Yes'"><svg
                                    class="fa-solid fa-chevron-right"></svg></div>
                        </label>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="modal-footer product-footer" *ngIf="!subAddonVisible && !isAddonModelLoading">
        <div class="add-to-product" *ngIf="!isModelLoading">
            <ul>
                <li><a (click)="updateSelected('remove')">
                        <!-- <svg class="fa-solid fa-trash-can"></svg> -->
                        <svg class="fa-solid fa-minus"></svg>
                    </a></li>
                <li><span>{{ selectedQnt }}</span></li>
                <li><a (click)="updateSelected('add')"><svg class="fa-solid fa-plus"></svg></a></li>
            </ul>
        </div>
        <button type="button" class="btn modal-black-btn w-100" (click)="validateMultiple(addonModal)">
            <i class="spinner-border" *ngIf="isModelLoading"></i>
            Add to cart
            <span *ngIf="selectedSubAddonPrice > 0">
                <span class="price-circle"> <svg class="fa-solid fa-circle"></svg></span>
                {{ selectedSubAddonPrice * selectedQnt | currency: "GBP"
                }}
            </span>
        </button>
    </div>

    <div id="addonpopup" *ngIf="subAddonVisible && !isAddonModelLoading">
        <div class="modal-header border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal"
                (click)="selectedAddonPrice = 0;selectedAddonDummy = [];subAddonVisible = false;">
                <svg class="fa-solid fa-chevron-left"></svg>
            </button>
            <div>
                <h6>{{ selectedMenu?.menu_name }}</h6>
                <span> {{ selectedVariant?.sub_name }}</span>
                <span class="subaddon-circle"> <svg class="fa-solid fa-circle"></svg></span>
                <span class="price" *ngIf="selectedMenu.product_percentage <= 0">
                    {{ selectedVariant?.orginal_price | currency: "GBP" }}
                </span>
                <span class="price" *ngIf="selectedMenu.product_percentage > 0">
                    <span class="px-1 text-decoration-line-through">
                        {{ selectedVariant?.orginal_price | currency: "GBP" }}
                    </span>
                    {{ (selectedVariant?.orginal_price - (selectedVariant?.orginal_price *
                    selectedMenu.product_percentage
                    / 100)) | currency: "GBP" }}
                </span>
            </div>
        </div>

        <div class="modal-body product-body" *ngIf="isAddonModelLoading">
            <div class="row text-center align-middle justify-content-center" *ngIf="isAddonModelLoading">
                <div class="col-md-12 ms-2 spinner-border text-primary text-center">
                    <span class="visually-hidden text-center">Loading...</span>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isAddonModelLoading">
            <div class="addon-list" *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
                <h6>
                    {{ mainAddon?.mainaddons_name }}
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count == 0">(Optional) Select up to {{
                        mainAddon.mainaddons_count }} add on(s)</span>
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0">(Required) Choose upto {{
                        mainAddon.mainaddons_count }} - Min. {{ mainAddon.mainaddons_mini_count}}</span>
                </h6>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count== 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.min_error">
                    (Required) • Select at least {{ mainAddon.mainaddons_mini_count }} add on(s).
                </span>
                <span class="text-danger"
                    *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.mainaddons_count!= 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <ul>
                    <li *ngFor="let subAddon of mainAddon.sub_addons">
                        <!-- <div class="form-check" *ngIf="mainAddon.mainaddons_count <= 1">
                            <input type="radio" class="form-check-input radio-button subaddon_{{subAddon?.id}}"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}" [value]="subAddon?.id"
                                [(ngModel)]="mainAddon.selectedSubAddonId"
                                (change)="addprice($event,subAddon?.subaddons_price,mainAddon?.id)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div> -->
                        <!--  *ngIf="mainAddon.mainaddons_count > 1" -->
                        <div class="form-check">
                            <input class="form-check-input subaddon_{{subAddon?.id}}" type="checkbox"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}"
                                [(ngModel)]="subAddon.selected"
                                (change)="addCheckboxPrice($event.target,subAddon?.subaddons_price)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div>
                    </li>
                </ul>
            </div>
            <button class="btn modal-black-btn" type="button" (click)="validate(addonModal)"
                *ngIf="!isAddonModelLoading">
                Confirm <span *ngIf="selectedAddonPrice > 0">
                    <span class="price-circle"> <svg class="fa-solid fa-circle"></svg></span> {{ selectedAddonPrice |
                    currency: "GBP" }} </span>
            </button>
        </div>
    </div>
</ng-template>

<!-- Addon-Modal -->
<ng-template #addonModal let-modal>
    <div id="addonpopup">
        <div class="modal-header border-0" *ngIf="!isAddonModelLoading">
            <button type="button" class="btn-close" data-bs-dismiss="modal"
                (click)="modal.dismiss('Cross click');selectedAddonPrice = 0;selectedAddonDummy = [];">
                <svg class="fa-solid fa-chevron-left"></svg>
            </button>
            <div>
                <h6>{{ selectedMenu?.menu_name }}</h6>
                <span> {{ selectedVariant?.sub_name }}</span>
            </div>
        </div>

        <div class="modal-body product-body" *ngIf="isAddonModelLoading">
            <div class="row text-center align-middle justify-content-center" *ngIf="isAddonModelLoading">
                <div class="col-md-12 ms-2 spinner-border text-primary text-center">
                    <span class="visually-hidden text-center">Loading...</span>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isAddonModelLoading">
            <div class="addon-list" *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
                <h6>
                    {{ mainAddon?.mainaddons_name }}
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count == 0">(Optional) Select up to {{
                        mainAddon.mainaddons_count }} add on(s)</span>
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0">(Required) Choose upto {{
                        mainAddon.mainaddons_count }} - Min. {{ mainAddon.mainaddons_mini_count }}</span>
                </h6>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count== 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.min_error">
                    (Required) • Select at least {{ mainAddon.mainaddons_mini_count }} add on(s).
                </span>
                <span class="text-danger"
                    *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.mainaddons_count!= 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <ul>
                    <li *ngFor="let subAddon of mainAddon.sub_addons">
                        <!-- <div class="form-check" *ngIf="mainAddon.mainaddons_count <= 1">
                            <input type="radio" class="form-check-input radio-button subaddon_{{subAddon?.id}}"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}" [value]="subAddon?.id"
                                [(ngModel)]="mainAddon.selectedSubAddonId"
                                (change)="addprice($event,subAddon?.subaddons_price,mainAddon?.id)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div> -->
                        <!-- *ngIf="mainAddon.mainaddons_count > 1" -->
                        <div class="form-check">
                            <input class="form-check-input subaddon_{{subAddon?.id}}" type="checkbox"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}"
                                [(ngModel)]="subAddon.selected"
                                (change)="addCheckboxPrice($event.target,subAddon?.subaddons_price)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div>
                    </li>
                </ul>
            </div>
            <button class="btn modal-black-btn" type="button" (click)="validate(addonModal)"
                *ngIf="!isAddonModelLoading">
                Add <span *ngIf="selectedAddonPrice > 0"> <span class="price-circle"> <svg
                            class="fa-solid fa-circle"></svg></span>
                    {{ selectedAddonPrice | currency: "GBP" }} </span>
            </button>
        </div>
    </div>
</ng-template>

<!-- Allergy-Modal -->
<ng-template #allergyModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Allergy Information</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center" *ngIf="restaurant?.allergy_message">{{restaurant?.allergy_message}}</p>
            <div class="text-center mt-3">
                <a class="btn modal-black-btn" type="button" href="tel:(+44) {{ restaurant?.restaurant_phone }};"
                    (click)="modal.dismiss('Cross click')">
                    <svg class="fa-solid fa-phone me-2 pe-sm-1"></svg>Call Store
                </a>
            </div>
        </div>
    </div>
</ng-template>

<!-- Item Not Available Modal -->
<ng-template #itemNotAvailableModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" (click)="handleItemNotAvailable('no')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Not Available</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center"*ngIf="hasOrderTypeMismatch && !hasDayMismatch">Some items in your cart are not available for the selected order type.</p>
            <p class="text-center"*ngIf="hasDayMismatch && !hasOrderTypeMismatch">Some items are not available on the selected day.</p>
            <p class="text-center"*ngIf="hasOrderTypeMismatch && hasDayMismatch">Some items are not available for the selected order type or delivery day.</p>
            <div class="d-flex justify-content-center pt-2">
                <button class="btn modal-black-btn" (click)="handleItemNotAvailable('no')">No</button>
                <button class="btn modal-black-btn" (click)="handleItemNotAvailable('yes')">Yes</button>
            </div>
        </div>
    </div>
</ng-template>

<!-- Item Refresh Modal -->
<ng-template #itemModal let-modal>
    <div id="items-already-in-cart-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Items already in cart</h5>
        </div>
        <div class="modal-body product-body">
            <p>Your cart contains items from other restaurant. Would you like to reset your cart for adding items from
                this restaurant?</p>

            <div class="d-flex justify-content-center pt-2">
                <button class="btn modal-black-btn" (click)="modal.dismiss('Cross click')">Close</button>
                <button class="btn modal-black-btn" (click)="cartempty()">Yes,Start Fresh</button>
            </div>
        </div>
    </div>
</ng-template>

<!-- Close Restaurant Modal -->
<ng-template #closeRetaurantModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Restaurant Closed</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center">This store is currently closed and is not accepting orders at the moment.</p>
            <p class="text-center">They will accept orders during opening times.</p>
            <button class="btn mt-3 modal-black-btn" type="button" (click)="modal.dismiss('Cross click')">
                See Store
            </button>
        </div>
    </div>
</ng-template>

<!-- Close Restaurant Modal -->
<ng-template #preRetaurantModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Restaurant Closed</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center">This store is currently closed and is not accepting orders at the moment.</p>
            <p class="text-center">You can schedule an order to be delivered or collected after their opening hours.</p>
            <button class="btn mt-3 modal-black-btn" type="button" (click)="modal.dismiss('Cross click')">
                Schedule an order
            </button>
        </div>
    </div>
</ng-template>

<!-- Unavailable Restaurant Modal -->
<ng-template #currentUnavailableRetaurantModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Restaurant Unavailable</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center">This store is currently closed and is not accepting orders at the moment.</p>
            <button class="btn mt-3 modal-black-btn" type="button" (click)="modal.dismiss('Cross click')">
                See Store
            </button>
        </div>
    </div>
</ng-template>


<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>