::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.product-details-left {
  max-width: 100%;
}

.product-rating-section {
  padding-top: 62px;
}

.make-reservation-box {
  padding: 35px 50px 40px 50px;
  background-color: #fff;
  border: 3px solid #F4F3F3;
  border-radius: 15px;
  width: calc(100% - 40px);
}

.make-reservation-box .main-heading {
  margin-bottom: 0;
}

.make-reservation-box h6 {
  font-family: 'Fredoka';
  font-size: 30px;
  text-align: center;
  font-weight: 600;
  line-height: 29px;
}

.make-reservation-box .form-group {
  margin-top: 38px;
  margin-bottom: 10px;
}

.make-reservation-box .form-group label {
  font-family: 'Visby CF';
  font-size: 20px;
  color: #000;
  font-weight: 700;
  line-height: 20px;
  padding-bottom: 18px;
}

.make-reservation-box .form-group input.form-control {
  font-size: 18px;
  color: #000000;
  font-weight: 700;
  padding: 4px 18px 7px;
  height: 45px;
  border-radius: 10px
}

.make-reservation-box .form-group.select-box {
  margin-top: 0;
}

.make-reservation-box .form-group.select-box::before {
  top: 9px;
  right: 26px;
  color: #000000;
}

.make-reservation-box .form-group select.form-control {
  padding: 4px 60px 7px 18px;
  height: 45px;
}

.make-reservation-box .instructions-box textarea.form-control {
  padding: 10px 15px;
  height: 120px;
  resize: none;
}

.make-reservation-box button.btn {
  font-size: 18px;
  font-weight: 700;
  font-family: 'Visby CF';
  width: 205px;
  margin: auto;
  display: table;
  margin-top: 20px;
}

.product-main-box::before {
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
}

.reservation-restaurant-title {
  padding: 30px 0 0 35px;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
}

.reservation-restaurant-title h5 {
  color: #fff;
  margin-bottom: 0;
}

.product-main-box .product-logo {
  z-index: 1;
}

#allergy-popup button.modal-black-btn {
  width: 180px;
  margin: auto;
  display: table;
  height: 46px;
  padding: 5px 10px;
}

#allergy-popup .modal-body p {
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 20px;
}

.view-menu-btn {
  font-family: 'Visby CF';
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  width: 185px;
  height: 42px;
  line-height: 38px;
  text-align: center;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
  border: 2px solid #fff;
  transition: all 0.5s;
  box-shadow: 0px 3px 6px 2.25px #00000040;
  background-color: #fff;
  position: absolute;
  right: 40px;
  bottom: 25px;
}

.view-menu-btn:hover {
  color: #ea3323;
  border-color: #ea3323;
}

.reservation-request-title {
  font-family: 'Visby CF';
  font-size: 25px;
  color: #000000;
  font-weight: 800;
  line-height: 22px;
  padding: 40px 0 30px 0;
}

.reservation-request-description {
  font-family: 'Visby CF';
  font-size: 18px;
  color: #000000;
  font-weight: 700;
  line-height: 22px;
  margin-bottom: 20px;
}

img.request-sent-icon {
  width: 90px;
}

.reservation-id {
  margin-bottom: 30px;
}

.reservation-id p {
  font-size: 20px;
  margin-bottom: 0;
}

.reservation-id p strong {
  color: #000;
}

.make-reservation-box .table-responsive {
  width: 100%;
  margin-top: 30px;
}

.table-responsive table {
  max-width: 400px;
  width:100%;
  margin:0 auto;
}

.table-responsive table tr td {
  font-size: 18px;
  color: #000;
  font-weight: 700;
  width: 50%;
  text-align: left;
  border: 0;  
  padding: 7px 10px;
}

.table-responsive table tr td:first-child {  
  color:#8F8F8A;
  white-space: nowrap;
}

#number-verification-popup .modal-body {
  padding: 80px 30px 30px 30px;
}

.modal-body img.verification-icon {
  height: 80px;
  margin: auto;
  display: flex;
  margin-bottom: 20px;
}

#number-verification-popup .modal-body h6 {
  font-size: 26px;
  font-weight: 800;
  font-family: 'Visby CF';
  text-align: center;
}

#number-verification-popup .modal-body p {
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  margin-bottom: 35px;
}

.modal-body .form-group {
  margin-bottom: 15px !important;
}

.modal-body .form-group input.form-control {
  padding: 4px 20px 7px;
  height: 50px;
}

#number-verification-popup .modal-body span {
  font-size: 15px;
  font-weight: 700;
  color: #8f8f8a;
  text-align: center;
  display: inline-block;
  width: 100%;
}

#number-verification-popup .more-option {
  display: flex;
  justify-content: center;
  margin: 0;
  padding-top: 15px;
}

#number-verification-popup .more-option li {
  padding: 0 5px;
}

#number-verification-popup .more-option li svg {
  color: #d7d7d7;
  font-size: 4px;
  position: relative;
  top: -4px;
}

#number-verification-popup .more-option li a {
  font-size: 16px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

.modal-footer {
  padding: 0 30px 30px 30px;
}

.modal-footer button.btn {
  margin: 0;
  width: 100%;
}

.forgot-password {
  padding-bottom: 25px;
}

.forgot-password span {
  font-size: 18px;
  color: #fc353a;
  font-family:'Visby CF';
  font-weight:600;
  text-decoration: none; 
}

.form-group label.form-label {
  font-size: 18px;
  color: #212529;
  font-weight: 700;
}

.modal-body .form-group .form-otp-list {
  display: flex;
}

.modal-body .form-group .form-otp-list input.form-control {
  width: 48px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
  margin-right: 8px;
}

.resend-code {
  padding-top: 15px;
}

.resend-code a {
  font-size: 18px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}
.opacity-resend {
  opacity: 0.5;
}
#opening-times{
  padding-top:0;
}
#opening-times .nav.nav-tabs{
  margin: 10px 0;
  border:0;
  flex-wrap: nowrap;
  padding: 5px;
  border-radius: 50px;
  background-color: #f4f3f3;  
}
#opening-times .nav.nav-tabs li{
  width:100%;
  margin:0;
}
#opening-times .nav.nav-tabs li button{
  font-size: 16px;
  padding:9px 15px;
  font-weight:800;
  border:0;
  border-radius: 25px;
  width: 100%;
  text-align: center;  
}
#opening-times .nav.nav-tabs li.active button{  
  background-color:#ea3323;
  color:#fff;
}
.back-to-login{
  font-size: 18px;  
  font-family: 'Visby CF';
  font-weight: 600;  
  text-align: center;
  padding-top: 20px;
}
.back-to-login span{
  color:#000;
  padding-right: 5px;
}
.back-to-login a{
  color: #fc353a;
  text-decoration: none;
}

.step-box {
  padding: 17px 100px;
  // border: 2px solid #F8F7F7;
  border-radius: 20px;
  position: relative;
  margin-bottom: 15px;
}

.step-box .step-number {
  font-family: 'Fredoka';
  font-size: 25px;
  color: #000000;
  font-weight: 500;
  text-align: center;
  width: 42px;
  height: 42px;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
  background-color: #F4F3F3;
  position: absolute;
  top: 14px;
  left: 30px;
}

.step-box h5.title {
  font-family: 'Fredoka';
  font-size: 30px;
  color: #000000;
  font-weight: 500;
  line-height: 36px;
  text-align: center;
  margin-bottom: 0;
}

.step-box .success-icon {
  position: absolute;
  top: 14px;
  right: 40px;
}

.step-box .success-icon img {
  width: 42px;
}


.accordion .accordion-item {
  border: 0;
  border-radius: 0;
}

.accordion-header button {
  font-family: 'Visby CF';
  font-size: 18px;
  color: #000000;
  padding: 14px 0;
  display: inline-block;
  width: 100%;
  text-decoration: none;
  font-weight: 800;
  position: relative;
  background-color: transparent;
  border:0;
  outline: none;
}

.accordion-header button.add-card-option::before {
  width: 25px;
  height: 25px;
  text-align: center;
  font-size: 15px;
  color: #000000;
  position: absolute;
  content: "\f078";
  font-family: 'fontawesome';
  right: 0;
  top: 17px;
  transition: all 0.5s;
  transform: rotate(-90deg);
}

// .accordion-header button.collapsed span.check {
//   display: none;
// }

.accordion-header button span.check {
  position: absolute;
  left: -35px;
}

.accordion-header button span.check img {
  width: 20px;
}

.step-body-box {
  padding-top: 20px;
  max-width: 490px;
  margin: auto;
}

.step-body-box .form-group span.icon {
  top: 10px;
}

.step-body-box input.form-control {
  height: 50px;
  padding-left: 20px;
  padding-right: 20px;
}

.step-body-box .select-box .form-control {
  height: 50px;
}

.step-body-box .select-box::before {
  font-size: 16px;
  top: 12px;
  right: 30px;
}

.step-body-box .select-box .form-control {
  padding-left: 20px;
}

.step-body-box .form-group textarea.textarea-msg {
  height: 200px;
  padding: 15px 20px;
  resize: none;
}

.next-btn button.btn {
  font-size: 18px;
  width: 100%;
  padding: 7px 20px;
  height: 45px;
}

.step-body-box .or-option span {
  color: #8F8F8A;
}

.step-body-box .or-option::after {
  background-color: #8F8F8A;
}

.step-body-box .accordion {
  padding-top: 5px;
  padding-bottom: 5px;
}

.step-body-box .accordion-body {
  padding: 0;
}
.step-body-box .accordion #creditpaymentStripe .form-group .form-control{
  padding:8px 15px;
}
.credit-card-list {
  margin-bottom: 0;
}

.credit-card-list li {
  display: inline-block;
  margin-right: 10px;
}

.credit-card-list li img {
  width: 40px;
}

.next-btn button.btn {
  font-size: 18px;
  width: 100%;
  padding: 7px 20px;
  height: 45px;
}

@media screen and (max-width: 1500px) {
  .step-box {
    padding: 5px 30px;
    border-radius: 15px;
    margin-bottom: 10px;
  }

  .step-box .step-number {
    font-size: 18px;
    width: 32px;
    height: 32px;
    line-height: 30px;
    top: 10px;
    left: 22px;
  }

  .step-box h5.title {
    font-size: 22px;
    line-height: 42px;
  }

  .step-box .success-icon {
    top: 10px;
    right: 30px;
  }

  .step-box .success-icon img {
    width: 22px;
  }

  .step-box span.account-email {
    font-size: 11px;
    line-height: normal;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 3px;
  }

  .accordion-header button {
    font-size: 14px;
    padding: 6px 0;
  }
  .accordion-header button.add-card-option svg{
    position: relative;
    top: 1px;
  }
  .accordion-header button span.check {
    left: -24px;
  }

  .accordion-header button span.check img {
    width: 15px;
  }

  .step-body-box .accordion-body img {
    width: 34px;
  }

  .step-body-box .accordion-body .credit-card-list li img {
    width: 24px;
  }

  .accordion-header button.add-card-option::before {
    width: 15px;
    height: 15px;
    font-size: 11px;
    top: 9px;
  }

  .step-body-box {
    padding-top: 5px;
    padding-bottom: 15px;
  }

  .step-body-box input.form-control {
    font-size: 13px;
    border-radius: 7px;
    padding-left: 10px;
    padding-right: 10px;
    padding: 6px 10px 7px;
    height: 30px;
  }

  .step-body-box .form-group span.icon {
    top: 1px;
  }

  .step-body-box button.btn {
    font-size: 18px;
    padding: 4px 15px;
    height: 35px;
    font-weight: 800;
  }

  .step-body-box .accordion-body img {
    width: 34px;
  }

  .product-rating-section {
    padding-top: 45px;
  }

  .make-reservation-box {
    padding: 25px 35px 30px;
    border: 2px solid #f4f3f3;
    border-radius: 12px;
    width: calc(100% - 30px);
  }

  .make-reservation-box h6 {
    font-size: 22px;
    line-height: 22px;
  }

  .make-reservation-box .form-group {
    margin-top: 20px;
  }

  .make-reservation-box .form-group label {
    font-size: 15px;
    padding-bottom: 10px;
  }

  .make-reservation-box .form-group input.form-control {
    font-size: 13px;
    padding: 4px 12px 5px;
    height: 34px;
    border-radius: 8px;
  }

  .make-reservation-box .form-group.select-box:before {
    font-size: 13px;
    top: 8px;
    right: 15px;
  }

  .make-reservation-box .form-group select.form-control {
    font-size: 14px;
    height: 34px;
    padding: 5px 50px 5px 12px;
    border-radius: 8px;
  }

  .make-reservation-box .instructions-box textarea.form-control {
    font-size: 13px;
  }

  .make-reservation-box button.btn {
    font-size: 13px;
    width: 155px;
    margin-top: 15px;
    padding: 4px 10px;
  }

  .reservation-restaurant-title {
    padding: 25px 0 0 30px;
  }

  .reservation-restaurant-title h5 {
    font-size: 34px;
  }

  #allergy-popup .modal-body p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 30px;
  }

  #allergy-popup button.modal-black-btn {
    width: 160px;
    height: 36px;
    font-size: 14px;
  }

  #allergy-popup button.modal-black-btn svg {
    font-size: 14px;
  }

  .view-menu-btn {
    font-size: 14px;
    width: 165px;
    height: 38px;
    line-height: 35px;
    right: 40px;
    bottom: 25px;
  }

  .reservation-request-title {
    font-size: 19px;
    padding: 30px 0 20px;
  }

  .reservation-request-description {
    font-size: 15px;
    margin-bottom: 15px;
  }

  img.request-sent-icon {
    width: 75px;
  }

  .reservation-id {
    margin-bottom: 20px;
  }

  .reservation-id p {
    font-size: 15px;
  }
  .make-reservation-box .table-responsive {
    margin-top: 20px;
  }
  .table-responsive table {
    max-width: 300px;
  }
  .table-responsive table tr td {
    font-size: 15px;    
  }

  #number-verification-popup .modal-body {
    padding: 50px 20px 20px;
  }

  #number-verification-popup .modal-body h6 {
    font-size: 18px;
  }

  #number-verification-popup .modal-body p {
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  .modal-body .add-btn a.btn {
    font-size: 14px;
    padding: 4px 15px;
  }

  .modal-body .form-group input.form-control {
    padding: 5px 15px 6px;
    height: 36px;
  }

  #number-verification-popup .modal-body span {
    font-size: 13px;
  }

  #number-verification-popup .more-option li a {
    font-size: 13px;
  }

  .modal-body img.verification-icon {
    height: 54px;
    margin-bottom: 10px;
  }

  .form-group label.form-label {
    font-size: 14px;
  }

  .modal-body .form-group .form-otp-list input.form-control {
    width: 36px;
  }
  .forgot-password{
    padding-bottom: 20px;
  }
  .forgot-password span {
    font-size: 14px;
  }
  .resend-code {
    padding-top: 10px;
  }
  .resend-code a {
    font-size: 14px;
  }
  #opening-times .nav.nav-tabs li button {
    font-size: 14px;
    padding: 7px 10px;
  }
  .back-to-login{
    font-size: 14px;
    padding-top: 15px;
  }  

  .next-btn button.btn {
    font-size: 18px;
    font-family:'Visby CF';
    font-weight: 800;
    height: 35px;
    padding:4px 15px;
    line-height: 20px;
  }
}

@media screen and (min-width: 1200px) {
  .product-main-box-section .container {
    max-width: 1310px;
  }

}


@media screen and (max-width: 991px) {
  .make-reservation-box {
    width: 100%;
    margin-bottom: 30px;
  }

  .make-reservation-box .form-group {
    margin-bottom: 0;
  }

  .view-menu-btn {
    font-size: 14px;
    width: 150px;
    height: 36px;
    line-height: 33px;
    right: 30px;
    bottom: 20px;
  }

}

@media screen and (max-width:767px) {
  .accordion-header button span.check {
    left: -20px;
  }

  .accordion-header button span.check img {
    width: 13px;
  }
}

@media screen and (max-width: 575px) {
  .reservation-restaurant-title {
    padding: 20px 0 0 20px;
  }

  .reservation-restaurant-title h5 {
    font-size: 28px;
  }

  #allergy-popup .modal-header {
    padding-left: 50px;
    padding-right: 50px;
  }

}

@media screen and (max-width: 480px) {

  .step-box {
    padding: 5px 15px;
    border-radius: 10px;
  }

  .step-box .step-number {
    font-size: 14px;
    width: 28px;
    height: 28px;
    line-height: 28px;
    left: 12px;
  }

  .step-box .success-icon {
    right: 12px;
  }

  .step-box .success-icon img {
    width: 20px;
  }

  .accordion-header button span.check {
    left: -12px;
  }

  .accordion-header button span.check img {
    width: 10px;
    margin-top: -2px;
  }

  .step-body-box .form-group textarea.textarea-msg {
    height: 150px;
    padding: 10px 20px;
  }

  .make-reservation-box {
    padding: 20px;
  }

  .make-reservation-box .form-group {
    margin-top: 20px;
  }

  .reservation-restaurant-title {
    padding: 15px 0 0 20px;
  }

  .reservation-restaurant-title h5 {
    font-size: 24px;
  }

  .product-main-box:before {
    border-radius: 15px;
  }

  .view-menu-btn {
    font-size: 13px;
    width: 130px;
    height: 34px;
    line-height: 30px;
    right: 15px;
    bottom: 15px;
  }

  .credit-card-list li img {
    width: 25px;
  }

}
