import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild, ElementRef, AfterViewChecked, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { NotificationService } from '../../../core/services/notification.service';
import { User } from '../../../core/models/user';
import { Faq } from '../../../core/models/faq';
import { FaqService } from '../../../core/services/faq.service';

interface ChatMessage {
  id: number;
  text: string;
  type: 'sent' | 'received';
  timestamp: Date;
}

@Component({
  selector: 'app-help',
  host: { ngSkipHydration: 'true' },
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss'],
})
export class HelpComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('chatBody') chatBody: ElementRef;
  @ViewChild('messageInput') messageInput: ElementRef;

  subs = new Subscription();

  user: User;
  faqs: Faq[] = [];

  expandableId: string;
  modalOptions: NgbModalOptions;
  previousPage: any;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isModelPaymentLoading = false; ModelPaymenterror = null;

  options = { disabled: 0, nopaginate: 1, query: null, page: 'go-grubz', customer_id: null };

  // Chat-related properties
  isChatOpen = false;
  isChatMinimized = false;
  chatMessages: ChatMessage[] = [];
  newMessage = '';
  isTyping = false;
  unreadMessages = 0;
  isDropdownOpen = false;
  private messageIdCounter = 1;
  private shouldScrollToBottom = false;

  // Order help flow properties
  showOrderHelp = false;
  showOrderSelection = false;
  showOrderDetails = false;
  showIssueSelection = false;
  showIssueDetails = false;
  showDetailedInput = false;
  selectedOrder: any = null;
  selectedIssue: string = '';
  detailMessage = '';
  selectedFiles: File[] = [];
  userOrders: any[] = [];
  issueTypes: string[] = [
    'Order is late',
    'Missing item',
    'Incorrect order',
    'Quality/Hygiene issue',
    'Didn\'t receive my order',
    'Incorrectly charged',
    'Other'
  ];
  orderIssues = [
    { id: 'not-delivered', title: 'Order not delivered', icon: 'fa-truck' },
    { id: 'wrong-items', title: 'Wrong items received', icon: 'fa-exchange-alt' },
    { id: 'missing-items', title: 'Missing items', icon: 'fa-minus-circle' },
    { id: 'quality-issue', title: 'Food quality issue', icon: 'fa-star' },
    { id: 'late-delivery', title: 'Late delivery', icon: 'fa-clock' },
    { id: 'refund-request', title: 'Refund request', icon: 'fa-money-bill-wave' },
    { id: 'other', title: 'Other issue', icon: 'fa-question-circle' }
  ];

  // Reservation help flow properties
  showReservationHelp = false;
  showReservationSelection = false;
  showReservationDetails = false;
  showReservationActions = false;
  selectedReservation: any = null;
  userReservations: any[] = [];
  reservationActions = [
    { id: 'modify-time', title: 'Change time', icon: 'fa-clock', description: 'Modify your reservation time' },
    { id: 'modify-guests', title: 'Change party size', icon: 'fa-users', description: 'Update number of guests' },
    { id: 'special-requests', title: 'Special requests', icon: 'fa-comment', description: 'Add dietary requirements or preferences' },
    { id: 'cancel-reservation', title: 'Cancel reservation', icon: 'fa-times-circle', description: 'Cancel your booking' },
    { id: 'contact-restaurant', title: 'Contact restaurant', icon: 'fa-phone', description: 'Speak directly with the restaurant' },
    { id: 'view-menu', title: 'View menu', icon: 'fa-utensils', description: 'Check the restaurant menu' }
  ];

  constructor(
    public userService: UserService,
    public faqService: FaqService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    this.fetchFaqs();
    this.initializeChat();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  fetchFaqs() {
    this.isLoading = true; this.error = null;

    this.subs.add(
      this.faqService.home(this.options)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.faqs = res;
            this.expandableId = this.faqs[0].id;
          },
          (err) => {
            this.faqs = [];
          }
        )
    );
  }

  clickOnAccordion(faqId) {
    if (faqId == this.expandableId) {
      faqId = null;
    }
    this.expandableId = faqId;
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  // Chat-related methods
  initializeChat(): void {
    // Add welcome message
    this.chatMessages = [
      {
        id: this.messageIdCounter++,
        text: `Hello ${this.user?.first_name || 'John'}, what can we help you with today?`,
        type: 'received',
        timestamp: new Date()
      }
    ];
  }

  openCustomerSupportChat(): void {
    this.isChatOpen = true;
    this.isChatMinimized = false;
    this.unreadMessages = 0;
    this.shouldScrollToBottom = true;

    // Focus on input after a short delay to ensure the chat is fully opened
    setTimeout(() => {
      if (this.messageInput?.nativeElement) {
        this.messageInput.nativeElement.focus();
      }
    }, 300);
  }

  closeChatBox(): void {
    this.isChatOpen = false;
    this.isChatMinimized = false;
  }

  minimizeChat(): void {
    this.isChatMinimized = true;
    this.isChatOpen = false;
  }

  maximizeChat(): void {
    this.isChatOpen = true;
    this.isChatMinimized = false;
    this.unreadMessages = 0;
    this.shouldScrollToBottom = true;

    setTimeout(() => {
      if (this.messageInput?.nativeElement) {
        this.messageInput.nativeElement.focus();
      }
    }, 100);
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  emailTranscript(): void {
    // Generate email transcript
    const transcript = this.chatMessages.map(msg =>
      `${msg.type === 'sent' ? 'You' : 'Support'}: ${msg.text} (${msg.timestamp.toLocaleString()})`
    ).join('\n');

    const subject = 'Go Grubz Chat Transcript';
    const body = `Chat Transcript:\n\n${transcript}`;
    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    window.open(mailtoLink);
    console.log('Email transcript requested');
  }

  sendFeedback(): void {
    // Add feedback message to chat
    const feedbackMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: 'Thank you for wanting to provide feedback! Please share your thoughts about our support service.',
      type: 'received',
      timestamp: new Date()
    };

    this.chatMessages.push(feedbackMessage);
    this.shouldScrollToBottom = true;
    console.log('Feedback option selected');
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdownContainer = target.closest('.dropdown-container');

    if (!dropdownContainer && this.isDropdownOpen) {
      this.closeDropdown();
    }
  }

  sendMessage(): void {
    if (!this.newMessage.trim()) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: this.newMessage.trim(),
      type: 'sent',
      timestamp: new Date()
    };

    this.chatMessages.push(userMessage);
    this.newMessage = '';
    this.shouldScrollToBottom = true;
    // Show typing indicator
    this.isTyping = true;
    this.scrollToBottom();

    // Simulate support response
    setTimeout(() => {
      this.isTyping = false;
      const supportResponse = this.generateSupportResponse(userMessage.text);

      const supportMessage: ChatMessage = {
        id: this.messageIdCounter++,
        text: supportResponse,
        type: 'received',
        timestamp: new Date()
      };

      this.chatMessages.push(supportMessage);
      this.shouldScrollToBottom = true;

      // If chat is minimized, increment unread counter
      if (this.isChatMinimized) {
        this.unreadMessages++;
      }
      this.scrollToBottom();
    }, 1500 + Math.random() * 1000); // Random delay between 1.5-2.5 seconds
  }

  sendQuickMessage(message: string): void {
    if (message === 'Help with an order') {
      this.startOrderHelp();
    } else if (message === 'My Reservations') {
      this.startReservationHelp();
    } else {
      this.newMessage = message;
      this.sendMessage();
    }
  }

  startOrderHelp(): void {
    this.showOrderHelp = true;
    this.showOrderSelection = true;
    this.loadUserOrders();

    const sentHelpMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: 'Help with an order',
      type: 'sent',
      timestamp: new Date()
    };

    this.chatMessages.push(sentHelpMessage);
    this.shouldScrollToBottom = true;

    // Add initial message
    const helpMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: 'Sorry to hear you\'ve been having trouble with your order. Please select which order you need help with.',
      type: 'received',
      timestamp: new Date()
    };

    this.chatMessages.push(helpMessage);
    this.shouldScrollToBottom = true;
  }

  loadUserOrders(): void {
    // Mock order data - replace with actual service call
    this.userOrders = [
      {
        id: 'ORD-001',
        restaurantName: 'Sizzler House',
        items: ['Grilled Chicken', 'Fries'],
        total: 90.22,
        status: 'Completed',
        orderDate: new Date('2024-05-27'),
        image: 'assets/images/offer-image-one.png'
      },
      {
        id: 'ORD-002',
        restaurantName: 'Major Curry Burntwood',
        items: ['Chicken Curry', 'Rice', 'Naan', 'Drink'],
        total: 45.00,
        status: 'Completed',
        orderDate: new Date('2024-05-14'),
        image: 'assets/images/offer-image-one.png'
      },
      {
        id: 'ORD-003',
        restaurantName: 'Sizzler House',
        items: ['Grilled Chicken', 'Fries'],
        total: 90.22,
        status: 'Completed',
        orderDate: new Date('2024-05-07'),
        image: 'assets/images/offer-image-one.png'
      }
    ];
  }

  selectOrder(order: any): void {
    this.selectedOrder = order;

    // Add support response asking about the issue
    setTimeout(() => {
      const orderMessage: ChatMessage = {
        id: this.messageIdCounter++,
        text: 'Thanks, now please tell us what the issue is with this order.',
        type: 'received',
        timestamp: new Date()
      };

      this.chatMessages.push(orderMessage);
      this.shouldScrollToBottom = true;
      this.showIssueSelection = true;
    }, 1000);
  }

  private generateSupportResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('order') || lowerMessage.includes('delivery')) {
      return "I'd be happy to help you with your order! Could you please provide your order number so I can look into this for you?";
    } else if (lowerMessage.includes('payment') || lowerMessage.includes('card') || lowerMessage.includes('charge')) {
      return "I understand you're having payment issues. Let me help you resolve this. Can you tell me more about the specific problem you're experiencing?";
    } else if (lowerMessage.includes('account') || lowerMessage.includes('profile') || lowerMessage.includes('login')) {
      return "I can help you with your account. What specific account issue are you experiencing? Are you having trouble logging in or updating your information?";
    } else if (lowerMessage.includes('refund') || lowerMessage.includes('cancel')) {
      return "I can assist you with refunds and cancellations. Please provide your order details and I'll check what options are available for you.";
    } else if (lowerMessage.includes('restaurant') || lowerMessage.includes('menu')) {
      return "For restaurant-specific questions or menu inquiries, I can help connect you with the right information. What would you like to know?";
    } else {
      return "Thank you for reaching out! I'm here to help. Could you please provide more details about your inquiry so I can assist you better?";
    }
  }

  selectIssue(issue: string): void {
    this.selectedIssue = issue;
    this.showIssueSelection = false;
    this.showOrderSelection = false;

    // Add user message
    const userMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: issue,
      type: 'sent',
      timestamp: new Date()
    };

    this.chatMessages.push(userMessage);
    this.shouldScrollToBottom = true;

    // Add support response based on issue type
    setTimeout(() => {
      let responseText = '';

      if (issue === 'Quality/Hygiene issue') {
        responseText = 'Sure, could you describe your issue to us in a bit more detail? Include any images/videos you may have.';
        this.showDetailedInput = true;
      } else {
        responseText = `Thank you for letting us know about the "${issue}" issue. We'll help you resolve this right away. Our team will investigate and get back to you shortly.`;
      }

      const issueMessage: ChatMessage = {
        id: this.messageIdCounter++,
        text: responseText,
        type: 'received',
        timestamp: new Date()
      };

      this.chatMessages.push(issueMessage);
      this.shouldScrollToBottom = true;
    }, 1000);
  }

  provideIssueHelp(issueType: string): void {
    let helpText = '';

    switch (issueType) {
      case 'not-delivered':
        helpText = 'I can see your order was marked as delivered. Let me check with the delivery partner and restaurant. In the meantime, I\'ll process a full refund for you.';
        break;
      case 'wrong-items':
        helpText = 'I apologize for the wrong items. I\'ll arrange for the correct items to be delivered and you can keep the wrong items as compensation.';
        break;
      case 'missing-items':
        helpText = 'I can see some items are missing from your order. I\'ll arrange for the missing items to be delivered immediately at no extra cost.';
        break;
      case 'quality-issue':
        helpText = 'I\'m sorry the food quality wasn\'t up to standard. I\'ll process a full refund and also provide a discount voucher for your next order.';
        break;
      case 'late-delivery':
        helpText = 'I apologize for the delayed delivery. I\'ll provide you with a refund for delivery charges and a 20% discount on your next order.';
        break;
      case 'refund-request':
        helpText = 'I\'ll process your refund request immediately. The refund will be credited to your original payment method within 3-5 business days.';
        break;
      default:
        helpText = 'Please describe your issue in detail and I\'ll do my best to help you resolve it quickly.';
    }

    setTimeout(() => {
      const solutionMessage: ChatMessage = {
        id: this.messageIdCounter++,
        text: helpText,
        type: 'received',
        timestamp: new Date()
      };

      this.chatMessages.push(solutionMessage);
      this.shouldScrollToBottom = true;
    }, 1000);
  }

  backToOrderSelection(): void {
    this.showOrderDetails = false;
    this.showOrderSelection = true;
    this.selectedOrder = null;
  }

  backToIssueSelection(): void {
    this.showIssueDetails = false;
    this.showOrderDetails = true;
    this.selectedIssue = '';
  }

  closeOrderHelp(): void {
    this.showOrderHelp = false;
    this.showOrderSelection = false;
    this.showOrderDetails = false;
    this.showIssueSelection = false;
    this.showIssueDetails = false;
    this.showDetailedInput = false;
    this.selectedOrder = null;
    this.selectedIssue = '';
    this.detailMessage = '';
    this.selectedFiles = [];
  }

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files) {
      for (let i = 0; i < files.length && this.selectedFiles.length < 10; i++) {
        const file = files[i];
        if (file.size <= 5 * 1024 * 1024) { // 5MB limit
          this.selectedFiles.push(file);
        }
      }
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  sendDetailedMessage(): void {
    if (this.detailMessage.trim()) {
      // Add user message
      const userMessage: ChatMessage = {
        id: this.messageIdCounter++,
        text: this.detailMessage,
        type: 'sent',
        timestamp: new Date()
      };

      this.chatMessages.push(userMessage);
      this.shouldScrollToBottom = true;

      // Add support response
      setTimeout(() => {
        const responseMessage: ChatMessage = {
          id: this.messageIdCounter++,
          text: 'Thank you for providing the details. We have received your report and our team will investigate this quality/hygiene issue immediately. We will get back to you within 24 hours with a resolution.',
          type: 'received',
          timestamp: new Date()
        };

        this.chatMessages.push(responseMessage);
        this.shouldScrollToBottom = true;

        // Hide detailed input
        this.showDetailedInput = false;
        this.detailMessage = '';
        this.selectedFiles = [];
      }, 1000);
    }
  }

  // Reservation Help Methods
  startReservationHelp(): void {
    this.showReservationHelp = true;
    this.showReservationSelection = true;
    this.loadUserReservations();

    // Add initial message
    const helpMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: 'I can help you with your reservations. Here are your upcoming and recent bookings.',
      type: 'received',
      timestamp: new Date()
    };

    this.chatMessages.push(helpMessage);
    this.shouldScrollToBottom = true;
  }

  loadUserReservations(): void {
    // Mock reservation data - replace with actual service call
    this.userReservations = [
      {
        id: 'RES-001',
        restaurantName: 'Sizzler House',
        customerName: 'John Brook',
        date: new Date('2024-05-27'),
        time: '19:30 PM',
        guests: 5,
        status: 'Confirmed',
        address: '123 Main Street, Downtown',
        phone: '+****************',
        image: 'assets/images/default-food.jpg',
        specialRequests: 'Window table preferred'
      },
      {
        id: 'RES-002',
        restaurantName: 'Major Curry Burntwood',
        customerName: 'John Brook',
        date: new Date('2024-05-27'),
        time: '19:30 PM',
        guests: 5,
        status: 'Confirmed',
        address: '456 Oak Avenue, Midtown',
        phone: '+****************',
        image: 'assets/images/default-food.jpg',
        specialRequests: ''
      },
      {
        id: 'RES-003',
        restaurantName: 'Sizzler House',
        customerName: 'John Brook',
        date: new Date('2024-05-27'),
        time: '19:30 PM',
        guests: 5,
        status: 'Confirmed',
        address: '789 Pine Street, Uptown',
        phone: '+****************',
        image: 'assets/images/default-food.jpg',
        specialRequests: 'Birthday celebration'
      }
    ];
  }

  selectReservation(reservation: any): void {
    this.selectedReservation = reservation;
    this.showReservationSelection = false;
    this.showReservationDetails = true;

    const reservationMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: `You selected your reservation at ${reservation.restaurantName} for ${reservation.date.toLocaleDateString()} at ${reservation.time}. How can I help you with this booking?`,
      type: 'received',
      timestamp: new Date()
    };

    this.chatMessages.push(reservationMessage);
    this.shouldScrollToBottom = true;
  }

  selectReservationAction(action: any): void {
    this.showReservationDetails = false;
    this.showReservationActions = true;

    let responseText = '';

    switch (action.id) {
      case 'modify-time':
        responseText = `I can help you change your reservation time. Your current booking is for ${this.selectedReservation.time} on ${this.selectedReservation.date.toLocaleDateString()}. What time would you prefer?`;
        break;
      case 'modify-guests':
        responseText = `Your current reservation is for ${this.selectedReservation.guests} guests. How many people will be joining you instead?`;
        break;
      case 'special-requests':
        responseText = `I can add special requests to your reservation. Do you have any dietary requirements, seating preferences, or special occasions to note?`;
        break;
      case 'cancel-reservation':
        responseText = `I can help you cancel your reservation at ${this.selectedReservation.restaurantName}. Please note that cancellation policies may apply. Would you like me to proceed with the cancellation?`;
        break;
      case 'contact-restaurant':
        responseText = `Here are the contact details for ${this.selectedReservation.restaurantName}:\n\nPhone: ${this.selectedReservation.phone}\nAddress: ${this.selectedReservation.address}\n\nYou can call them directly for any specific requests.`;
        break;
      case 'view-menu':
        responseText = `I'll help you view the menu for ${this.selectedReservation.restaurantName}. Let me fetch their current menu for you.`;
        break;
      default:
        responseText = 'How else can I help you with your reservation?';
    }

    const actionMessage: ChatMessage = {
      id: this.messageIdCounter++,
      text: responseText,
      type: 'received',
      timestamp: new Date()
    };

    this.chatMessages.push(actionMessage);
    this.shouldScrollToBottom = true;
  }

  backToReservationSelection(): void {
    this.showReservationDetails = false;
    this.showReservationSelection = true;
    this.selectedReservation = null;
  }

  backToReservationDetails(): void {
    this.showReservationActions = false;
    this.showReservationDetails = true;
  }

  closeReservationHelp(): void {
    this.showReservationHelp = false;
    this.showReservationSelection = false;
    this.showReservationDetails = false;
    this.showReservationActions = false;
    this.selectedReservation = null;
  }

  private scrollToBottom(): void {
    if (this.chatBody?.nativeElement) {
      const element = this.chatBody.nativeElement;
      element.scrollTop = element.scrollHeight;
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
