$(document).ready(function () {

  function toggleCategoryClass(scrollingCategoryId) {
    var categoryElement = $(scrollingCategoryId);
    var categoryElement = $(".menu-category-list , .filter-section-bg");
    if (categoryElement.length) {
      var categoryOffset = categoryElement.offset().top;
      var scrollPosition = $(window).scrollTop();
      
      if (scrollPosition >= categoryOffset) {
        categoryElement.addClass("fixed");
      } else {
        categoryElement.removeClass("fixed");
      }
    }
  }

  $(window).scroll(function () {
    var scrollingCategoryId = '#allMenu , #filter-section';
    toggleCategoryClass(scrollingCategoryId);
  });  

});
