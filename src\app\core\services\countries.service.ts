import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ErrorHandler } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { Countries } from '../models/countries';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CountriesService {
  private url = `${environment.apiBaseUrl}countries/`

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Countries> {
    return this.http.get<Countries>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(countries: Countries): Observable<Countries> {
    return this.http.post<Countries>(this.url, Countries.toFormData(countries))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(countries: Countries): Observable<Countries> {
    return this.http.post<Countries>(this.url + countries.id, Countries.toFormData(countries))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<Countries> {
    return this.http.delete<Countries>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  enable(id: string): Observable<Countries> {
    return this.http.post<Countries>(this.url + id + '/enable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  disable(id: string): Observable<Countries> {
    return this.http.post<Countries>(this.url + id + '/disable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
