
export class PromotionBanner {
  id: string;
  banner_image: string;
  banner_link: string;
  restaurant_id: number;
  restaurant_name: string;

  status: boolean;

  image_url: string;

  created_at: string;
  updated_at: string;

  imageFile: any;

  static toFormData(promotion_banner: PromotionBanner) {
    const formData = new FormData();

    if (promotion_banner.id) formData.append('id', promotion_banner.id);
    if (promotion_banner.restaurant_id) formData.append('restaurant_id', promotion_banner.restaurant_id.toString());
    if (promotion_banner.banner_link) formData.append('banner_link', promotion_banner.banner_link);
    if (promotion_banner.imageFile) formData.append('image', promotion_banner.imageFile, promotion_banner.imageFile.name)

    return formData;
  }
}
