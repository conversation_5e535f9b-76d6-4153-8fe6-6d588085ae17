.order-history-main-box p {
  color: #8F8F8A;
  font-family: 'Visby CF';
  font-size: 25px;
  font-weight: 700;
  margin-bottom: 10px;
}

.order-history-main-box p.requested-date {
  margin-bottom: 43px;
}

.order-history-main-box p.pending-text {
  color: #FD811F;
}

.order-history-main-box p.success-text {
  color: #14A411;
}

.order-history-main-box {
  margin-bottom: 70px;
}

.order-history-box {
  display: flex;
}

.order-image {
  max-width: 280px;
  width: 100%;
  min-height: 170px;
  max-height: 170px;
  position: relative;
}

.order-image img.order-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0px 0px 25px 5px #00000033;
}

.order-image .order-logo {
  width: 74px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: absolute;
  top: 16px;
  left: 16px;
  border-radius: 50%;
  box-shadow: 0px 4px 15px 0px #00000080;
  background-color: #FFFFFF;
}

.order-image .order-logo img {
  width: 100%;
  box-shadow: none;
}

.order-details {
  width: 100%;
  padding-left: 45px;
}

.order-title {
  display: flex;
  margin-bottom: 18px;
}

.order-title h6 {
  font-family: 'Fredoka';
  color: #000000;
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 0;
}

.booking-details ul.order-date-time {
  margin-bottom: 10px;
}

.booking-details ul.order-date-time li {
  color: #8F8F8A;
  font-family: 'Visby CF';
  font-size: 20px;
  font-weight: 700;
  display: inline-block;
  position: relative;
  padding-right: 15px;
  margin-right: 15px;
}

.booking-details ul.order-date-time li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.booking-details ul.order-date-time li::after {
  width: 4px;
  height: 4px;
  content: '';
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: -2px;
  background-color: #8F8F8A;
  border-radius: 50%;
}

.booking-details ul.order-date-time li:last-child:after {
  display: none;
}

.view-store ul {
  display: flex;
  margin-top: 40px;
  margin-bottom: 0;
}

.view-store ul li {
  display: inline-block;
  margin-left: 40px;
}

.view-store ul li:first-child {
  margin-left: 0;
}

.view-store button.btn {
  font-size: 18px;
  font-weight: 700;
  padding: 7px 15px;
  width: 150px;
  height: 40px;
  white-space: nowrap;
}

.dont-have-order {
  padding-top:200px;
  padding-bottom: 50px;
}

.dont-have-order p {
  font-size: 30px;
  color: #000000;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2px;
}

.dont-have-order span {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  display: block;
  padding-bottom: 40px;
}

.dont-have-order button.btn {
  font-size: 18px;
  font-weight: 700;
  padding: 7px 15px;
  width: 150px;
  height: 40px;
}

.text-success {
  color: #14a411 !important;
}

.text-danger {
  color: #fc353a !important;
}

.reservations-tab {
  padding: 8px 12px;
  max-width: 725px;
  width: 100%;
  margin: auto;
  margin-bottom: 85px;
  display: table;
  background-color: #fff;
  box-shadow: 0px 0px 20px 8px #0000001A;
  border-radius: 50px;
}

.reservations-tab ul {
  margin: 0;
  display: flex;
}

.reservations-tab ul li {
  width: 100%;
}

.reservations-tab ul li button {
  width: 100%;
  padding: 13px 20px;
  font-size: 28px;
  color: #EA3323;
  font-weight: 700;
  border:0;
  font-family: 'Visby CF';
  display: inline-block;
  text-decoration: none;
  text-align: center;
  background-color: #fff;
  border-radius: 50px;
}

.reservations-tab ul li.active button{
  color: #fff;
  background-color: #EA3323;
}

/*---review-popup-css---*/
.modal-body {
  padding: 20px 30px 30px 30px;
}
.modal-body .login-title {
  margin-bottom: 30px;
}
.modal-body .form-group {
  margin-bottom: 20px;
}
.modal-body .form-group textarea.form-control {
  font-size: 18px;
  padding: 10px 20px;
  height: 100px;
  resize: none;
}
.modal-body button.btn {
  width: 100%;
}
.reservation-id{
  margin-bottom: 10px;
}
.reservation-id p{
  font-size: 20px;  
  margin-bottom: 0;
}
.reservation-id p strong{
  color:#000;
}

@media screen and (max-width:1800px) {
  .order-title {
    margin-bottom: 10px;
  }

  .order-title h6 {
    min-width: 220px;
  }

  .order-details {
    padding-left: 40px;
  }

  .order-history-main-box p {
    font-size: 22px;
  }

  .booking-details ul.order-date-time {
    margin-bottom: 10px;
  }

  .booking-details ul.order-date-time li {
    font-size: 18px;
    padding-right: 10px;
    margin-right: 10px;
  }

  .view-store ul li {
    margin-left: 25px;
  }

  .view-store ul li:first-child {
    margin-left: 0;
  }

  .view-store button.btn {
    font-size: 16px;
    width: 140px;
  }
  .reservation-id p{
    font-size: 18px;
  }
}

@media screen and (max-width:1500px) {
  .reservations-tab {
    max-width: 545px;
    padding:6px 8px;
    margin-bottom: 65px;
  }
  .reservations-tab ul li button {
    padding: 10px 20px;
    font-size: 21px;
  }
  .order-history-main-box p.requested-date {
    font-size: 18px;
    margin-bottom: 35px;
  }
  .order-history-main-box{
    margin-bottom: 50px;
  }

  .order-image {
    max-width: 210px;
    min-height: 127px;
    max-height: 127px;
  }
  .order-image .order-logo {
    width: 55px;
    height: 55px;
    padding: 6px;
    top: 15px;
    left: 15px;
  }
  .order-title h6 {
    font-size: 22px;
  }
  .order-details {
    padding-left: 30px;
  }
  .order-history-main-box p {
    font-size: 15px;
  }
  .booking-details ul.order-date-time li {
    font-size: 15px;
  }
  .view-store ul li {
    margin-left: 10px;
  }
  .view-store ul li:first-child {
    margin-left: 0;
  }
  .view-store button.btn {
    font-size: 13px;
    padding: 1px 5px;
    width: 104px;
    height: 30px;
    line-height: 24px;
  }
  .dont-have-order{
    padding-top: 125px;
  }
  .dont-have-order p {
      font-size: 22px;
  }
  .dont-have-order span{
      font-size: 15px;
      padding-bottom: 25px;
  }
  .dont-have-order button.btn {
      font-size: 13px;
      font-weight: 700;
      padding: 2px 10px;
      width: 112px;
      height: 30px;
  }
  .modal-body{
    padding: 20px;
  }
  .modal-body .login-title {
      margin-bottom: 20px;
  }
  .star-ratings > label:before {
      width: 20px;
      height: 20px;
      background-size: 20px;
      margin: 2px 3px;
  }
  .star-ratings:not(:checked) > label:hover, 
  .star-ratings:not(:checked) > label:hover ~ label, 
  .star-ratings > input:checked ~ label {
      background-size: 20px;
      background-position: 3px 2px;
  }
  .star-ratings{
      margin-bottom: 5px;
  }
  .modal-body .form-group textarea.form-control {
      font-size: 14px;
      padding: 10px 15px;
      height: 90px;
  }
  .modal-body button.btn {
      padding: 5px 15px;
  }  
  .reservation-id p {
    font-size: 15px;
  }

}

@media screen and (max-width:1199px) {    
  .order-details {
    padding-left: 25px;
  }
  .view-store ul {
    flex-wrap: wrap;
    margin-top: 20px;
  }
  .view-store ul li {
    margin: 0;
    margin-bottom: 7px;
  }
  .view-store button.btn{
    font-size: 13px;
  }
  .view-store ul li:last-child {
    margin-bottom: 0;
  }

}

@media screen and (max-width:991px) {  
  .reservations-tab{
    margin-bottom: 50px;
  }
  .reservations-tab ul li button {
    font-size: 17px;
  }
  .order-history-box {
    flex-wrap: wrap;
  }
  .order-history-main-box p.requested-date {
    margin-bottom: 30px;
  }
  .order-image {
    margin-bottom: 25px;
  }
  .order-details {
    padding-left: 0px;
  }
  .view-store ul li {
    margin: 0;
    margin-left: 10px;
  }

  .view-store ul li:first-child {
    margin-left: 0;
  }

}

@media screen and (max-width:767px) {
  .order-history-box {
    justify-content: center;
  }

  .dont-have-order {
    padding-top: 80px;
    padding-bottom: 50px;
  }

}


@media screen and (max-width:480px) {
  .reservations-tab{
    margin-bottom: 30px;
  }
  .dont-have-order {
    padding-top: 50px;
    padding-bottom: 40px;
  }
  .dont-have-order p {    
    margin-bottom: 10px;
  }
  .dont-have-order span {
    padding-bottom: 10px;
  }
  .reservations-tab {
    padding: 5px;
  }  
  .reservations-tab ul li button {
    font-size: 12px;
    padding: 10px 5px;
  }
  .view-store ul {
    margin-top: 10px;
  }
  .view-store ul li {
    width: 100%;
    margin: 0;
    text-align: center;
    margin-bottom: 8px;
  }
  .view-store ul li:last-child {
    margin-bottom: 0;
  }

}
