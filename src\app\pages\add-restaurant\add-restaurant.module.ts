import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { AddRestaurantComponent } from './add-restaurant.component';

const routes: Routes = [
  { path: '', component: AddRestaurantComponent },
];
@NgModule({
  imports: [
    SharedModule,
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule
  ],
  declarations: [AddRestaurantComponent],
  exports: [AddRestaurantComponent]
})
export class AddRestaurantModule { }
