
export class Dietary {
  id: string;
  name: string;
  status: boolean = true;

  created_at: string;
  updated_at: string;

  static toFormData(dietary: Dietary) {
    const formData = new FormData();

    if (dietary.id) formData.append('id', dietary.id);
    if (dietary.name) formData.append('name', dietary.name);
    formData.append('status', dietary.status ? '1' : '0');

    return formData;
  }
}
