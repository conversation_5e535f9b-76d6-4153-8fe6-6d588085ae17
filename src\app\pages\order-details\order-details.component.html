<section class="checkout-page">

    <div class="container loader-height" *ngIf="isLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container" *ngIf="!isLoading">
        <div class="checkout-section track-section">
            <div class="login-box">
                <div class="track-order-box">
                    <div class="track-order-heading">
                        <div class="product-logo cursor" (click)="orderStore(restaurant)">
                            <img [src]="restaurant?.image_url" [alt]="restaurant.restaurant_name"
                                onerror="this.src='./assets/favicon.png';">
                        </div>
                        <h5 class="cursor" (click)="orderStore(restaurant)">{{ restaurant.restaurant_name }}</h5>
                    </div>

                    <h6 *ngIf="order.status == 'Pending'">Your order has been placed</h6>
                    <h6 *ngIf="order.status == 'Pending'">Waiting for restaurant to accept</h6>
                    <h6 *ngIf="order.status == 'Accepted'">Your order preparation time</h6>
                    <h6 *ngIf="order.status == 'Delivered'">Your order has been Delivered</h6>
                    <h6 *ngIf="order.status == 'Collected'">Your order has been collected</h6>
                    <h6 *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'">Your order preparation
                        time</h6>
                    <h6 class="reject-title" *ngIf="order.status == 'Failed'">Your order has been rejected</h6>

                    <span *ngIf="order.status != 'Delivered'">
                        In order to receive updates on your order via email or SMS, please verify your email and
                        phone number.This can be done on the profile section.
                        <br>Please contact the restaurant if you do not receive any notifications once verified.
                    </span>

                    <p *ngIf="order.status == 'Pending'">Thank you, your order have been
                        successfully placed. Please wait for the restaurant to confirm your order and delivery
                        time.
                    </p>
                    <p *ngIf="order.status == 'Accepted'">Your order has been accepted</p>
                    <p *ngIf="order.status == 'Delivered'">Enjoy your meal. Remember to leave a review once your
                        finished.</p>
                    <p *ngIf="order.status == 'Collected'">Sit tight, your meal is on its way to you.</p>
                    <p *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'">Your meal is being
                        cooked
                        up by the chef.</p>
                    <p *ngIf="order.status == 'Failed'">Rejected Reason : {{ order.failed_reason }}</p>
                    <div class="reservation-id">
                        <p><strong>Order ID</strong> : #{{ order.order_number }}</p>
                    </div>
                    <div class="order-status" *ngIf="order.status != 'Failed'">
                        <div class="check-arrow">
                            <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image" loading="lazy">
                        </div>

                        <div class="preparation-timer" *ngIf="order.status == 'Accepted'">
                            <ul *ngIf="daysToDday>0 || hoursToDday>0 || minutesToDday>0 || secondsToDday>0">
                                <li *ngIf="daysToDday > 0">
                                    <h2>{{daysToDday}}</h2>
                                    <span>Days</span>
                                </li>
                                <li *ngIf="hoursToDday > 0">
                                    <h2>{{hoursToDday}}</h2>
                                    <span>Hours</span>
                                </li>
                                <li *ngIf="minutesToDday > 0">
                                    <h2> {{minutesToDday}}</h2>
                                    <span>Minutes</span>
                                </li>
                                <li *ngIf="secondsToDday > 0">
                                    <h2>{{secondsToDday}}</h2>
                                    <span>Seconds</span>
                                </li>
                            </ul>
                        </div>

                        <div class="order-statusp-list">
                            <ul class="{{ order.order_type }}">
                                <ng-container *ngFor="let status of orderStatuses">
                                    <li [class.active]="status.checked"
                                        *ngIf="status.order_type=='all' || status.order_type==order.order_type">
                                        <p *ngIf="order.order_type =='pickup' && status.title == 'Delivered'">Picked Up
                                        </p>
                                        <p *ngIf="order.order_type !='pickup' || status.title != 'Delivered'">
                                            {{status.title}}</p>
                                    </li>
                                </ng-container>
                            </ul>
                        </div>
                    </div>

                    <div class="order-status rejected-status" *ngIf="order.status == 'Failed'">
                        <div class="check-arrow">
                            <img src="assets/images/reject.png" alt="Go-Grubz-reject-image">
                        </div>
                        <div class="order-statusp-list">
                            <ul>
                                <li [class.active]="status.checked" *ngFor="let status of orderRejectStatuses">
                                    <p>{{status.title}}</p>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="order-details">
                        <div class="table-responsive">
                            <table>
                                <tr>
                                    <td>Order Type:</td>
                                    <td>{{ order.order_type | titlecase }}</td>
                                </tr>
                                <tr *ngIf="order.order_type == 'delivery'">
                                    <td>Address:</td>
                                    <td>{{ order.address }}</td>
                                </tr>
                                <tr *ngIf="order.order_type == 'delivery'">
                                    <td>Delivery Time:</td>
                                    <td>{{order.delivery_date}} {{order.delivery_time}}</td>
                                </tr>
                                <tr>
                                    <td>Order At:</td>
                                    <td>{{convertToDate(order.created)}}</td>
                                </tr>
                                <tr *ngIf="order.order_description">
                                    <td>Order Description:</td>
                                    <td>{{order.order_description}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
            <div class="add-to-cart-box-two" *ngIf="order.cart_view?.length > 0">
                <div class="d-flex h-100">
                    <div class="add-cart-box">
                        <div class="cart-middle">
                            <div class="cart-item" *ngFor=" let cart of order.cart_view; let i=index">
                                <div class="cart-image-item" *ngIf="cart?.image_url">
                                    <img [src]="cart?.image_url" [alt]="cart.menu_name">
                                </div>
                                <div class="cart-content">
                                    <h6>{{cart.menu_name}}</h6>
                                    <ul *ngIf="cart.subaddons_name">
                                        <li>{{cart.subaddons_name}}</li>
                                    </ul>
                                    <p>{{convertNumber(cart.total_price)}}</p>
                                </div>
                                <div class="cart-add-item">
                                    <ul>
                                        <li><span>{{cart.quantity}}</span></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="offfer-heading" *ngIf="order?.applied_offers?.length > 0">
                                <h6>
                                    <img src="assets/images/offer-icon.png" alt="Go-Grubz-offer-image">
                                    Your Offer
                                </h6>
                            </div>
                            <div *ngIf="order?.applied_offers?.length > 0">
                                <div class="cart-item" *ngFor="let appliedOffer of order.applied_offers; let i=index">
                                    <div class="cart-content">
                                        <h6>{{appliedOffer?.menu_name}}</h6>
                                        <ul *ngIf="appliedOffer?.subaddons_name">
                                            <li>{{appliedOffer?.subaddons_name}}</li>
                                        </ul>
                                        <p>{{convertNumber(appliedOffer?.total_price)}}</p>
                                    </div>
                                    <div class="cart-add-item">
                                        <ul class="offer-counter">
                                            <li><span>{{ appliedOffer?.quantity }}</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="subtotal-price-box">
                        <div class="subtotal-box">
                            <ul class="add-promo-code">
                                <li>
                                    <span>Subtotal</span>
                                    <span>{{convertNumber(order.order_sub_total)}}</span>
                                </li>
                                <li *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
                                    <span>Delivery Charge</span>
                                    <span>{{convertNumber(order.delivery_charge)}}</span>
                                </li>
                                <li *ngIf="order.service_charge > 0">
                                    <span>Service charge</span>
                                    <span>{{convertNumber(order.service_charge)}}</span>
                                </li>
                                <li *ngIf="order.voucher_code && order.voucher_amount > 0">
                                    <span>Voucher ({{order.voucher_code}})</span>
                                    <span>(-) {{convertNumber(order.voucher_amount)}}</span>
                                </li>
                                <li *ngIf="order.offer_amount > 0">
                                    <span>Offer {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}</span>
                                    <span>(-) {{convertNumber(order.offer_amount)}}</span>
                                </li>
                                <li *ngIf="order.charity_amount > 0">
                                    <span>Charity {{order.charity_message}}</span>
                                    <span>{{convertNumber(order.charity_amount)}}</span>
                                </li>
                                <li *ngIf="order.reward_offer > 0 && order.reward_used == 'Y'">
                                    <span>Redeem
                                        {{order.reward_offer_percentage?'('+order.reward_offer_percentage+'%)':''}}</span>
                                    <span> (-) {{convertNumber(order.reward_offer)}}</span>
                                </li>
                                <li *ngIf="order.driver_tip > 0">
                                    <span>Driver Tip</span>
                                    <span>{{convertNumber(order.driver_tip)}}</span>
                                </li>
                                <li *ngIf="order.wallet_amount > 0">
                                    <span>My Wallet</span>
                                    <span> (-) {{convertNumber(order.wallet_amount)}}</span>
                                </li>
                            </ul>
                            <ul class="add-promo-code" *ngIf="order?.surcharges?.length > 0">
                                <li></li>
                                <li *ngFor="let surcharge of order.surcharges">
                                    <span>{{surcharge.surcharge_name}}</span>
                                    <span>{{convertNumber(surcharge.surcharge_amount)}}</span>
                                </li>
                            </ul>
                            <ul class="add-promo-code">
                                <li class="total-amount">
                                    <span>Total</span>
                                    <span>{{convertNumber(order.order_grand_total + order.charity_amount)}}</span>
                                </li>
                                <li class="earn-points px-0"
                                    *ngIf="order.order_point > 0 && order.status == 'Delivered'">
                                    <p><svg class="fa-solid fa-trophy"></svg> You've earned {{ order.order_point }}
                                        points</p>
                                </li>
                                <li class="earn-points px-0"
                                    *ngIf="order.order_point > 0 && order.status != 'Delivered'">
                                    <p><svg class="fa-solid fa-trophy"></svg> You will earn {{ order.order_point }}
                                        points</p>
                                </li>
                                <li *ngIf="order.split_payment == 'No'">
                                    <span>Payment Method</span>
                                    <span *ngIf="order.payment_method == 'cod'">cash on delivery</span>
                                    <span *ngIf="order.payment_method == 'Stripe'">
                                        XXXX-{{order.card_view.card_number}}</span>
                                    <span *ngIf="order.payment_method == 'Apple Pay'">pay by link</span>
                                </li>
                                <li *ngIf="order.split_payment == 'Yes'">
                                    <span>Payment Method</span>
                                    <span>Paid via Wallet +
                                        {{order.payment_method == 'cod'?'cash on delivery':order.payment_method ==
                                        'Stripe'?'Credit/Debit Card':'Paypal'}}</span>
                                </li>
                                <li>
                                    <span>Payment Status</span>
                                    <span *ngIf="order.payment_status == 'NP'">Unpaid</span>
                                    <span *ngIf="order.payment_status == 'P'">Paid</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>