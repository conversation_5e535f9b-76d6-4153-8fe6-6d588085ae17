$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.step-box {
  padding: 17px 100px;
  border: 2px solid #F8F7F7;
  border-radius: 20px;
  position: relative;
  margin-bottom: 15px;
}

.step-box .step-number {
  font-family: '<PERSON>oka';
  font-size: 25px;
  color: #000000;
  font-weight: 500;
  text-align: center;
  width: 42px;
  height: 42px;
  line-height: 40px;
  border-radius: 50%;
  display: inline-block;
  background-color: #F4F3F3;
  position: absolute;
  top: 14px;
  left: 30px;
}

.step-box h5.title {
  font-family: '<PERSON><PERSON>';
  font-size: 30px;
  color: #000000;
  font-weight: 500;
  line-height: 36px;
  text-align: center;
  margin-bottom: 0;
}

.step-box .success-icon {
  position: absolute;
  top: 14px;
  right: 40px;
}

.step-box .success-icon img {
  width: 42px;
}

.checkout-section {
  display: flex;
}

.step-body-box {
  padding-top: 20px;
  max-width: 490px;
  margin: auto;
}

.step-body-box .form-group span.icon {
  top: 10px;
}

.step-body-box input.form-control {
  height: 50px;
  padding-left: 20px;
  padding-right: 20px;
}

.step-body-box .select-box .form-control {
  height: 50px;
}

.step-body-box .select-box::before {
  font-size: 16px;
  top: 12px;
  right: 30px;
}

.step-body-box .select-box .form-control {
  padding-left: 20px;
}

.step-body-box .form-group textarea.textarea-msg {
  height: 200px;
  padding: 15px 20px;
  resize: none;
}

.next-btn button.btn {
  font-size: 18px;
  width: 100%;
  padding: 7px 20px;
  height: 45px;
}

.step-body-box .or-option span {
  color: #8F8F8A;
}

.step-body-box .or-option::after {
  background-color: #8F8F8A;
}

.step-body-box .accordion {
  padding-top: 5px;
  padding-bottom: 5px;
}

.accordion .accordion-item {
  border: 0;
  border-radius: 0;
}

.accordion-header button {
  font-family: 'Visby CF';
  font-size: 18px;
  color: #000000;
  padding: 14px 0;
  display: inline-block;
  width: 100%;
  text-decoration: none;
  font-weight: 800;
  position: relative;
  background-color: transparent;
  border:0;
  outline: none;
}

.accordion-header button.add-card-option::before {
  width: 25px;
  height: 25px;
  text-align: center;
  font-size: 15px;
  color: #000000;
  position: absolute;
  content: "\f078";
  font-family: 'fontawesome';
  right: 0;
  top: 17px;
  transition: all 0.5s;
  transform: rotate(-90deg);
}

// .accordion-header button.collapsed span.check {
//   display: none;
// }

.accordion-header button span.check {
  position: absolute;
  left: -35px;
}

.accordion-header button span.check img {
  width: 20px;
}

.step-body-box .accordion-body {
  padding: 0;
}

.security-number-group {
  display: flex;
  align-items: center;
  margin-bottom: 13px;
}

.security-number-group img {
  margin-right: 18px;
}

.security-number-group input {
  width: 70px;
  height: 34px;
  border-radius: 2px;
  padding: 0 10px;
  border: 2px solid #8F8F8A;
}

.security-number-group span {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #8F8F8A;
  font-weight: 700;
  padding-left: 20px;
}

.accordion-body .form-check .form-check-input {
  width: 18px;
  height: 18px;
}

.accordion-body .form-check .form-check-input:checked {
  border-color: #14A411 !important;
  background-color: #14A411;
  background-image: url(/assets/images/white-check.svg);
  background-size: 11px;
}

.accordion-body .form-check .form-check-label {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  padding-left: 10px;
}

.delivery-pickup-box {
  padding-bottom: 15px;
}

.delivery-pickup-box .nav.nav-tabs {
  border: 0;
  margin: auto;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  background: #f4f3f3;
  max-width: fit-content;
  padding: 6px;
  border-radius: 10px;
}

.delivery-pickup-box .nav.nav-tabs li button{
  font-size: 18px;
  color: #6c757d;
  font-weight: 600;
  cursor: pointer;
  padding: 5px 25px;
  margin: 0;
  border: 0;
  line-height: 24px;
  text-align: center;
  border-radius: 10px;
  min-height: 46px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.delivery-pickup-box .nav.nav-tabs li button.active {
  background: #202020;
  color: #fff;
}

.delivery-pickup-box .nav.nav-tabs li button span.unavailable-text {
  font-size: 10px;
  display: block;
}

.pickup-time-list ul li a.active {
  background: #202020;
  color: #fff;
}

.delivery-time {
  display: flex;
  justify-content: space-between;
  padding-bottom: 15px;
}

.delivery-time svg {
  margin-right: 10px;
}

.delivery-time span {
  font-size: 18px;
  font-weight: 600;
  color: #202020;
}

.delivery-pickup-list {
  display: flex;
}

.delivery-list-box button {
  min-width: 150px;
  margin-right: 10px;
  position: relative;
  display: inline-block;
  padding: 10px 35px 12px 12px;
  border: 1px solid #f4f3f3;
  text-decoration: none;
  border-radius: 10px;
  background-color: transparent;
}

.delivery-list-box button h6 {
  font-family: 'Visby CF';
  font-size: 16px;
  line-height: normal;
  font-weight: 800;
  margin-bottom: 0;
}

.delivery-list-box button p {
  font-size: 14px;
  margin-bottom: 0;
}

.delivery-list-box button svg {
  position: absolute;
  top: 13px;
  right: 10px;
}

.delivery-list-box button svg.fa-circle {
  color: #dbdbdb;
}

.delivery-list-box button svg.fa-circle-check {
  color: #fc353a;
}

#schedule-later-popup .modal-body h6 {
  font-size: 20px;
  font-weight: 800;
  margin-bottom: 15px;
  font-family: 'Visby CF';
}

#schedule-later-popup .nav.nav-tabs {
  border: 0;
  background-color: #f4f3f3;
  border-radius: 10px;
  margin-bottom: 15px;
}

#schedule-later-popup .nav.nav-tabs li {
  width: 25%;
}

#schedule-later-popup .nav.nav-tabs li a {
  font-size: 16px;
  color: #202020;
  padding: 5px 20px;
  cursor: pointer;
  text-align: center;
  border: 0;
  border-radius: 10px;
  font-weight: 600;
}

#schedule-later-popup .nav.nav-tabs li a span {
  font-size: 18px;
  font-weight: 800;
  display: block;
}

#schedule-later-popup .nav.nav-tabs li a.active {
  background-color: #202020;
  color: #fff;
}

.pickup-time-list ul {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
}

.pickup-time-list ul li {
  padding: 0 5px 10px 5px;
  width: 50%;
}

.pickup-time-list ul li:first-child {
  padding-left: 0;
}

.pickup-time-list ul li:last-child {
  padding-left: 0;
}

.pickup-time-list ul li a {
  font-size: 14px;
  color: #202020;
  width: 100%;
  font-weight: 800;
  text-align: center;
  padding: 5px 15px;
  border-radius: 25px;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  background-color: #f4f3f3;
}

.contact-details {
  padding-bottom: 30px;
}

.contact-details ul {
  margin-bottom: 0;
}

.contact-details ul li {
  border-bottom: 1px solid #f4f3f3;
}

.contact-details ul li button {
  display: flex;
  width: 100%;
  position: relative;
  text-align: left;
  padding: 14px 30px 16px 0;
  text-decoration: none;
  border:0;  
  outline: none;
  background-color: transparent;
}

.contact-details ul li .icon {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translate(0, -50%);
}

.contact-details ul li .icon svg {
  font-size: 18px;
  color: #202020;
}

.contact-details ul li .address {
  padding-left: 40px;
}

.contact-details ul li .address p {
  font-size: 16px;
  color: #202020;
  font-weight: 600;
  margin-bottom: 0px;
  line-height: normal;
}

.contact-details ul li .address p a {
  color: #202020;
}

.contact-details ul li .address span {
  font-size: 14px;
  color: #8f8f8a;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-details ul li .right-arrow {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 10px;
}

.contact-details ul li .right-arrow svg {
  font-size: 16px;
  color: #202020;
}

.order-place-btn {
  max-width: 580px;
  margin: auto;
}

.order-place-btn button.btn {
  width: 100%;
}

.my-wallet-box {
  padding: 15px 25px 10px 25px;
  background: #f4f3f3;
  border-radius: 5px;
}

.my-wallet-box .form-check {
  display: flex;
  margin-bottom: 5px;
}

.my-wallet-box .form-check .form-check-input {
  width: 18px;
  height: 18px;
  margin-top: 7px;
  margin-right: 15px;
}

.my-wallet-box .form-check-label {
  font-family: 'Visby CF';
  font-size: 20px;
  font-weight: 800;
  width: 100%;
}

.my-wallet-box .form-check-label svg {
  margin-right: 5px;
}

.my-wallet-box p {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.my-wallet-box p span.wallet-amount {
  font-size: 22px;
  font-weight: 800;
  color: #fc353a;
  padding-left: 5px;
}

.my-wallet-box p.paid-amount {
  color: #14a411;
}

.my-wallet-box p.paid-amount span.wallet-amount {
  color: #14a411;
}

.my-wallet-box .form-group {
  margin-bottom: 10px;
}

.my-wallet-box .form-group input.form-control {
  font-size: 16px;
  height: 46px;
  background-color: #fff !important;
}

.driver-tip {
  position: relative;
  padding: 0 15px;
  border-radius: 5px;
}

.driver-tip h6 {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #202020;
  font-weight: 700;
  margin-bottom: 5px;
}

.driver-tip ul {
  padding: 0;
  margin: 0;
  display: flex;
  background: #f4f3f3;
  border-radius: 25px;
}

.driver-tip ul li {
  width: 100%;
  position: relative;
  display: inline-block;
  margin-bottom: 0;
}

.driver-tip ul li button {
  font-size: 14px;
  font-weight: 600;
  color: #202020;
  cursor: pointer;
  width: 100%;
  height: 30px;
  line-height: 28px;
  text-decoration: none;
  text-align: center;
  position: relative;
  display: inline-block;
  border:0;
  border-radius: 25px;
}

.driver-tip ul li button img {
  width: 16px;
  margin-right: 8px;
}

.driver-tip ul li button.active {
  color: #fff;
  border-color: #fc353a;
  background-color: #fc353a;
}

.driver-tip ul li button.close {
  font-size: 8px;
  color: #fc353a;
  width: 12px;
  height: 12px;
  text-align: center;
  padding: 0;
  line-height: 2px;
  position: absolute;
  z-index: 2;
  top: -4px;
  right: -2px;
  border: 1px solid #fc353a;
  border-radius: 50%;
  background-color: #fff;
}

.modal-body {
  padding: 80px 30px 30px 30px;
}

.modal-body img.verification-icon {
  height: 80px;
  margin: auto;
  display: flex;
  margin-bottom: 20px;
}

#select-address-popup .modal-body h6,
#edit-number-popup .modal-body h6,
#update-number-popup .modal-body h6,
#number-verification-popup .modal-body h6 {
  font-size: 26px;
  font-weight: 800;
  font-family: 'Visby CF';
  text-align: center;
}

#number-verification-popup .modal-body p {
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  margin-bottom: 35px;
}

.modal-body .form-group {
  margin-bottom: 15px !important;
}

.modal-body .form-group input.form-control {
  padding: 4px 20px 7px;
  height: 50px;
}

#number-verification-popup .modal-body span {
  font-size: 15px;
  font-weight: 700;
  color: #8f8f8a;
  text-align: center;
  display: inline-block;
  width: 100%;
}

#number-verification-popup .more-option {
  display: flex;
  justify-content: center;
  margin: 0;
  padding-top: 15px;
}

#number-verification-popup .more-option li {
  padding: 0 5px;
}

#number-verification-popup .more-option li svg {
  color: #d7d7d7;
  font-size: 4px;
  position: relative;
  top: -4px;
}

#number-verification-popup .more-option li a {
  font-size: 16px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

.modal-footer {
  padding: 0 30px 30px 30px;
}

.modal-footer button.btn {
  margin: 0;
  width: 100%;
}

#add-new-address-popup button.btn {
  margin: 0;
  width: 100%;
}

.add-btn a.btn {
  font-size: 18px;
  padding: 7px 16px;
}

.address-list {
  padding: 10px 0 0 0;
}

.address-list ul {
  margin-bottom: 15px;
}

.address-list ul li {
  border-bottom: 1px solid #dfdfdf;
}

.address-list ul li .form-check {
  display: flex;
  padding: 0;
  margin: 0;
}

.address-list ul li .form-check .form-check-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 15px 15px 0 0;
}

.address-list ul li .form-check label.form-check-label {
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  padding: 11px 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 65px;
}

#select-address-popup .modal-body .address-list h6.drop-off-options-title {
  font-size: 18px;
  text-align: left;
  margin: 20px 0 5px 0;
}

.address-list .form-group textarea.form-control {
  min-height: 120px;
  padding: 10px 20px;
}

.postcode-group input.form-control {
  padding-right: 100px !important;
}

.postcode-group .btn {
  line-height: 25px;
}

.postcode-group .btn:hover {
  color: #fff !important;
}

.subtotal-box ul li .form-check {
  margin-bottom: 0;
  min-height: auto;
}

#free-products-popup .modal-body {
  padding-top: 30px;
}

#free-products-popup .modal-body h6 {
  font-family: 'Visby CF';
  font-weight: 800;
  text-align: center;
  margin-bottom: 30px;
}

.free-products-list {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 2px solid #f4f3f3;
}

.free-products-list:last-child {
  margin-bottom: 0;
}

.free-products-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.free-products-list li a {
  cursor: pointer;
}

.free-products-list .checkbox .form-check-input {
  width: 18px;
  height: 18px;
  margin-top: 6px;
}

.free-products-list .checkbox .form-check-label {
  font-size: 18px;
  color: #202020;
  font-weight: 800;
  text-transform: capitalize;
  padding-left: 12px;
}

.free-products-list li .add-to-product {
  margin-bottom: 0;
}

#free-products-popup .modal-footer span.error-text {
  font-size: 16px;
  color: #f00;
  font-weight: 600;
  margin-bottom: 5px;
  display: inline-block;
  text-transform: capitalize;
}

.offfer-heading {
  padding: 10px 25px;
  background-color: #f4f3f3;
}

.offfer-heading img {
  width: 25px;
  margin-top: -3px;
  margin-right: 3px;
}

.offfer-heading h6 {
  font-size: 22px;
  font-family: 'Visby CF';
  font-weight: 800;
  margin-bottom: 0;
}

.add-cart-box .cart-item .cart-add-item ul.offer-counter {
  background-color: #fff;
  width: 30px;
  justify-content: center;
}

.add-cart-box .cart-item .cart-add-item ul.offer-counter li span {
  padding: 0;
}

.credit-card-list {
  margin-bottom: 0;
}

.credit-card-list li {
  display: inline-block;
  margin-right: 10px;
}

.credit-card-list li img {
  width: 40px;
}

.error-text {
  font-size: 16px;
  font-weight: 500;
  color: #f00;
}

#items-already-in-cart-popup .modal-body {
  text-align: center;
}

#items-already-in-cart-popup .modal-body h6 {
  margin-bottom: 12px;
}

#items-already-in-cart-popup .modal-body p {
  font-size: 16px;
  color: #000000;
}

#items-already-in-cart-popup .modal-body button.btn {
  padding: 10px 20px;
  font-size: 18px;
  margin: 0 15px;
}

.earn-points {
  padding: 2px 12px;
}

.earn-points p {
  font-family: 'Visby CF';
  font-size: 13px;
  color: #fc353a;
  font-weight: 700;
  margin-bottom: 0;
}

.earn-points svg {
  font-size: 14px;
  margin-right: 2px;
  position: relative;
  top: 1px;
}

#add-new-card-popup .modal-body {
  padding-top: 25px;
}

#add-new-card-popup .form-group span.form-control {
  padding: 7px 15px 7px;
}

#add-new-card-popup .modal-body button.btn {
  padding: 10px 20px;
  font-size: 18px;
}

.address-text-error {
  font-size: 16px;
  color: #fc353a;
  margin-bottom: 0;
  letter-spacing: 1px;
}

.add-items {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  display: table;
  bottom: 10px;
  z-index: 100;
}

.add-items button.btn {
  font-size: 16px;
  line-height: 24px;
  padding: 8px 15px;
  border-radius: 30px;
  box-shadow: 0 0 10px 2px #b9b9b9;
  display: flex;
  justify-content: space-between;
  width: 650px;
}

.add-items button.btn svg {
  margin-left: 2px;
}

.add-items button.btn:hover {
  background-color: #fff !important;
}

.modal-body .form-group.postcode-group .btn.modal-black-btn .spinner-border {
  width: 12px;
  height: 12px;
}

.modal-body .form-group.postcode-group .btn.modal-black-btn:hover {
  color: #000 !important;
  background-color: transparent !important;
}

#add-new-address-popup .modal-body {
  padding-top: 24px;
}

.forgot-password {
  padding-bottom: 25px;
}

.forgot-password span {
  font-size: 18px;
  color: #fc353a;
  font-family: 'Visby CF';
  font-weight: 600;
  text-decoration: none;
}

.form-group label.form-label {
  font-size: 18px;
  color: #212529;
  font-weight: 700;
}

.form-otp-list {
  display: flex;
}

.form-otp-list input.form-control {
  width: 48px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
  margin-right: 8px;
}

.resend-code {
  padding-top: 15px;
}

.resend-code a {
  font-size: 18px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

.subtotal-price-box {
  overflow: hidden;
  overflow-y: auto;
}

.subtotal-price-box::-webkit-scrollbar {
  width: 5px;
  border-radius: 5px;
}

.subtotal-price-box::-webkit-scrollbar-track {
  background: #ffe6e7;
  border-radius: 5px;
}

.subtotal-price-box::-webkit-scrollbar-thumb {
  background: $primary;
  border-radius: 5px;
}

.subtotal-price-box::-webkit-scrollbar-thumb:hover {
  background: $primary;
}

.suggested-items-box {
  padding: 12px 15px 20px;
  border-top: 2px solid #f4f3f3;
}

.suggested-items-box h6 {
  font-size: 20px;
  color: #000;
  font-weight: 800;
  font-family: 'Visby CF';
  margin-bottom: 22px;
}

.suggested-items-box h6 img {
  width: 22px;
  margin-top: -6px;
  margin-left: 10px;
}

.suggested-items-box .cart-item {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.suggested-items-box .cart-item .cart-image-item {
  padding-right: 10px;
}

.suggested-items-box .cart-item .cart-image-item img {
  width: 63px;
  height: 63px;
  border-radius: 8px;
}

.suggested-items-box .cart-item .cart-content {
  width: 100%;
}

.suggested-items-box .cart-item .cart-content ul {
  padding: 0;
  margin-bottom: 2px;
}

.suggested-items-box .cart-item .cart-content ul li {
  font-family: Visby CF;
  font-size: 16px;
  color: #8f8f8a;
  font-weight: 600;
  padding-left: 5px;
  margin-left: 5px;
  display: inline-block;
  position: relative;
}

.suggested-items-box .cart-item .cart-content ul li:before {
  position: absolute;
  content: "";
  top: 50%;
  width: 2px;
  height: 2px;
  left: -2px;
  border-radius: 50%;
  background-color: #8f8f8a;
}

.suggested-items-box .cart-item .cart-content ul li:first-child {
  padding-left: 0;
  margin-left: 0;
}

.suggested-items-box .cart-item .cart-content ul li:first-child::before {
  display: none;
}

.suggested-items-box .cart-item .cart-content h6 {
  font-family: 'Visby CF';
  font-size: 16px;
  color: #000000;
  word-break: break-word;
  line-height: 20px;
  font-weight: 800;
  margin-bottom: 0;
}

.suggested-items-box .cart-item .cart-content p {
  font-family: 'Visby CF';
  font-size: 14px;
  color: #000000;
  line-height: 22px;
  font-weight: 600;
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggested-items-box .cart-item .cart-content p span {
  display: flex;
  align-items: center;
}

.suggested-items-box .cart-item .cart-content button.add-option {
  font-family: 'Fredoka';
  color: #fff;
  font-size: 11px;
  font-weight: 400;
  width: 42px;
  height: 20px;
  line-height: 12px;
  border-radius: 25px;
  background-color: #EA3323;
  box-shadow: 0px 0px 3.75px 0px #********;
  text-align: center;
  text-decoration: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-left: 2px;
  border:0;
}

.back-to-login {
  font-size: 18px;
  font-family: 'Visby CF';
  font-weight: 600;
  text-align: center;
  padding-top: 20px;
}

.back-to-login span {
  color: #000;
  padding-right: 5px;
}

.back-to-login a {
  color: #fc353a;
  text-decoration: none;
}

.step-box span.account-email {
  font-weight: 800;
}

/*---------------------------------------------
         Product-Multiple-Options-Popup
---------------------------------------------*/
.product-header {
  padding: 15px 25px;
}

.product-close {
  position: initial !important;
}

.product-body {
  padding: 20px 25px;
}

.product-body h6 {
  text-align: center;
  margin-bottom: 12px;
}

.product-body p {
  font-size: 16px;
  text-align: center;
}

.popup-product-box {
  padding-bottom: 15px;
}

.product-body .product-image {
  text-align: center;
}

.product-body .product-image img {
  width: 90px;
  border-radius: 5px;
}

.product-body .choose-options h6 {
  font-size: 22px;
  margin-bottom: 0;
  text-align: left;
}

.required-select {
  display: flex;
  align-items: center;
}

.required-select .required {
  font-size: 14px;
  color: #ff0000;
  font-weight: 600;
}

.required-select .required svg {
  margin-right: 5px;
}

.required-select svg.fa-circle {
  color: #8F8F8A;
  font-size: 3px;
  margin: 0 6px;
}

.required-select span {
  color: #8F8F8A;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Visby CF';
}

.choose-options .choose-list {
  margin-bottom: 0;
}

.choose-options .choose-list li .form-check {
  cursor: pointer;
  position: relative;
  padding: 0px;
  margin-bottom: 0;
  display: flex;
  border-bottom: 2px solid #F8F7F7;
}

.choose-options .choose-list li .form-check label {
  width: 100%;
  padding: 0 45px;
}

.choose-options .choose-list li .form-check .burger-name {
  font-size: 16px;
  color: #202020;
  font-weight: bold;
}

.choose-options .choose-list li .form-check input.form-check-input {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 0;
  margin: 0;
}

.choose-options .choose-list li .form-check .form-check-label {
  padding: 10px 0;
}

.extra-addon-list li {
  font-size: 13px;
  color: #202020;
  display: inline-block;
  font-weight: 500;
  font-family: 'Visby CF';
}

.extra-addon-list li svg.fa-circle {
  font-size: 5px;
  margin: 0 5px 0 3px;
}

.choose-options .choose-list li .form-check .price {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #8B8F8F;
  display: block;
  padding-bottom: 5px;
}

.choose-options .choose-list li .form-check .edit-section span {
  font-size: 14px;
  color: #000;
  font-weight: 600;
  padding: 4px 15px 5px 15px;
  background-color: #f9f9f9;
  border: 1px solid #f4f3f3;
  border-radius: 20px;
}

.choose-options .choose-list li .form-check .right-arrow {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
}

.choose-options .choose-list li .form-check .right-arrow svg {
  font-size: 15px;
  color: #202020;
}

.product-footer {
  padding: 0 25px 25px 25px;
}

.product-footer button.btn.btn-primary {
  width: 100%;
}

/*----------Addon-Popup----------*/

#addonpopup .modal-header {
  padding: 15px 75px;
}

#addonpopup .modal-header button.btn-close {
  top: 24px;
  left: 22px;
}

#addonpopup .modal-header h6 {
  font-size: 24px;
  line-height: normal;
  margin-bottom: 0;
}

#addonpopup .modal-body {
  padding: 20px 25px 25px 25px;
}

#addonpopup .addon-list h6 {
  font-size: 24px;
  margin-bottom: 0;
}

#addonpopup .addon-list ul {
  padding: 10px 0 30px 0;
  margin-bottom: 0;
}

#addonpopup .addon-list ul li {
  border-bottom: 2px solid #F8F7F7;
}

#addonpopup .addon-list ul li .form-check {
  position: relative;
  display: flex;
  padding-left: 0px;
  margin-bottom: 0;
}

#addonpopup .form-check input.form-check-input {
  margin-top: 14px;
}

#addonpopup .addon-list ul li .form-check .form-check-label {
  color: #202020;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  padding: 11px 0;
}

#addonpopup button.btn {
  width: 100%;
  font-size: 18px;
  padding: 9px 15px;
}

#addonpopup .form-check input.form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #202020;
  margin-left: 0;
  margin-right: 10px;
}

#addonpopup .form-check input.form-check-input.radio-button {
  border-radius: 50%;
}

.add-to-cart-box-two .add-cart-box .cart-item .cart-content h6 {
  word-break: break-word;
}

.subtotal-box ul.add-promo-code li.total-amount {
  padding: 5px 0 6px 0;
  margin-top: 5px;
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
}

.subtotal-box ul.add-promo-code li.total-amount span {
  font-weight: 800;
  padding: 0;
}

.edit-addon-btn {
  padding: 0;
  height: 20px;
  width: 28px;
  font-size: 10px;
  line-height: 18px;
  background-color: #fc353a !important;
  border: 1px solid #fc353a !important;
  margin-left: 15px;
  color: #fff;
  border-radius: 5px;
  position: relative;
  top: 3px;
  box-shadow: none !important;
  font-weight: 700;
}

.veg-nonveg {
  width: 14px;
  height: 14px;
  position: relative;
  border: 2px solid #3ab54a;
  margin-right: 2px;
}

.veg-nonveg::before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #3ab54a;
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  margin: auto;
}

.veg-nonveg.noneveg-bg {
  border-color: #fe0000;
}

.veg-nonveg.noneveg-bg::before {
  background-color: #fe0000;
}


.subaddon-circle {
  margin: 0 5px;
  font-size: 5px !important;
  color: #8f8f8a;
  vertical-align: middle;
}

.price-circle {
  font-size: 4px !important;
  color: #fff;
  vertical-align: middle;
}

.price-circle svg {
  margin: 0px !important;
  transition: all .5s;
}

#addonpopup button.btn:hover .price-circle {
  color: #000;
  opacity: 0.7;
}
.suggested-items-box .cart-item .cart-content p span.price{
  padding-left: 20px;
  position: relative;
  flex-wrap: wrap;
  align-items: self-start;
}
.suggested-items-box .cart-item .cart-content p span.price span{
  margin-right:5px;
}
.suggested-items-box .cart-item .cart-content .veg-nonveg{
  position: absolute;
  left:0;
  top:5px;
}
.fa-circle-xmark{
  color:#f00;
}

.edit-delete ul {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
}
.edit-delete ul li {
  margin:5px;
  border-bottom: none !important;
}
.edit-delete ul li button {
  padding: 0;
  color: #fff;
  font-size: 13px;
  width: 30px;
  height: 30px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0px 0px 4px 3px #0000001A;
  border: 1px dashed #242323;
  background-color:#242323;
}
.edit-delete ul li button.delete-btn {
  border-color: #fc353a;
  background-color:#fc353a;
}
.step-body-box .accordion #creditpaymentStripe .form-group .form-control{
  padding:8px 15px;
}
.not-deliverable{
  opacity: 0.4;
  pointer-events: none;
}
.not-deliverable .not-deliverable-text{
  color: #f00;
  font-weight: bold;
}
.pay-icons-list{
  padding:0;
  margin:0;
  list-style: none;
  position: relative;
  top:2px;
  padding-left: 15px;
}
.pay-icons-list li{
  display: inline-block;
  margin-right: 10px;
}
.pay-icons-list li img{
  width: 45px;
}

@media screen and (max-width:1500px) {
  .step-box {
    padding: 5px 30px;
    border-radius: 15px;
    margin-bottom: 10px;
  }

  .step-box .step-number {
    font-size: 18px;
    width: 32px;
    height: 32px;
    line-height: 30px;
    top: 10px;
    left: 22px;
  }

  .step-box h5.title {
    font-size: 22px;
    line-height: 42px;
  }

  .step-box .success-icon {
    top: 10px;
    right: 30px;
  }

  .step-box .success-icon img {
    width: 22px;
  }

  .step-box span.account-email {
    font-size: 11px;
    line-height: normal;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 3px;
  }

  .step-body-box {
    padding-top: 5px;
    padding-bottom: 15px;
  }

  .step-body-box input.form-control {
    font-size: 13px;
    border-radius: 7px;
    padding-left: 10px;
    padding-right: 10px;
    padding: 6px 10px 7px;
    height: 30px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .step-body-box .form-group span.icon {
    top: 1px;
  }

  .step-body-box button.btn {
    font-size: 18px;
    padding: 4px 15px;
    height: 35px;
    font-weight: 800;
  }

  .delivery-pickup-box .nav.nav-tabs {
    padding: 4px;
    border-radius: 5px;
    margin-bottom: 10px;
  }

  .delivery-pickup-box .nav.nav-tabs li button {
    font-size: 14px;
    line-height: 20px;
    min-height: 40px;
    height: 100%;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .delivery-time {
    padding-bottom: 10px;
  }

  .delivery-time span {
    font-size: 13px;
  }

  .delivery-list-box a {
    padding: 5px 30px 5px 10px;
    border-radius: 5px;
  }

  .delivery-list-box a h6 {
    font-size: 14px;
  }

  .delivery-list-box a p {
    font-size: 12px;
  }

  .delivery-list-box a svg {
    font-size: 13px;
    top: 8px;
    right: 8px;
  }

  .delivery-pickup-box {
    padding-bottom: 5px;
  }

  .contact-details {
    padding-bottom: 0px;
  }

  .contact-details ul li button {
    padding: 8px 20px 8px 0;
    min-height: 58px;
  }

  .contact-details ul li .icon svg {
    font-size: 15px;
  }

  .contact-details ul li .address {
    padding-left: 35px;
    line-height: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .contact-details ul li .address p {
    font-size: 14px;
  }

  .contact-details ul li .address span {
    font-size: 13px;
  }

  .contact-details ul li .right-arrow svg {
    font-size: 12px;
  }

  .my-wallet-box {
    padding: 8px 15px;
    border-radius: 4px;
  }

  .my-wallet-box .form-check-label {
    font-size: 15px;
  }

  .my-wallet-box .form-check .form-check-input {
    width: 16px;
    height: 16px;
    margin-top: 4px;
    margin-right: 10px;
  }

  .my-wallet-box p {
    font-size: 14px;
  }

  .my-wallet-box p span.wallet-amount {
    font-size: 18px;
    padding-left: 1px;
  }

  .accordion-header button {
    font-size: 14px;
    padding: 6px 0;
  }
  .accordion-header button.add-card-option svg{
    position: relative;
    top: 1px;
  }
  .accordion-header button span.check {
    left: -24px;
  }

  .accordion-header button span.check img {
    width: 15px;
  }

  .step-body-box .accordion-body img {
    width: 34px;
  }

  .step-body-box .accordion-body .credit-card-list li img {
    width: 24px;
  }

  .accordion-header button.add-card-option::before {
    width: 15px;
    height: 15px;
    font-size: 11px;
    top: 9px;
  }

  .driver-tip ul li button {
    font-size: 11px;
    height: 26px;
    line-height: 25px;
  }

  .driver-tip ul li:last-child {
    margin-right: 0;
  }

  .driver-tip ul li button img {
    width: 10px;
    margin-right: 3px;
    margin-top: -1px;
  }

  .offfer-heading {
    padding: 8px 20px;
  }

  .offfer-heading h6 {
    font-size: 18px;
  }

  .offfer-heading img {
    width: 20px;
  }

  .modal-body {
    padding: 50px 20px 20px;
  }

  #free-products-popup .modal-body {
    padding-top: 20px;
  }

  .modal-footer {
    padding: 0 20px 20px;
  }

  #free-products-popup .modal-body h6 {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .free-products-list {
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #f4f3f3;
  }

  .free-products-list .checkbox .form-check-label {
    font-size: 14px;
    padding-left: 10px;
  }

  .free-products-list .checkbox .form-check-input {
    width: 16px;
    height: 16px;
    margin-top: 5px;
  }

  #schedule-later-popup .modal-body h6 {
    font-size: 18px;
    margin-bottom: 10px;
  }

  #schedule-later-popup .nav.nav-tabs {
    margin-bottom: 15px;
  }

  #schedule-later-popup .nav.nav-tabs li a {
    font-size: 14px;
    padding: 5px 15px;
    border-radius: 8px;
    line-height: 20px;
  }

  #schedule-later-popup .nav.nav-tabs li a span {
    font-size: 16px;
  }

  .pickup-time-list ul li a {
    font-size: 14px;
    padding: 4px 10px;
  }

  #edit-number-popup .modal-body h6,
  #number-verification-popup .modal-body h6,
  #select-address-popup .modal-body h6,
  #update-number-popup .modal-body h6 {
    font-size: 18px;
  }

  .modal-body .add-btn a.btn {
    font-size: 14px;
    padding: 4px 15px;
  }

  .address-list ul li .form-check label.form-check-label {
    font-size: 13px;
    padding: 8px 0;
    max-height: 55px;
  }

  .address-list ul li .form-check .form-check-input {
    width: 14px;
    height: 14px;
    margin: 11px 10px 0 0;
  }

  #select-address-popup .modal-body .address-list h6.drop-off-options-title {
    font-size: 18px;
    margin: 15px 0 0;
  }

  .address-list .form-group textarea.form-control {
    font-size: 14px;
    min-height: 100px;
    padding: 8px 15px;
  }

  #select-address-popup .modal-body .form-group {
    margin-bottom: 0px !important;
  }

  .modal-body .form-group input.form-control {
    padding: 5px 15px 6px;
    height: 36px;
  }

  .postcode-group input.form-control {
    padding-right: 80px !important;
  }

  .postcode-group .btn {
    width: 65px;
    height: 26px;
    font-size: 11px;
    line-height: 22px;
    top: 5px;
    right: 10px;
  }

  #add-new-address-popup .modal-body button.btn {
    padding: 5px 15px;
  }

  .modal-body img.verification-icon {
    height: 54px;
    margin-bottom: 10px;
  }

  #edit-number-popup .modal-body .form-group {
    margin-bottom: 0 !important;
  }

  #edit-number-popup .modal-body h6 {
    margin-bottom: 15px !important;
  }

  #items-already-in-cart-popup .modal-body p {
    font-size: 13px;
    margin-bottom: 15px;
  }

  #items-already-in-cart-popup .modal-body button.btn {
    font-size: 16px;
    margin: 0 5px;
    padding: 5px 20px;
  }

  .empty-cart-cls img {
    width: 55px;
  }

  .empty-cart-cls p {
    font-size: 18px;
  }

  .earn-points p {
    font-size: 11px;
  }

  .earn-points svg {
    font-size: 14px;
    margin-right: 2px;
  }

  #add-new-card-popup .form-group span.form-control {
    padding: 2px 15px;
  }

  #add-new-card-popup .modal-body button.btn {
    font-size: 14px;
    padding: 5px 15px;
  }

  .address-text-error {
    font-size: 13px;
  }

  #add-new-address-popup .modal-body {
    padding-top: 18px;
  }

  .error-text {
    font-size: 13px;
  }

  .form-group label.form-label {
    font-size: 14px;
  }

  .form-otp-list input.form-control {
    width: 36px;
  }

  .forgot-password {
    padding-bottom: 20px;
  }

  .forgot-password span {
    font-size: 14px;
  }

  .resend-code {
    padding-top: 10px;
  }

  .resend-code a {
    font-size: 14px;
  }

  .suggested-items-box {
    padding: 5px 10px;
  }

  .suggested-items-box h6 {
    font-size: 15px;
    margin-bottom: 13px;
  }

  .suggested-items-box h6 img {
    width: 20px;
    margin-left: 5px;
  }

  .suggested-items-box .cart-item {
    margin-bottom: 20px;
  }

  .suggested-items-box .cart-item .cart-image-item {
    padding-right: 5px;
  }

  .suggested-items-box .cart-item .cart-image-item img {
    width: 40px;
    height: 40px;
  }

  .suggested-items-box .cart-item .cart-content h6 {
    font-size: 12px;
    line-height: 16px;
  }

  .suggested-items-box .cart-item .cart-content ul {
    line-height: normal;
  }

  .suggested-items-box .cart-item .cart-content ul li {
    font-size: 12px;
    line-height: 18px;
  }

  .suggested-items-box .cart-item .cart-content p {
    font-size: 12px;
    line-height: 18px;
  }
  .suggested-items-box .cart-item .cart-content p span.price {
    padding-left: 15px;
  }
  .suggested-items-box .cart-item .cart-content button.add-option {
    font-size: 10px;
    width: 32px;
    min-width: 32px;
    height: 15px;
    line-height: 14px;
  }

  .back-to-login {
    font-size: 14px;
    padding-top: 15px;
  }

  #addonpopup .modal-header {
    padding: 10px 65px;
  }

  #addonpopup .modal-header button.btn-close {
    left: 20px;
  }

  #addonpopup .modal-header h6 {
    font-size: 20px;
  }

  #addonpopup .modal-header span {
    font-size: 14px;
  }

  #addonpopup .modal-body {
    padding: 20px 20px 20px;
  }

  #addonpopup .addon-list h6 {
    font-size: 20px;
  }

  #addonpopup .addon-list h6 span {
    font-size: 16px;
  }

  #addonpopup .addon-list ul {
    padding: 0px 0 20px;
  }

  #addonpopup .addon-list ul li .form-check .form-check-label {
    font-size: 14px;
    padding: 10px 0;
  }

  #addonpopup .form-check input.form-check-input {
    width: 16px;
    height: 16px;
    margin-top: 14px;
  }

  #addonpopup button.btn {
    font-size: 14px;
    padding: 5px 15px;
  }

  .product-header {
    padding: 10px 20px;
  }

  .product-body {
    padding: 20px 20px;
  }

  .product-body h6 {
    font-size: 22px;
    margin-bottom: 5px;
  }

  .product-body .choose-options h6 {
    margin-bottom: 10px;
  }

  .choose-options .choose-list li .form-check .form-check-label {
    padding: 0;
  }

  .choose-options .choose-list li .form-check label {
    padding: 0 30px;
  }

  .choose-options .choose-list li .form-check input.form-check-input {
    width: 16px;
    height: 16px;
  }

  .choose-options .choose-list li .form-check .right-arrow svg {
    font-size: 12px;
  }

  .subtotal-box ul.add-promo-code li.total-amount {
    margin-top: 0;
  }

  .veg-nonveg {
    width: 10px;
    height: 10px;
    border: 1px solid #3ab54a;
    margin-right: 2px;
  }

  .veg-nonveg:before {
    width: 4px;
    height: 4px;
  }
  .promo-code-box .error-text{
    font-size: 11px;
  }
  .next-btn button.btn {
    font-size: 18px;
    font-family:'Visby CF';
    font-weight: 800;
    height: 35px;
    padding:4px 15px;
    line-height: 20px;
  }
  .accordion-body .form-group .form-control{
    padding:7px 15px 7px;
  }
  .edit-delete ul li {
    margin:3px;
  }
  .edit-delete ul li button {
    font-size: 11px;
    width: 26px;
    height: 26px;
    line-height: 24px;
  }
  .pay-icons-list li img {
    width: 36px;
  }
  
}

.opacity-resend {
  opacity: 0.5;
}

@media screen and (max-width:1199px) {
  .add-to-cart-box-two .add-cart-box .cart-top .checkout-btn button.btn {
    display: none;
  }

  .add-to-cart-box-two .add-cart-box .top-cart-name .cart-name {
    margin-bottom: 4px;
  }

  .blink-animation {
    font-size: 13px;
  }

  .suggested-items-box .cart-item {
    margin-bottom: 15px;
  }
  .edit-delete ul li button {
    font-size: 13px;
    width: 30px;
    height: 30px;
    line-height: 28px;
  }

}

@media screen and (max-width:991px) {
  .edit-delete ul li {
    margin: 0 3px;
  }
  .edit-delete ul li button {
    font-size: 12px;
    width: 28px;
    height: 28px;
    line-height: 23px;
  }
  
}

@media screen and (max-width:767px) {
  .accordion-header button span.check {
    left: -20px;
  }

  .accordion-header button span.check img {
    width: 13px;
  }

  .add-items {
    width: 100%;
    padding: 0 22px;
  }

  .add-items button.btn {
    width: 100%;
  }

}

@media screen and (max-width:575px) {  
  .driver-tip ul li button {
    font-size: 13px;
  }

}

@media screen and (max-width:480px) {
  .step-box {
    padding: 5px 15px;
    border-radius: 10px;
  }

  .step-box .step-number {
    font-size: 14px;
    width: 28px;
    height: 28px;
    line-height: 28px;
    left: 12px;
  }

  .step-box .success-icon {
    right: 12px;
  }

  .step-box .success-icon img {
    width: 20px;
  }

  .step-body-box .form-group textarea.textarea-msg {
    height: 150px;
    padding: 10px 20px;
  }

  .delivery-pickup-list {
    flex-wrap: wrap;
  }

  .delivery-list-box {
    margin-bottom: 10px;
  }

  .modal-body .add-btn a.btn {
    padding: 3px 9px;
  }

  .modal-content button.btn-close {
    left: 16px;
  }

  #number-verification-popup .modal-body p {
    font-size: 16px;
    margin-bottom: 30px;
  }

  #number-verification-popup .more-option li {
    padding: 0 3px;
  }

  #number-verification-popup .more-option li a {
    font-size: 15px;
  }

  .accordion-header button span.check {
    left: -12px;
  }

  .accordion-header button span.check img {
    width: 10px;
    margin-top: -2px;
  }

  .add-items {
    padding: 0 13px;
  }

  .add-items button.btn {
    font-size: 15px;
    padding: 6px 15px;
  }
  .pay-icons-list{
    top: 0px;
  }
  .credit-card-list li img {
    width: 25px;
  }

}
