import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { BecomeRestaurant } from '../models/become-restaurant';
import { environment } from '../../../environments/environment';
import { ErrorHandler } from '../../shared/error-handler';

@Injectable({
  providedIn: 'root',
})
export class BecomeRestaurantService {
  private url = environment.apiBaseUrl + 'become-partners/';

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<BecomeRestaurant> {
    return this.http.get<BecomeRestaurant>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(become_restaurant: BecomeRestaurant): Observable<any> {
    return this.http.post<BecomeRestaurant>(this.url, BecomeRestaurant.toFormData(become_restaurant))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(become_restaurant: BecomeRestaurant): Observable<any> {
    return this.http.post<BecomeRestaurant>(this.url + become_restaurant.id, BecomeRestaurant.toFormData(become_restaurant))
      .pipe(catchError(ErrorHandler.handleError));
  }

  verify(become_restaurant: BecomeRestaurant): Observable<any> {
    return this.http.post<BecomeRestaurant>(this.url + become_restaurant.id + '/verify', BecomeRestaurant.toFormData(become_restaurant))
      .pipe(catchError(ErrorHandler.handleError));
  }

  resend(become_restaurant: BecomeRestaurant): Observable<any> {
    return this.http.post<BecomeRestaurant>(this.url + become_restaurant.id + '/resend', BecomeRestaurant.toFormData(become_restaurant))
      .pipe(catchError(ErrorHandler.handleError));
  }


  delete(id: string): Observable<any> {
    return this.http.delete<BecomeRestaurant>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }
}
