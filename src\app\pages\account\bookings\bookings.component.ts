import { <PERSON><PERSON><PERSON>cy<PERSON>ip<PERSON>, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit, HostListener, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { environment } from '../../../../environments/environment';
import { User } from '../../../core/models/user';
import { Booking } from '../../../core/models/booking';
import { BookingService } from '../../../core/services/booking.service';
import { NgForm } from '@angular/forms';
import { Restaurant } from '../../../core/models/restaurant';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
@Component({
  selector: 'app-bookings',
  host: { ngSkipHydration: 'true' },
  templateUrl: './bookings.component.html',
  styleUrls: ['./bookings.component.scss'],
})
export class BookingsComponent implements OnInit, OnDestroy {
  @ViewChild('targetUpcoming') targetUpcoming: any;
  @ViewChild('targetPrevious') targetPrevious: any;

  subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  previousBookings: Booking[] = [];
  upcomingBookings: Booking[] = [];
  booking: Booking = new Booking();
  modalOptions: NgbModalOptions;
  streetAddress: string;

  status: string = 'upcoming';

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isScrollLoading = false;
  scrolling: boolean = false;

  currentRate = 2;
  totalBookings = 0;
  last_page = 0;
  previousPage: any;
  page = 1;
  per_page = 10;

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    public bookingService: BookingService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    public sanitizer: DomSanitizer,
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    if (this.status == 'upcoming') {
      this.fetchUpcommingBookings();
    } else {
      this.fetchPreviousBookings();
    }
  }


  @HostListener("window:scroll", ["$event"])
  onScroll() {
    if (this.status == 'upcoming') {
      const height = this.targetUpcoming.nativeElement.offsetTop;

      if (window.pageYOffset >= height - 200 && !this.scrolling) {
        this.page = this.page + 1;
        this.scrolling = true;
        this.isScrollLoading = true;

        this.subs.add(
          this.bookingService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page, upcoming_status: this.status })
            .pipe(finalize(() => this.isScrollLoading = false))
            .subscribe(
              (res) => {
                this.upcomingBookings = this.upcomingBookings.concat(res.data);
                this.totalBookings = res.total;
                this.scrolling = false;
              }, (err) => { }
            )
        )
      }
    } else {
      const height = this.targetPrevious.nativeElement.offsetTop;

      if (window.pageYOffset >= height && !this.scrolling) {
        this.page = this.page + 1;
        this.scrolling = true;
        this.isScrollLoading = true;

        this.subs.add(
          this.bookingService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page, previous_status: this.status })
            .pipe(finalize(() => this.isScrollLoading = false))
            .subscribe(
              (res) => {
                this.previousBookings = this.previousBookings.concat(res.data);
                this.totalBookings = res.total;
                this.scrolling = false;
              }, (err) => { }
            )
        )
      }
    }
  }

  fetchUpcommingBookings() {
    this.isLoading = true;

    this.subs.add(
      this.bookingService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page, upcoming_status: this.status })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(
          (res) => {
            this.upcomingBookings = res.data;
            this.totalBookings = res.total;
            this.last_page = res.last_page;
          }, (err) => {
            this.upcomingBookings = [];
            this.fetchRestaurant();
          }
        )
    )
  }

  fetchPreviousBookings() {
    this.isLoading = true;

    this.subs.add(
      this.bookingService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page, previous_status: this.status })
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(
          (res) => {
            this.previousBookings = res.data;
            this.totalBookings = res.total;
            this.last_page = res.last_page;
          }, (err) => {
            this.previousBookings = [];
            this.fetchRestaurant();
          }
        )
    )
  }

  fetchRestaurant() {
    this.isLoading = false; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  redirectOnRest() {
    this.router.navigateByUrl('/location/' + this.restaurantService.zipcode);
  }

  statusChange(stat) {
    this.status = stat;
    this.page = 1;
    if (this.status == 'upcoming') {
      this.fetchUpcommingBookings();
    } else {
      this.fetchPreviousBookings();
    }
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.fetchUpcommingBookings();
    }
  }

  bookingView(model, booking) {
    this.booking = booking;
    this.openModal(model);
  }

  bookView(bookingId) {
    var bookingIds = btoa(bookingId);
    this.router.navigateByUrl(`/booking-detail/${bookingIds}`);
  }

  cancelBooking(model, booking: Booking) {
    this.booking = Object.assign({}, booking);
    this.openModal(model);
  }

  validateBooking(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.booking.status = 'Cancel';

    this.isModelLoading = true; this.Modelerror = false;

    this.subs.add(
      this.bookingService.change_status(this.booking)
        .pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            if (this.status == 'upcoming') {
              this.fetchUpcommingBookings();
            } else {
              this.fetchPreviousBookings();
            }
            this.modalService.dismissAll();
          }, (err) => {
            this.Modelerror = err
          }
        )
    )
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  bookingStore(booking) {
    localStorage.setItem(environment.googleFirebase, booking.restaurant_id);
    this.router.navigateByUrl('/' + booking?.restaurant?.seo_url + `/reservation`);
  }

  viewStore(booking) {
    localStorage.setItem(environment.googleFirebase, booking.restaurant_id);
    this.router.navigateByUrl('/' + booking?.restaurant?.city_name + '/' + booking?.restaurant?.seo_url + '/menus');
  }

  viewAddress(booking) {
    this.streetAddress = 'https://www.google.com/maps?q=' + booking.restaurant.sourcelatitude + ',' + booking.restaurant.sourcelongitude;
    window.open(this.streetAddress, '_blank');
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'dd MMM yyyy', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.modalService.dismissAll();
  }
}
