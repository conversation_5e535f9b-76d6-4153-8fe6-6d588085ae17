.image-upload {
  width: 128px;
  height: 128px;
  background-color: #eee;
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  overflow: hidden;
  border: 7px solid #d9d9d9;

  @media screen and (max-width:575px) {
    width: 100px !important;
    height: 100px !important;
  }

  .placeholder-icon {
    font-size: 48px;
    color: #bbb;
  }

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;
    transition: opacity 200ms;
    background-color: rgba($color: #000000, $alpha: 0.6);
    opacity: 1;

    .upload-icon {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24px;
      cursor: pointer;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transition: color 200ms;
      color: rgb(255, 255, 255);
    }

    .delete-icon {
      color: white;
      font-size: 16px;
      cursor: pointer;
      position: absolute;
      top: 5px;
      right: 5px;
      transition: color 200ms;

      &:hover {
        color: rgb(255, 159, 159);
      }
    }
  }

  // &:hover {
  //   .overlay {
  //     opacity: 1;
  //   }
  // }
}
