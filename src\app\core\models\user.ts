export class User {
  id: string; // 145729,
  role_id: string = '5'; // 3,
  username: string; // "<EMAIL>",
  current_password: string;
  password: string;
  confirmPassword: string;
  permissions: string; // "",
  sms_permissions: string; // "",
  first_name: string; // "CharlesSobMH",
  last_name: string; // "CharlesSobMH",
  phone_number: string; // "89911922452",
  address: string; // "",
  referral_code: string; // "CHARLESSOBMH8229",
  referred_by: string; // "",
  wallet_amount: number; // 0,
  image: string; // "",
  cover_image: string; // "",
  newsletter: string; // "N",
  device_type: string; // null,
  device_id: string; // "",
  status: string; // "1",
  deleted_status: string; // "N",
  created: string; // "2021-07-13 04:02:09",
  modified: string; // "2021-07-13 04:02:09",
  social_id: string; // null,
  otp: string; // "",
  email_otp: string; // "",
  verify_type: string; // "",
  email_verify: boolean; // 0,
  phone_verify: boolean; // 0,
  otp_send: boolean; // 0,
  ip_address: string; // "***********"

  thumb_url: string;
  image_url: string;

  imageFile: any;

  //Notification
  order_place: number;
  order_accept: number;
  order_reject: number;
  order_delivered: number;
  book_table: number;
  book_status: number;
  important_update: number;

  // Email Notification
  email_order_place: boolean;
  email_order_accept: boolean;
  email_order_reject: boolean;
  email_order_delivered: boolean;
  email_book_table: boolean;
  email_book_status: boolean;
  email_important_update: boolean;

  // SMS Notification
  sms_order_place: boolean;
  sms_order_accept: boolean;
  sms_order_reject: boolean;
  sms_order_delivered: boolean;
  sms_book_table: boolean;
  sms_book_status: boolean;
  sms_important_update: boolean;

  // App Notification
  app_order_place: boolean;
  app_order_accept: boolean;
  app_order_reject: boolean;
  app_order_delivered: boolean;
  app_book_table: boolean;
  app_book_status: boolean;
  app_important_update: boolean;

  static loginForm(user: User) {
    const formData = new FormData();
    if (user.username) formData.append('username', user.username);
    if (user.password) formData.append('password', user.password);
    if (user.role_id) formData.append('role_id', user.role_id);
    return formData;
  }
  static signupForm(user: User) {
    const formData = new FormData();
    if (user.role_id) formData.append('role_id', user.role_id);
    if (user.social_id) formData.append('social_id', user.social_id);
    if (user.last_name) formData.append('last_name', user.last_name);
    if (user.first_name) formData.append('first_name', user.first_name);
    if (user.phone_number) formData.append('phone_number', user.phone_number);
    if (user.username) formData.append('username', user.username);
    if (user.password) formData.append('password', user.password);
    if (user.confirmPassword) formData.append('confirm_password', user.confirmPassword);
    if (user.referred_by) formData.append('referred_by', user.referred_by);
    return formData;
  }

  static toFormData(user: User) {
    const formData = new FormData();

    if (user.id) formData.append('id', user.id);
    if (user.role_id) formData.append('role_id', user.role_id);
    if (user.first_name) formData.append('first_name', user.first_name);
    if (user.last_name) formData.append('last_name', user.last_name);
    if (user.phone_number) formData.append('phone_number', user.phone_number);
    if (user.current_password) formData.append('current_password', user.current_password);
    if (user.password) formData.append('password', user.password);
    if (user.confirmPassword) formData.append('confirm_password', user.confirmPassword);
    if (user.imageFile) formData.append('image', user.imageFile, user.imageFile.name)
    if (user.otp) formData.append('otp', user.otp);
    if (user.email_otp) formData.append('email_otp', user.email_otp);
    if (user.verify_type) formData.append('verify_type', user.verify_type);
    if (user.newsletter) formData.append('newsletter', user.newsletter);
    formData.append('email_verify', user.email_verify ? '1' : '0');
    formData.append('order_place', user.order_place.toString());
    formData.append('order_accept', user.order_accept.toString());
    formData.append('order_reject', user.order_reject.toString());
    formData.append('order_delivered', user.order_delivered.toString());
    formData.append('book_table', user.book_table.toString());
    formData.append('book_status', user.book_status.toString());
    formData.append('important_update', user.important_update.toString());

    return formData;
  }

  static toPhoneVerifyFormData(user: User) {
    const formData = new FormData();
    // formData.append('email_verify', user.email_verify ? '1' : '0');
    formData.append('phone_verify', '0');
    return formData;
  }

  static toForgotMailFormData(user: User) {
    const formData = new FormData();
    if (user.username) formData.append('email', user.username);
    if (user.role_id) formData.append('role_id', user.role_id);
    return formData;
  }
}
