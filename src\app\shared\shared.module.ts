import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RequestInterceptor } from './request.interceptor';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzNotificationModule } from 'ng-zorro-antd/notification';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NZ_I18N, en_US } from 'ng-zorro-antd/i18n';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { PaginationComponent } from '../pagination/pagination.component';
import { NzRateModule } from 'ng-zorro-antd/rate';
import { ImageUploadComponent } from './image-upload/image-upload.component';
import { IconsProviderModule } from '../icons-provider.module';
@NgModule({
  declarations: [PaginationComponent, ImageUploadComponent],
  imports: [
    CommonModule,
    FormsModule,
    NzFormModule,
    NzLayoutModule,
    NzMenuModule,
    NzInputModule,
    NzUploadModule,
    HttpClientModule,
    IconsProviderModule
  ],
  exports: [CommonModule, FormsModule, ImageUploadComponent, PaginationComponent, NzLayoutModule, NzIconModule, NzMenuModule, NzCheckboxModule, NzTagModule,
    NzFormModule, NzNotificationModule, NzMessageModule, NzInputModule, NzPopconfirmModule, NzButtonModule, NzDividerModule, NzPaginationModule, NzUploadModule, NzResultModule, NzTableModule, NzCardModule, NzAvatarModule,
    NzSelectModule, NzTreeSelectModule, NzTabsModule, NzModalModule, NzPopoverModule, NzTimePickerModule, NzRadioModule, NzSwitchModule, NzBadgeModule,
    NzInputNumberModule, NzToolTipModule, NzEmptyModule, NzStatisticModule, NzSpinModule, NzDatePickerModule, NzDrawerModule, NzSkeletonModule, NzRateModule],
  providers: [
    { provide: NZ_I18N, useValue: en_US },
    { provide: HTTP_INTERCEPTORS, useClass: RequestInterceptor, multi: true },
  ],
})

export class SharedModule { }
