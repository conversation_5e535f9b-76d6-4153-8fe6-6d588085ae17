.contact-preferences-box{
  padding: 40px 80px 65px 80px;
  border-radius: 15px;
  background: #fff;
  box-shadow: 0 0 30px 1px #0000001a;
}

.contact-preferences-box .main-heading{
  text-align: center;
  margin-bottom: 90px;
}
.contact-preferences-box .main-heading h6{  
  font-size: 30px;
  color:#000000;
  font-weight: 700;
  font-family: 'Visby CF';
  line-height: 32px;
  text-align: center;
}

.contact-preferences-checkbox{
  padding:30px 35px;
  box-shadow: 0px 0px 10px 4px #0000001A;
  border-radius: 10px;
  margin-bottom: 85px;
}
.form-check{
  display: flex;
  padding-left:0;
  margin-bottom:0;
  line-height: 32px;
  min-height: 34px;
}
.form-check input.form-check-input{ 
  width: 32px;
  height: 32px;
  border-radius: 5px;
  border: 2px solid #D9D9D9;
  margin-top:0;
  box-shadow: none;
  margin-left:0;
  margin-right: 30px;
}
.form-check label.form-check-label{
  width: 100%;
  font-family: 'Visby CF';
  font-size: 20px;
  color:#000000;
  font-weight: 700;
  line-height: 32px;
}
.form-check-input:checked{
  background-color: #14A411;
  border-color: #14A411 !important;   
}
.form-check-input:checked + .form-check-label{
  color:#14A411;
}
.save-preferences-btn a.btn{
  width: 228px;
}

@media screen and (max-width:1500px) {
.contact-preferences-box{
  padding: 30px 80px 48px 80px;
  border-radius: 12px;
} 
.contact-preferences-box .main-heading {
  margin-bottom: 60px;
}
.contact-preferences-box .main-heading h6 {
  font-size: 22px;
}
.contact-preferences-checkbox{
  padding:22px 24px;
  margin-bottom: 64px;
}
.form-check{
  line-height: 25px;
  min-height: 25px;
}
.form-check input.form-check-input {
  width: 25px;
  height: 25px;
  margin-right: 25px;
}
.form-check label.form-check-label{
  font-size: 18px;
  line-height: 25px;
}  
.save-preferences-btn a.btn{
  font-size: 18px;
  padding:11px 15px;
}

}

@media screen and (max-width:1199px) {
.contact-preferences-box .main-heading {
  margin-bottom: 50px;
}  
.contact-preferences-box{
  padding: 30px 60px 40px;
  border-radius: 10px;
} 
.contact-preferences-checkbox{
  padding: 20px 20px;
  margin-bottom: 50px;
}
.form-check input.form-check-input {
  width: 22px;
  height: 22px;
  margin-right: 15px;
} 
.form-check label.form-check-label {
  font-size: 16px;
  line-height: 20px;
}
.save-preferences-btn a.btn {
  width: 210px;
}

}

@media screen and (max-width:991px) {
.contact-preferences-box{
    padding: 20px 30px 30px;
}
.contact-preferences-box .main-heading {
  margin-bottom: 40px;
}
.contact-preferences-checkbox{
  padding: 15px;
  margin-bottom: 40px;
}
.form-check input.form-check-input {
  width: 20px;
  height: 20px;
  margin-top: 2px;
}
.form-check label.form-check-label {
  font-size: 14px;
  line-height: 22px;
}
.save-preferences-btn a.btn{
  font-size: 14px;
  padding: 5px 15px;
  width: 180px;  
}

}

@media screen and (max-width:480px) {
.contact-preferences-box{
  padding: 15px 20px 20px 20px;
} 
.contact-preferences-box .main-heading {
  margin-bottom: 20px;
} 
.contact-preferences-checkbox{
  margin-bottom: 20px;
}
.save-preferences-btn a.btn {
  width: 160px;
}
.form-check input.form-check-input {
  width: 16px;
  height: 16px;
  margin-top: 4px;
  margin-right: 10px;
}  

}