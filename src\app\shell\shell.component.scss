$primary: #FC353A;

:root {
  --primary: #FC353A;
}

.header-bg {
  width: 100%;
  padding: 30px 178px;
  position: absolute;
  z-index: 123;
  left: 0;
  right: 0;  
}
.header-bg .container{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-left {
  display: flex;
  align-items: center;
}

button.toggle {
  width: 38px;
  height: 32px;
  background-color: transparent;
  padding: 0;
  border: 0;
  position: relative;
  top:13px;
  margin-right: 40px;
}

button.toggle span {
  width: 38px;
  height: 5px;
  position: absolute;
  top: 13px;
  left: 0;
  right: 0;
  display: inline-block;
  background-color: #fff;
  border-radius: 10px;
  transition-duration: 0.5s;
}

button.toggle::before {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  content: '';
  width: 38px;
  height: 5px;
  background-color: #fff;
  border-radius: 10px;
  transition-duration: 0.5s;
}

button.toggle::after {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  content: '';
  width: 38px;
  height: 5px;
  background-color: #fff;
  border-radius: 10px;
  transition-duration: 0.5s;
}
.logo button{
  padding:0;
  background-color: transparent;
  border:0;
}
.logo button img {
  max-width: 130px;
}

.logo button img:hover {
  -webkit-animation-name: buzz-out-on-hover;
  animation-name: buzz-out-on-hover;
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

.logo button img.red-logo {
  display: none;
}

.navigation-box {
  padding-left: 85px;
}

.navigation-box ul {
  display: flex;
  margin: 0;
  padding: 5px 7px;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.4);
}

.navigation-box ul li a {
  font-size: 17px;
  color: #fff;
  cursor: pointer;
  font-weight: 600;
  text-decoration: none;
  line-height: 20px;
  display: inline-block;
  padding: 10px 22px;
  border-radius: 50px;
}

.navigation-box ul li:last-child a {
  padding-left: 15px;
  padding-right: 15px;
}

.navigation-box ul li.active a,
.navigation-box ul li a:hover {
  color: $primary;
  background: #fff;
}

.header-right ul {
  margin: 0;
  display: flex;
  align-items: center;
}

.header-right ul li {
  margin-left: 16px;
}

.header-right ul li:first-child {
  margin-left: 0;
}
.header-right ul.account-dropdown-list li button,
.header-right ul li a {
  font-size: 17px;
  color: #fff;
  display: inline-block;
  text-decoration: none;
  font-weight: 600;
  line-height: 20px;
  padding: 8px 20px;
  border-radius: 50px;
  border: 2px solid #fff;
}

.header-right ul li a:hover {
  color: $primary;
  background: #fff;
}

.header-right ul li.add-cart-btn button {
  font-size: 17px;
  font-weight: 800;
  color: $primary;
  padding: 6px 20px;
  background-color: #fff;
  cursor: pointer;
  border: 2px solid #fff;
  border-radius: 25px;
}

.header-right ul li.add-cart-btn button img {
  width: 22px;
  margin-right: 5px;
  margin-top: -5px;
}

.header-right ul li.add-cart-btn button img.white-cart-image {
  display: none;
}

.navigation-dropdown {
  position: fixed;
  z-index: 123;
  border: 0;
  border-radius: 0;
  padding: 20px 25px 30px 25px;
  width: 300px;
  background-color: #fff;
  top: 0;
  bottom: 0;
  left: -100%;
  box-shadow: 0 0 5px 0 #e1e1e1;
  visibility: hidden;
  opacity: 0;
  transition: all 0.5s;
  overflow: hidden;
  overflow-y: auto;
}

.navigation-dropdown::-webkit-scrollbar {
  width: 5px;
}

.navigation-dropdown::-webkit-scrollbar-track {
  background: #ffe6e7;
}

.navigation-dropdown::-webkit-scrollbar-thumb {
  background: $primary;
}

.navigation-dropdown::-webkit-scrollbar-thumb:hover {
  background: $primary;
}

.navigation-dropdown.open {
  left: 0;
  visibility: visible;
  opacity: 1;
}

.navigation-dropdown button.close {
  font-size: 32px;
  color: $primary;
  padding: 0;
  padding-top: 5px;
  border: 0;
  border-radius: 0;
  position: relative;
  z-index: 12;
  background-color: transparent;
  margin-bottom: 70px;
}

.navigation-dropdown .logo {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  padding-top: 25px;
}

.navigation-dropdown .logo img {
  width: 118px;
}

.navigation-list {
  margin-bottom: 110px;
}

.navigation-list ul {
  margin: 0;
}

.navigation-list ul li {
  margin-bottom: 22px;
}

.navigation-list ul li button.dropdown-item,
.navigation-list ul li a.dropdown-item {
  font-size: 22px;
  color: #000000;
  font-weight: 600;
  background-color: transparent;
  padding: 0;
}
.navigation-list ul li.active button.dropdown-item,
.navigation-list ul li button.dropdown-item:hover,
.navigation-list ul li.active a.dropdown-item,
.navigation-list ul li a.dropdown-item:hover {
  color: $primary;
}

.fixed-header .header-bg {
  padding-top: 30px;
  padding-bottom: 30px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 2px 10px 0 #a3a3a3;
}

.fixed-header button.toggle::after,
.fixed-header button.toggle::before,
.fixed-header button.toggle span {
  background-color: $primary;
}

.fixed-header .logo button img.red-logo {
  display: inline-block;
}

.fixed-header .logo button img.white-logo {
  display: none;
}

.fixed-header .header-bg .navigation-box ul {
  background-color: #F4F3F3;
}

.fixed-header .header-bg .navigation-box ul li a {
  color: #000000;
}

.fixed-header .header-bg .navigation-box ul li.active a,
.fixed-header .header-bg .navigation-box ul li a:hover {
  color: #fff;
  background-color: $primary;
}

.fixed-header .header-right ul li a {
  color: #000000;
  background-color: #F4F3F3;
  border-color: #F4F3F3;
}

.fixed-header .header-right ul li a:hover {
  color: #000000;
  background-color: transparent;
}

.header-right li.account-dropdown {
  position: relative;
}

.header-right ul li.account-dropdown>a {
  display: flex;
  align-items: center;
}

.header-right ul li.account-dropdown a span {
  max-width: 9ch;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  padding-right: 2px;
}

.header-right .account-dropdown-content {
  position: absolute;
  top: 45px;
  right: 0;
  padding-top: 20px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s;
}

.header-right ul.account-dropdown-list {
  position: initial;
  display: block;
  border: 0;
  border-radius: 15px;
  overflow: hidden;
}

.header-right ul.account-dropdown-list.show {
  display: block;
}

.header-right .account-dropdown-content::before {
  position: absolute;
  top: 5px;
  right: 15px;
  content: '';
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid #fff;
}

.header-right ul.account-dropdown-list li {
  margin: 0;
}
.header-right ul.account-dropdown-list li button,
.header-right ul.account-dropdown-list li a {
  font-size: 18px;
  color: #000000;
  border: 0;
  border-radius: 0;
}
.header-right ul.account-dropdown-list li button:hover,
.header-right ul.account-dropdown-list li a:hover {
  color: #FFFFFF;
  background: #ff4c51;
}

.header-right li.account-dropdown:hover .account-dropdown-content {  
  opacity: 1;
  visibility: visible;
  transition-delay: 0.5s;
}

.header-two .header-right li.account-dropdown .account-dropdown-content {
  top: 35px;
}

.header-two .header-right ul.account-dropdown-list {
  background-color: #f4f3f3;
}

.header-two .header-right ul.account-dropdown-list li a {
  padding: 10px 25px;
  color: #000;
  border-color: #f4f3f3;
  background-color: #f4f3f3;
}

.header-two .header-right ul.account-dropdown-list li button:hover,
.header-two .header-right ul.account-dropdown-list li a:hover {
  color: #fff;
  background-color: #505050;
}

.header-two .header-right .account-dropdown-content::before {
  border-bottom-color: #f4f3f3;
}

.red-header .header-right ul.account-dropdown-list {
  background-color: #fc353a;
}
.red-header .header-right ul.account-dropdown-list li button,
.red-header .header-right ul.account-dropdown-list li a {
  color: #fff;
  border-color: #fc353a;
  background-color: #fc353a;
}

.red-header .header-right ul.account-dropdown-list li button:hover,
.red-header .header-right ul.account-dropdown-list li a:hover {
  color: #fc353a;
  background-color: #ffe1e2;
}

.red-header .header-right .account-dropdown-content::before {
  border-bottom-color: #fc353a;
}
.red-header .header-right ul li button:hover, 
.red-header .header-right ul li button.basket-toggle-btn {
    color: #fff;
    background-color: #fc353a;
}
.black-header .header-right ul.account-dropdown-list {
  background-color: #000;
}
.black-header .header-right ul.account-dropdown-list li button,
.black-header .header-right ul.account-dropdown-list li a {
  color: #fff;
  border: 0;
  border-color: #000;
  background-color: #000;
}

.black-header .header-right ul.account-dropdown-list li button:hover,
.black-header .header-right ul.account-dropdown-list li a:hover {
  color: #000;
  background-color: #e9e9e9;
}

.black-header .header-right .account-dropdown-content::before {
  border-bottom-color: #000;
}

.your-restaurant-box p {
  font-size: 22px;
  color: #000000;
  font-weight: 600;
  margin-bottom: 24px;
}

.your-restaurant-box p a {
  color: #000000;
  text-decoration: none;
}

.your-restaurant-box p a:hover {
  color: #fc353a;
}

.download-grubz-app {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.download-grubz-app .grubz-app-icon {
  width: 80px;
  min-width: 80px;
  height: 80px;
  min-height: 80px;
  line-height: 75px;
  text-align: center;
  border-radius: 12px;
  box-shadow: 0 0 10px 5px #dbdbdb;
  background-color: $primary;
}

.download-grubz-app .grubz-download-app-text {
  padding-left: 15px;
}

.download-grubz-app .grubz-download-app-text p {
  font-size: 13px;
  color: #000000;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 0;
}

.app-buttons ul {
  display: flex;
  margin-bottom: 0;
}

.app-buttons ul li {
  margin-right: 22px;
}

.app-buttons ul li a {
  width: 100px;
  height: 40px;
  color: #000000;
  font-size: 12px;
  font-weight: bold;
  line-height: 40px;
  display: inline-block;
  border-radius: 10px;
  text-decoration: none;
  background: #F4F3F3;
  position: relative;
}

.app-buttons ul li a img {
  width: auto;
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translate(0, -50%);
  animation-name: initial;
}

.app-buttons ul li a.ios-btn {
  padding-left: 52px;
}

.app-buttons ul li a.android-btn {
  padding-left: 42px;
}

/*-------Header-2-CSS-------*/
.header-bg.header-two {
  padding-top: 30px;
  padding-bottom: 30px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-bottom: 2px solid #F4F3F3;
}

.header-two .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-two button.toggle::after,
.header-two button.toggle::before,
.header-two button.toggle span {
  background-color: $primary;
}

.header-two button.toggle {
  margin-right: 50px;
}

.header-two button.toggle.open span {
  background: transparent;
}

.header-bg.header-two .logo button img {
  max-width: 130px;
}

.header-two .navigation-box {
  padding-left: 85px;
}

.header-two .navigation-box ul {
  padding: 6px 15px;
  background-color: #F4F3F3;
}

.header-two .navigation-box ul li a {
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  padding: 8px 17px;
}

.header-two .navigation-box ul li.active a,
.header-two .navigation-box ul li a:hover {
  color: #000;
  background-color: #fff;
  box-shadow: 0px 0px 9px 0px #00000026;
}

.header-two .header-right ul li.add-cart-btn {
  margin-right: 66px;
  padding-left: 193px;
}

.header-two .header-right ul li a {
  font-size: 18px;
  color: #000000;
  padding: 6px 28px;
  background-color: #F4F3F3;
  border-color: #F4F3F3;
}

.header-two .header-right ul li button.basket-toggle-btn {
  width: 100px;
  padding: 4px 10px;
  color: #FFFFFF;
  background-color: #EA3323 !important;
  border-color: #EA3323;
  text-align: center;
}

.header-two .header-right ul li.add-cart-btn button img.red-cart-image {
  display: none;
}

.header-two .header-right ul li.add-cart-btn button img.white-cart-image {
  display: inline-block;
}

.header-two .header-right ul li a:hover {
  background-color: transparent;
}

.header-middle-box {
  display: flex;
  align-items: center;
}

.header-middle-box .location-box {
  position: relative;
  margin-bottom: 0;
  margin-right: 35px;
}

.location-search-box {
  position: absolute;
  left: 0;
  right: 0;
  top: 50px;
  border: 10px;
  background-color: #fff;
  padding: 10px;
  box-shadow: 0 0 10px 0 #dfdfdf;
  border-radius: 10px;
  width: 250px;
  z-index: 12;
}

.search-for-restaurant input.form-control {
  padding-right: 40px;
}

.location-search-box .search-for-restaurant svg {
  font-size: 16px;
}

.location-search-box .search-for-restaurant input.form-control {
  font-size: 13px;
  width: 100%;
  height: 36px;
  padding: 0 50px 0 40px;
}

.location-search-box .search-for-restaurant button.btn {
  width: 44px;
  height: 36px;
  padding: 0;
  text-align: center;
  right: 0;
  border-radius: 0 20px 20px 0;
}

.location-search-box .search-for-restaurant button.btn svg {
  left: 8px;
}

/*-----black-header-----*/
.black-header {

  button.toggle span,
  button.toggle:after,
  button.toggle:before {
    background-color: #000000;
  }

  .navigation-box ul {
    background: #fff;
  }

  .navigation-box ul li a {
    color: #000000;
  }

  .navigation-box ul li a:hover,
  .navigation-box ul li.active a {
    color: #fff;
    background-color: #000000;
  }

  .header-right ul li a {
    color: #000;
    border: 2px solid #000;
  }

  .header-right ul li a:hover {
    color: #fff;
    background: #000;
  }

  .header-right ul li.add-cart-btn button {
    color: #fff;
    background-color: #000;
  }

  .navigation-dropdown button.close {
    color: #000;
  }

  .navigation-list ul li a.dropdown-item {
    color: #000;

    &:hover {
      color: #fc353a;
    }
  }

  .download-grubz-app .grubz-app-icon {
    background-color: #000;
  }

  .navigation-dropdown::-webkit-scrollbar-track {
    background: #dddddd;
  }

  .navigation-dropdown::-webkit-scrollbar-thumb {
    background: #000;
  }

  .navigation-dropdown::-webkit-scrollbar-thumb:hover {
    background: #000;
  }

}

/*-----Red-header-----*/
.red-header {

  button.toggle:after,
  button.toggle:before,
  button.toggle span {
    background-color: #fc353a;
  }

  .navigation-box ul {
    background-color: #FFFFFF;
  }

  .navigation-box ul li a {
    color: #FC353A;
  }

  .navigation-box ul li.active a,
  .navigation-box ul li a:hover {
    background-color: #FC353A;
    color: #fff;
  }

  .header-right ul li a {
    color: #FC353A;
    border-color: #FC353A;
  }

  .header-right ul li a:hover {
    color: #fff;
    background-color: #FC353A;
  }

  .header-right ul li a.basket-toggle-btn {
    color: #fff;
    background-color: #fc353a;
  }
}

.search-close {
  position: absolute;
  right: 15px;
  top: 20px;
  left: auto;
  font-size: 14px !important;
}

.location-box ul li {
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 11;
}

.location-box ul li button {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding:0;
  border:0;  
  color: #8b8f8f;
  font-weight: 600;
  text-decoration: underline;
  background-color: transparent;
}

/*---------Popup-CSS---------*/
.forgot-password {
  padding-bottom: 25px;
}

.forgot-password span {
  font-size: 18px;
  color: #fc353a;
  font-family: 'Visby CF';
  font-weight: 600;
  text-decoration: none;
}

.form-group label.form-label {
  font-size: 18px;
  color: #212529;
  font-weight: 700;
}

.form-otp-list {
  display: flex;
}

.form-otp-list input.form-control {
  width: 48px;
  padding-left: 10px;
  padding-right: 10px;
  text-align: center;
  margin-right: 8px;
}

.resend-code {
  padding-top: 15px;
}

.resend-code a {
  font-size: 18px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

.back-to-login {
  font-size: 18px;
  font-family: 'Visby CF';
  font-weight: 600;
  text-align: center;
  padding-top: 20px;
}

.back-to-login span {
  color: #000;
  padding-right: 5px;
}

.back-to-login a {
  color: #fc353a;
  text-decoration: none;
}

/*---------Media-css---------*/
.signup-here-error-text {
  color: #f5222d;
}

.account-dropdown a.active {
  background-color: #fff !important;
  border-color: $primary !important;
  color: $primary !important;
}
.footer-box button{
  padding:0;
  border:0;
  background-color: transparent;
  outline: none;
}
.footer-box ul li button{
  padding:0;
  border:0;
  background-color: transparent;
  outline: none;
}

@media screen and (max-width:1800px) {
  .header-bg {
    padding: 40px 130px;
  }

  button.toggle {
    width: 42px;
    height: 34px;
    margin-right: 30px;
  }

  button.toggle span {
    top: 14px;
    width: 42px;
  }

  button.toggle::after,
  button.toggle::before {
    width: 42px;
  }

  .logo button img {
    width: 130px;
  }

  .navigation-box {
    padding-left: 40px;
  }

  .navigation-box ul {
    padding: 6px 8px;
  }

  .navigation-box ul li a {
    font-size: 16px;
    padding: 9px 16px;
  }

  .header-right ul li a {
    font-size: 16px;
    padding: 8px 18px;
  }

  button.toggle.open::after {
    transform: rotateZ(-45deg) scaleX(1.25) translate(9px, -10px);
  }

  button.toggle.open::before {
    transform: rotateZ(45deg) scaleX(1.25) translate(8px, 8px);
  }

  .navigation-dropdown button.close {
    margin-bottom: 80px;
  }

  .navigation-list {
    margin-bottom: 80px;
  }

  .header-two .navigation-box {
    padding-left: 40px;
  }

  .header-two .navigation-box ul {
    padding: 6px 8px;
  }
  .header-right ul.account-dropdown-list li button,
  .header-right ul.account-dropdown-list li a {
    padding: 10px 22px;
  }

  .header-right ul.account-dropdown-list:before {
    top: -14px;
  }

}

@media screen and (max-width:1750px) {
  .header-middle-box .location-box {
    margin-right: 16px;
  }

  .header-two button.toggle {
    margin-right: 30px;
  }

  .header-right ul li.add-cart-btn {
    margin-right: 0px;
  }

  .header-right ul li.add-cart-btn button {
    font-size: 18px;
    padding: 7px 18px;
  }

  .header-right ul li.add-cart-btn button img {
    width: 22px;
  }

  .header-right ul li {
    margin-left: 10px;
  }

  .header-two .header-right ul li.add-cart-btn {
    margin-right: 26px;
    padding-left: 165px;
  }

}

@media screen and (max-width:1500px) {
  body {
    padding: 17px 28px 25px 28px;
  }

  .header-bg {
    padding: 20px 120px;
  }

  button.toggle {
    width: 30px;
    height: 24px;
  }

  button.toggle span {
    top: 10px;
    width: 30px;
    height: 4px;
  }

  button.toggle:after,
  button.toggle:before {
    width: 30px;
    height: 4px;
  }

  .logo button img {
    width: 98px;
  }

  .navigation-box {
    padding-left: 65px;
  }

  .navigation-box ul {
    padding: 5px 5px;
  }

  .navigation-box ul li a {
    font-size: 13px;
    padding: 5px 15px;
  }

  .search-close {
    top: 18px;
  }

  .header-right ul li a {
    font-size: 13px;
    padding: 4px 14px;
  }

  .header-right ul li.add-cart-btn button {
    font-size: 13px;
    padding: 4px 14px;
  }

  .header-right ul li.add-cart-btn button img {
    width: 18px;
    margin-top: -6px;
  }

  .header-two button.toggle {
    margin-right: 35px;
  }

  .header-bg.header-two .logo button img {
    max-width: 98px;
  }

  .header-middle-box .location-box {
    padding: 7px 15px 5px 15px;
    margin-right: 55px;
  }

  .header-middle-box .location-box ul li.dot {
    margin: 0 17px;
  }

  .header-middle-box .location-box ul li svg.fa-circle {
    width: 3px;
    font-size: 3px;
    top: -2px;
  }

  .header-middle-box .location-box ul li svg.fa-location-dot {
    width: 14px;
    font-size: 20px;
    top: 0px;
    margin-right: 15px;
  }

  .header-middle-box .location-box ul li {
    font-size: 13px;
  }

  .header-middle-box .search-for-restaurant svg {
    font-size: 18px;
  }

  .header-middle-box .search-for-restaurant input.form-control {
    font-size: 13px;
    width: 375px;
    height: 36px;
    padding: 0 40px 0 45px;
  }

  .header-bg.header-two {
    padding: 22px 120px;
  }

  .header-two .navigation-box {
    padding-left: 60px;
  }

  .header-two .navigation-box ul {
    padding: 5px 5px;
  }

  .header-two .navigation-box ul li a {
    font-size: 13px;
    padding: 4px 14px;
  }

  .header-bg.header-two .header-right ul li {
    margin-left: 16px;
  }

  .header-two .header-right ul li a {
    font-size: 16px;
    padding: 4px 25px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    width: 90px;
    padding: 4px 10px;
  }

  .header-right .account-dropdown-content {
    padding-top: 18px;
  }

  .header-right li.account-dropdown .account-dropdown-content {
    top: 34px;
  }

  .header-right .account-dropdown-content:before {
    top: 3px;
  }

  .header-right ul.account-dropdown-list {
    border-radius: 12px;
  }
  .header-right ul.account-dropdown-list li button,
  .header-right ul.account-dropdown-list li a {
    font-size: 16px;
    padding: 8px 20px;
  }

  .header-two .header-right li.account-dropdown .account-dropdown-content {
    top: 34px;
  }

  .header-two .header-right .account-dropdown-content ul li {
    margin-left: 0;
  }

  .header-two .header-right ul.account-dropdown-list li a {
    padding: 8px 20px;
  }

  .navigation-dropdown .logo {
    padding-top: 20px;
  }

  .navigation-dropdown button.close,
  .navigation-list {
    margin-bottom: 60px;
  }

  .navigation-list ul li {
    margin-bottom: 20px;
  }
  .navigation-list ul li button.dropdown-item,
  .navigation-list ul li a.dropdown-item {
    font-size: 18px;
  }

  .your-restaurant-box p {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .download-grubz-app .grubz-app-icon {
    width: 70px;
    min-width: 70px;
    height: 70px;
    min-height: 70px;
    line-height: 65px;
  }

  .download-grubz-app .grubz-app-icon img {
    width: 40px;
  }

  .download-grubz-app .grubz-download-app-text {
    padding-left: 12px;
  }

  .app-buttons ul li a {
    height: 38px;
    line-height: 38px;
  }

  .location-search-box .search-for-restaurant input.form-control {
    width: 100%;
    padding-right: 55px;
  }

  .form-group label.form-label {
    font-size: 14px;
  }

  .form-otp-list input.form-control {
    width: 36px;
  }

  .forgot-password {
    padding-bottom: 20px;
  }

  .forgot-password span {
    font-size: 14px;
  }

  .resend-code {
    padding-top: 10px;
  }

  .resend-code a {
    font-size: 14px;
  }

  .back-to-login {
    font-size: 14px;
    padding-top: 15px;
  }

  .signup-here-error-text {
    font-size: 13px;
  }
  .header-two .header-right ul li button.basket-toggle-btn {
    width: 90px;
  }

}

@media screen and (max-width:1399px) {
  .navigation-box {
    padding-left: 40px;
  }


}

@media screen and (max-width:1330px) {
  button.toggle {
    margin-right: 25px;
  }

  .navigation-box {
    padding-left: 8px;
  }

  .navigation-box ul li a {
    font-size: 14px;
    padding: 6px 15px;
  }

  .header-middle-box .location-box {
    margin-right: 25px;
  }

  .header-middle-box .search-for-restaurant input.form-control {
    width: 300px;
  }

  .location-search-box .search-for-restaurant input.form-control {
    width: 100%;
  }
}

@media screen and (max-width:1300px) {
  .header-bg {
    padding: 20px 100px;
  }

  button.toggle {
    margin-right: 20px;
  }

  .navigation-box ul li a {
    font-size: 14px;
    padding: 5px 10px;
  }

  .header-right ul li.add-cart-btn button {
    font-size: 15px;
    padding: 3px 16px;
  }

  .header-two button.toggle {
    margin-right: 25px;
  }

}

@media screen and (max-width:1199px) {
  .main-heading h2 {
    font-size: 45px;
    line-height: 55px;
    margin-bottom: 30px;
  }
  .header-bg.header-two {
    padding: 22px 65px;
  }  
  button.toggle{
    margin-right: 15px;
  }
  .navigation-box ul li a {
    font-size: 13px;
    padding: 4px 10px;
  }
  .header-right ul li {
    margin-left: 5px;
  }
  .header-bg.header-two .header-right ul li {
    margin-left: 10px;
  }
  .header-right ul li a {
    font-size: 13px;
    padding: 3px 12px;
  }
  .header-right ul li.add-cart-btn button {
    font-size: 13px;
    padding: 3px 12px;
  }
  .header-right ul li.add-cart-btn button img {
    width: 16px;
    margin-right: 4px;
    margin-top: -5px;
  }
  .navigation-list ul li button.dropdown-item,
  .navigation-list ul li a.dropdown-item {
    font-size: 16px;
  }
  .header-right li.account-dropdown .account-dropdown-content{
    display: none;
  }
  .your-restaurant-box p {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .navigation-dropdown {
    padding: 15px 20px 20px 20px;
    width: 250px;
  }

  .app-buttons ul li a {
    width: 90px;
  }

  .app-buttons ul li a.ios-btn {
    padding-left: 47px;
  }

  .app-buttons ul li a.android-btn {
    padding-left: 37px;
  }

  .download-grubz-app .grubz-app-icon {
    width: 70px;
    min-width: 70px;
    height: 70px;
    min-height: 70px;
    line-height: 65px;
    border-radius: 10px;
  }

  .download-grubz-app .grubz-download-app-text {
    padding-left: 10px;
  }

  .download-grubz-app .grubz-download-app-text p {
    font-size: 12px;
  }

  .app-buttons ul li {
    margin-right: 15px;
  }

  .app-buttons ul li:last-child {
    margin-right: 0;
  }

  .navigation-dropdown button.close {
    font-size: 28px;
    margin-left: 5px;
    margin-bottom: 40px;
  }

  .navigation-dropdown .logo {
    padding-top: 20px;
  }

  .navigation-dropdown .logo img {
    width: 92px;
  }

  .header-two button.toggle {
    margin-right: 15px;
  }

  .header-middle-box .location-box {
    padding-top: 6px;
    margin-right: 15px;
  }

  .header-middle-box .location-box ul li svg.fa-location-dot {
    margin-right: 10px;
  }

  .header-middle-box .location-box ul li.dot {
    margin: 0 15px;
  }

  .header-middle-box .search-for-restaurant input.form-control {
    width: 250px;
    height: 34px;
  }
  .header-middle-box .search-for-restaurant svg {
    font-size: 16px;
  }
  .header-two .header-right ul li.add-cart-btn {
    padding-left: 85px;
  }

  .header-two .navigation-box {
    padding-left: 20px;
  }

  .header-two .navigation-box ul li a {
    padding: 4px 10px;
  }

  .location-search-box .search-for-restaurant input.form-control {
    width: 100%;
  }  
  .header-middle-box .location-box ul li{
    max-width: 90px;
  }
  .header-two .header-right ul li a {
    font-size: 13px;
    padding: 4px 15px;
  }

}

@media screen and (max-width:991px) {  
  .navigation-box {
    display: none;
  }

  .header-bg {
    padding: 15px 50px;
  }

  .header-middle-box .location-box {
    margin-right: 0px;
    display: none;
  }

  .header-two .header-right ul li.add-cart-btn {
    padding-left: 0px;
    margin-right: 0;
  }

  .header-two .header-right ul li button.basket-toggle-btn {
    width: 80px;
  }  

}

@media screen and (max-width:767px) {
  .header-bg.header-two {
    padding: 15px 30px;
  }

  .header-two .header-right ul li a {
    padding: 3px 15px;
    font-size: 15px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    width: 80px;
    padding: 3px 5px;
  }

}

@media screen and (max-width:575px) {
  .logo button img {
    width: 100px;
  }

  .header-bg.header-two .header-right ul li {
    margin-left: 8px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    width: 65px;
    font-weight: normal;
    padding: 3px 5px;
  }

  .header-two .header-right ul li.add-cart-btn button img.white-cart-image {
    margin-right: 5px;
  }

}

@media screen and (max-width:480px) {
  .header-bg {
    padding: 10px 20px;
  }

  .logo button img {
    width: 90px;
  }

  button.toggle {
    height: 24px;
    margin-right: 12px;
  }

  button.toggle span {
    top: 10px;
  }

  .header-right ul li a {
    font-size: 12px;
    padding: 2px 10px;
  }

  .header-right ul li.add-cart-btn button {
    font-size: 12px;
    padding: 4px 10px;
  }

  .header-right ul li.add-cart-btn button img {
    width: 16px;
    margin-right: 4px;
    margin-top: -5px;
  }

  .navigation-list {
    margin-bottom: 45px;
  }
  .navigation-list ul li button.dropdown-item,
  .navigation-list ul li a.dropdown-item {
    font-size: 15px;
  }

  .your-restaurant-box p {
    font-size: 15px;
  }

  .header-two button.toggle {
    margin-right: 12px;
  }

  .header-bg.header-two {
    padding-left: 20px;
    padding-right: 20px;
  }

  .header-bg.header-two .header-right ul li {
    margin-left: 5px;
  }

  .header-two .header-right ul li a {
    font-size: 12px;
    padding: 1px 10px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    width: 60px;
    padding: 1px 5px;
  }

  .header-right li.account-dropdown .account-dropdown-content {
    top: 28px;
  }

  .header-two .header-right li.account-dropdown .account-dropdown-content {
    top: 28px;
  }

  .navigation-dropdown button.close {
    padding-top: 16px;
    font-size: 24px;
    margin-bottom: 30px;
  }

  .navigation-list {
    margin-bottom: 50px;
  }

  .navigation-dropdown {
    padding: 10px 20px 20px;
    width: 250px;
  }

  .navigation-list ul li {
    margin-bottom: 15px;
  }
  .header-right ul.account-dropdown-list li button,
  .header-right ul.account-dropdown-list li a {
    font-size: 15px;
    padding: 6px 15px;
  }

  .header-two .header-right ul.account-dropdown-list li a {
    font-size: 15px;
    padding: 6px 15px;
  }

  .empty-cart-cls img {
    width: 50px;
  }

  .empty-cart-cls p {
    font-size: 18px;
  }
  
  .app-buttons ul li a.ios-btn {
    padding-left: 45px;
  }

  .app-buttons ul li a.android-btn {
    padding-left: 35px;
  }

  .app-buttons ul li a img {
    width: 20px;
  }
  .header-two .header-right ul li button.basket-toggle-btn {
    width: 65px;
  }  

}

@media screen and (max-width:400px) {
  button.toggle {
    width: 26px;
    height: 22px;
    top:7px;
    margin-right: 10px;
  }

  button.toggle span {
    width: 26px;
    top: 10px;
    height: 3px;
  }

  button.toggle::after,
  button.toggle::before {
    width: 26px;
    height: 3px;
  }

  .logo button img {
    width: 70px;
  }

  .header-right ul li a {
    padding: 0px 7px 2px 7px;
    font-size: 12px;
  }

  .header-right ul li a.basket-toggle-btn {
    width: 65px;
    text-align: center;
    padding: 1px 5px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    line-height: 24px;
  }

  .header-right ul li.add-cart-btn button span {
    font-size: 13px;
  }

  .header-right ul li.add-cart-btn button img.white-cart-image {
    display: none;
  }

}

@media screen and (max-width:350px) {
  .header-right ul li {
    margin-left: 2px;
  }

  .header-right ul li a {
    padding: 0 6px 2px;
    font-size: 10px;
    line-height: 22px;
  }

  .header-right ul li.add-cart-btn button img {
    width: 14px;
    margin-right: 2px;
    margin-top: -5px;
  }

  .header-right ul li.add-cart-btn button span {
    font-size: 12px;
  }

  .header-right ul li a.basket-toggle-btn {
    font-size: 10px;
    width: 56px;
    padding:0 2px;
  }

  .header-bg.header-two .header-right ul li {
    margin-left: 2px;
  }

  .header-two .header-right ul li a {
    font-size: 11px;
    padding: 1px 5px;
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    line-height: 21px;
    width: 50px;
    padding: 1px 2px;
  }

  .header-two .header-right ul li.add-cart-btn button img.white-cart-image {
    margin-right: 2px;
    margin-top: -5px;
  }

  .header-right li.account-dropdown .account-dropdown-content {
    top: 26px;
  }

  .header-two .header-right li.account-dropdown .account-dropdown-content {
    top: 26px;
  }

}
