
export class CheckoutList {
  id: string; //: 4188

  restaurant_id: string; //: 3
  order_type: string; //both , delivery , pickup
  message: string;
  status: string; //: "1"
  selected: boolean; //: "1"

  created?: string; //: "2007-11-19 17:51:00",


  static toFormData(checkout_list: CheckoutList) {
    const formData = new FormData();

    if (checkout_list.id) formData.append('id', checkout_list.id);
    if (checkout_list.restaurant_id) formData.append('restaurant_id', checkout_list.restaurant_id);
    if (checkout_list.order_type) formData.append('order_type', checkout_list.order_type);
    if (checkout_list.message) formData.append('message', checkout_list.message);
    formData.append('status', checkout_list.status ? '1' : '0');

    return formData;
  }
}
