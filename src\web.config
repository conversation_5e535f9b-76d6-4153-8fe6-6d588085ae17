<?xml version="1.0" encoding="utf-8"?>
<configuration>

<system.webServer>
  <rewrite>
    <rules>
      <rule name="Angular Routes" stopProcessing="true">
        <match url=".*" />
        <conditions logicalGrouping="MatchAll">
          <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
        </conditions>
        <action type="Rewrite" url="/" />
      </rule>
    </rules>
  </rewrite>
        <security>
            <requestFiltering>
                <fileExtensions>
                    <add fileExtension=".svg" allowed="true" />
                    <add fileExtension=".woff" allowed="true" />
                    <add fileExtension=".woff2" allowed="true" />
                </fileExtensions>
            </requestFiltering>
        </security>
  
<httpProtocol>
    <customHeaders>
        <!-- <add name="X-Content-Type-Options" value="no-sniff"/>
        <add name="X-Frame-Options" value="SAMEORIGIN"/>
        <add name="X-Xss-Protection" value="1; mode=block"/>
        <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains"/> -->
    </customHeaders>
  </httpProtocol>
        <httpRedirect enabled="false" destination="" />
</system.webServer>

</configuration>