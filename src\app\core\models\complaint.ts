export class Complaint {
  id: string;

  message: string;
  user_id: string;
  restaurant_id: string;
  order_id: string;

  admin: boolean = false;
  status: boolean = false;
  close: boolean = false;

  created: string;

  static toFormData(complaint: Complaint) {
    const formData = new FormData();

    if (complaint.id) formData.append('id', complaint.id);
    if (complaint.message) formData.append('message', complaint.message);
    if (complaint.user_id) formData.append('user_id', complaint.user_id);
    if (complaint.restaurant_id) formData.append('restaurant_id', complaint.restaurant_id);
    if (complaint.order_id) formData.append('order_id', complaint.order_id);
    formData.append('admin', complaint.admin ? '1' : '0');
    formData.append('status', complaint.status ? '1' : '0');
    // formData.append('close', complaint.close ? '1' : '0');

    return formData;
  }
}