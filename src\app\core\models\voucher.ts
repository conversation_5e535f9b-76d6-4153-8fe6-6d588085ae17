export class Voucher {
  id: string;
  user_id: string;
  resid: string;
  day: string;
  restaurant_id: string;
  voucher_code: string;
  type_offer: string;
  offer_mode: string;
  free_delivery: string;

  offer_value: number;
  minimum_value: number;
  
  customer_id: string;
  order_type: string;

  voucher_from: string;
  voucher_to: string;
  delivery_type: boolean;
  pickup_type: boolean;
  dinein_type: boolean;
  status: boolean;

  static toFormData(voucher: Voucher) {
    const formData = new FormData();

    if (voucher.voucher_code) formData.append('voucher_code', voucher.voucher_code);
    if (voucher.restaurant_id) formData.append('restaurant_id', voucher.restaurant_id);
    if (voucher.customer_id) formData.append('customer_id', voucher.customer_id);
    if (voucher.order_type) formData.append('order_type', voucher.order_type);
    
    return formData;
  }
}
