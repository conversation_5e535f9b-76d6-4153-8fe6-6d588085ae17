::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.checkout-section.track-section {
  display: flex;
}

.checkout-section.track-section .login-box {
  max-width: 955px;
  padding-right: 60px;
}

.track-order-box {
  padding-top: 40px;
  text-align: center;
  max-width: 700px;
  width: 100%;
  float: right;
}

.track-order-heading {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.track-order-heading .product-logo {
  width: 78px;
  height: 78px;
  line-height: 78px;
  text-align: center;
  box-shadow: 0px 0px 10px 2px #0000001A;
  border-radius: 50%;
  margin-right: 30px;
}

.track-order-heading .product-logo img {
  width: 60px;
}

.track-order-heading h5 {
  font-family: 'Visby CF';
  font-size: 30px;
  font-weight: 800;
  margin-bottom: 0;
}

.track-order-box h6 {
  font-family: 'Visby CF';
  font-size: 25px;
  font-weight: 800;
  margin-bottom: 12px;
}

.track-order-box h6.reject-title {
  color: #fc353a;
}

.track-order-box span {
  color: #202020;
  font-size: 16px;
  font-weight: 500;
  display: inline-block;
  padding-bottom: 10px;
}

.track-order-box p {
  font-family: 'Visby CF';
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 25px;
}

.order-status .check-arrow {
  margin-bottom: 70px;
}

.order-status .check-arrow img {
  width: 100px;
}

.order-statusp-list ul {
  display: flex;
  margin-bottom: 0;
  position: relative;
}

.order-statusp-list ul::before {
  position: absolute;
  top: 0;
  width: 100%;
  content: '';
  border-radius: 10px;
  height: 10px;
  background-color: #D9D9D9;
}

.order-statusp-list ul li {
  position: relative;
  padding-top: 35px;
  width: 25%;
}

.order-statusp-list ul li.active p {
  color: #000;
}

.order-statusp-list ul.pickup li {
  width: 33.3%;
}

.order-statusp-list ul li::after {
  position: absolute;
  top: -8px;
  content: '';
  width: 25px;
  height: 25px;
  border-radius: 50%;
  z-index: 12;
  background-color: #D9D9D9;
}

.order-statusp-list ul li.active::after {
  background-color: #14A411;
}

.order-statusp-list ul li::before {
  position: absolute;
  top: 0;
  left: -45%;
  width: 100%;
  content: '';
  border-radius: 10px 0 0 10px;
  height: 10px;
  background-color: #D9D9D9;
}

.order-statusp-list ul li.active::before {
  background-color: #14A411;
}

.order-statusp-list ul li:first-child::before {
  width: 51%;
  left: 0;
}

.order-statusp-list ul li:last-child::before {
  width: 150%;
  border-radius: 10px;
}

.rejected-status .order-statusp-list ul li {
  width: 100%;
}

.rejected-status .order-statusp-list ul li.active::after,
.rejected-status .order-statusp-list ul li.active:before {
  background-color: #FC353A;
}

.order-statusp-list ul li p {
  font-family: 'Visby CF';
  font-size: 20px;
  color: #D9D9D9;
  font-weight: 700;
  margin-bottom: 0;
}

.add-to-cart-box-two .add-cart-box .cart-middle {
  max-height: calc(100vh - 130px);
}

.reservation-id {
  text-align: center;
  margin-bottom: 40px;
}

.reservation-id p {
  font-size: 20px;
  color: #8f8f8a;
  font-weight: bold;
  margin-bottom: 0;
}

.reservation-id p strong {
  color: #000;
}

.order-details .table-responsive {
  width: 100%;
  margin-top: 30px;
}

.order-details .table-responsive table {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.order-details .table-responsive table tr td {
  font-size: 18px;
  color: #000;
  font-weight: 700;
  width: 50%;
  text-align: left;
  border: 0;
  padding: 7px 10px;
}

.order-details .table-responsive table tr td:first-child {
  color: #8F8F8A;
  white-space: nowrap;
}

.preparation-timer {
  width: 100%;
  margin-bottom: 40px;
}

.preparation-timer ul {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}

.preparation-timer ul li {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  margin: 0 15px;
}

.preparation-timer ul li h2 {
  font-size: 34px;
  line-height: normal;
  margin: 0;
  color: #fc353a;
}

.preparation-timer ul li span {
  font-size: 16px;
  color: #202020;
  font-weight: 600;
  padding-bottom: 0;
}

.subtotal-box ul.add-promo-code li.total-amount {
  padding: 5px 0 6px 0;
  margin-top: 5px;
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
}

.subtotal-box ul.add-promo-code li.total-amount span {
  font-weight: 800;
  padding: 0;
}
.earn-points {
  padding: 2px 12px;
}
.earn-points p {
  font-family: 'Visby CF';
  font-size: 13px;
  color: #fc353a;
  font-weight: 700;
  margin-bottom: 0;
}
.earn-points svg {
  font-size: 14px;
  margin-right: 2px;
  position: relative;
  top: 1px;
}

@media screen and (max-width:1500px) {
  .checkout-section.track-section .login-box {
    padding-right: 45px;
    max-width: 600px;
  }

  .track-order-box {
    padding-top: 30px;
    max-width: 525px;
  }

  .track-order-heading {
    margin-bottom: 34px;
  }

  .track-order-heading .product-logo {
    width: 58px;
    height: 58px;
    line-height: 58px;
  }

  .track-order-heading .product-logo img {
    width: 40px;
  }

  .track-order-heading h5 {
    font-size: 22px;
  }

  .track-order-box h6 {
    font-size: 18px;
    line-height: 20px;
  }

  .track-order-box span {
    font-size: 13px;
    font-weight: 600;
  }

  .track-order-box p {
    font-size: 16px;
  }

  .order-status .check-arrow {
    margin-bottom: 52px;
  }

  .order-status .check-arrow img {
    width: 75px;
  }

  .order-statusp-list ul:before {
    height: 7px;
  }

  .order-statusp-list ul li:before {
    height: 7px;
  }

  .order-statusp-list ul li:after {
    top: -6px;
    width: 18px;
    height: 18px;
  }

  .order-statusp-list ul li:first-child:before {
    width: 52%;
  }

  .order-statusp-list ul li {
    padding-top: 27px;
  }

  .order-statusp-list ul li p {
    font-size: 13px;
  }

  .reservation-id {
    margin-bottom: 30px;
  }

  .reservation-id p {
    font-size: 15px;
  }

  .order-details .table-responsive {
    margin-top: 20px;
  }

  .order-details .table-responsive table {
    max-width: 300px;
  }

  .order-details .table-responsive table tr td {
    font-size: 15px;
  }

  .preparation-timer {
    margin-bottom: 30px;
  }

  .preparation-timer ul li {
    width: 80px;
    height: 80px;
    margin: 0 10px;
  }

  .preparation-timer ul li h2 {
    font-size: 24px;
    color: #fc353a;
  }

  .preparation-timer ul li span {
    font-size: 12px;
  }

  .subtotal-box ul.add-promo-code li.total-amount {
    margin-top: 0;
  }
  .earn-points p{
    font-size:11px;
  }
  .earn-points p svg{
    font-size: 12px;
  }

}

@media screen and (max-width:1199px) {
  .checkout-section.track-section {
    flex-wrap: wrap;
  }

  .track-order-box {
    float: none;
    margin: auto;
    margin-bottom: 30px;
  }

  .checkout-section.track-section .login-box {
    max-width: 100%;
    padding: 0;
  }

}

@media screen and (max-width:767px) {
  .track-order-box {
    padding-top: 30px;
  }

  .track-order-heading {
    margin-bottom: 30px;
  }

  .order-status .check-arrow {
    margin-bottom: 50px;
  }


}

@media screen and (max-width:575px) {
  .track-order-heading .product-logo {
    margin-right: 20px;
  }

  .track-section .add-to-cart-box-two .add-cart-box {
    height: auto;
  }

  .add-to-cart-box-two .add-cart-box .cart-middle {
    max-height: initial;
  }

  .add-to-cart-box-two .subtotal-price-box {
    border-bottom: 0;
  }

  .track-order-heading {
    margin-bottom: 25px;
  }

  .order-status .check-arrow {
    margin-bottom: 40px;
  }

}

@media screen and (max-width:480px) {
  .track-order-box {
    padding-top: 25px;
    margin-bottom: 30px;
  }

  .track-order-box h6 {
    font-size: 16px;
    line-height: 20px;
  }

  .track-order-heading .product-logo {
    width: 55px;
    height: 55px;
    line-height: 50px;
    margin-right: 15px;
  }

  .track-order-heading .product-logo img {
    width: 40px;
  }

  .order-status .check-arrow {
    margin-bottom: 30px;
  }

  .track-order-heading {
    margin-bottom: 15px;
  }

  .track-order-box p {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .order-status .check-arrow img {
    width: 60px;
  }

  .order-statusp-list ul li::before {
    left: -48%;
  }

  .order-statusp-list ul li:after {
    top: -5px;
    width: 16px;
    height: 16px;
  }

  .order-statusp-list ul li:first-child::before {
    width: 52%;
  }

  .order-statusp-list ul li p {
    font-size: 11px;
    line-height: 16px;
    text-align: center;
  }

  .reservation-id {
    margin-bottom: 20px;
  }

  .preparation-timer {
    margin-bottom: 20px;
  }

  .preparation-timer ul li {
    width: 60px;
    height: 60px;
    border: 1.5px dashed #d9d9d9;
    margin: 0 5px;
  }

  .preparation-timer ul li h2 {
    font-size: 20px;
  }

  .preparation-timer ul li span {
    font-size: 10px;
  }

}
