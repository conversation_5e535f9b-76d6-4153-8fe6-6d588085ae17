import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { <PERSON>rror<PERSON>andler } from '../../shared/error-handler';

@Injectable({
  providedIn: 'root',
})
export class CategoryService {
  public url = environment.apiBaseUrl + 'categories/';
  constructor(private http: HttpClient) { }
  get(options: any = {}): Observable<any> {
    let params = new HttpParams();
    if (options.prefilled) params = params.set('prefilled', '1');
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', '1');
    if (options.isSuggested) params = params.set('is_suggested', '1');
    if (options.order_type) params = params.set('order_type', options.order_type);
    return this.http
      .get<any>(this.url, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }
}
