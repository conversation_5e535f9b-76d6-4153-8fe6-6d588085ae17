import { Component, ElementRef, HostListener, Inject, PLATFORM_ID, ViewChild } from '@angular/core';
import { CanonicalService } from './shared/canonical.service';
import { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router, RouterEvent } from '@angular/router';
import { SiteSettingService } from './core/services/site-setting.service';
import { UserService } from './core/services/user.service';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { SiteSetting } from './core/models/site-setting';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NotificationService } from './core/services/notification.service';
import { User } from './core/models/user';
import { NgForm } from '@angular/forms';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-root',
  host: { ngSkipHydration: 'true' },
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  subs = new Subscription();

  @ViewChild('otpModal') otpModal: ElementRef;
  @ViewChild('profileModal') profileModal: ElementRef;

  title = 'gogrubz-frontend';
  loading = true
  user: User;
  loginuser: User;
  siteSetting: SiteSetting = new SiteSetting();

  phoneModelReferance: any;

  isModelOtpLoading = false; Modelotperror = null;
  isModelProfileLoading = false; ModelProfileerror = false;

  constructor(
    private userService: UserService,
    private siteSettingService: SiteSettingService,
    private notificationService: NotificationService,
    public modalService: NgbModal,
    private canonicalService: CanonicalService,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: Object,
    private metaTagService: Meta,
    private titleService: Title,
  ) {
    router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        setTimeout(() => {
          this.document.documentElement.scrollTop = 0; // || window.scrollTo(0, 0);
        }, 190);
        this.fetchSiteSetting();
      }
    });

    // this.router.events.subscribe((e: RouterEvent) => {
    //   this.navigationInterceptor(e);
    // })

  }

  ngOnInit() {
    this.canonicalService.setCanonicalURL();
    if (isPlatformBrowser(this.platformId)) {
      this.loading = false;
    }
  }

  navigationInterceptor(event: RouterEvent): void {
    // if (event instanceof NavigationStart) {
    //   this.loading = true
    // }
    // if (event instanceof NavigationEnd) {
    //   setTimeout(() => {
    //     this.loading = false;
    //   }, 2000);
    // }

    // // Set loading state to false in both of the below events to hide the spinner in case a request fails
    // if (event instanceof NavigationCancel) {
    //   setTimeout(() => {
    //     this.loading = false;
    //   }, 2000);
    // }
    // if (event instanceof NavigationError) {
    //   setTimeout(() => {
    //     this.loading = false;
    //   }, 2000);
    // }
  }

  fetchSiteSetting() {
    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.titleService.setTitle(this.siteSetting.gogrubz_meta_title);
        this.metaTagService.updateTag({ name: 'keywords', content: this.siteSetting.gogrubz_meta_keywords });
        this.metaTagService.updateTag({ name: 'description', content: this.siteSetting.gogrubz_meta_description });
        this.metaTagService.updateTag({ property: 'og:title', content: this.siteSetting.gogrubz_meta_title });
        this.metaTagService.updateTag({ property: 'og:description', content: this.siteSetting.gogrubz_meta_description });
        this.metaTagService.updateTag({ property: 'robots', content: 'index, follow' });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        this.user = JSON.parse(this.userService.getUser());
        if (this.user?.id) {
          if ((!this.user.phone_verify && this.siteSetting.signup_verify_type == 'phone') || (this.siteSetting.signup_verify_type == 'both' && !this.user.phone_verify) && this.siteSetting?.signup_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', backdrop: 'static' });
          }
        }
      }, err => { })
    );
  }

  otpSend() {
    this.isModelOtpLoading = false; this.Modelotperror = false;

    // if (this.siteSetting.signup_verify_type == 'both') {
    //   if (!this.user?.phone_verify && !this.user?.email_verify) {
    //     this.user.verify_type = 'both';
    //   } else {
    //     if (!this.user?.phone_verify && this.user?.email_verify) {
    //       this.user.verify_type = 'phone';
    //     }
    //     if (this.user?.phone_verify && !this.user?.email_verify) {
    //       this.user.verify_type = 'email';
    //     }
    //   }
    // } else {
    //   if (this.siteSetting.signup_verify_type == 'mail') {
    //     this.user.verify_type = 'email';
    //   } else {
    //     this.user.verify_type = this.siteSetting.signup_verify_type;
    //   }
    // }
    this.user.verify_type = 'phone';

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz");
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.isModelOtpLoading = false; this.Modelotperror = false;

    // if (this.siteSetting.signup_verify_type == 'both') {
    //   if (!this.user?.phone_verify && !this.user?.email_verify) {
    //     this.user.verify_type = 'both';
    //   } else {
    //     if (!this.user?.phone_verify && this.user?.email_verify) {
    //       this.user.verify_type = 'phone';
    //     }
    //     if (this.user?.phone_verify && !this.user?.email_verify) {
    //       this.user.verify_type = 'email';
    //     }
    //   }
    // } else {
    //   if (this.siteSetting.signup_verify_type == 'mail') {
    //     this.user.verify_type = 'email';
    //   } else {
    //     this.user.verify_type = this.siteSetting.signup_verify_type;
    //   }
    // }
    this.user.verify_type = 'phone';

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("otp sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.validateOtp(form)
  }

  validateOtp(form: NgForm) {
    this.isModelOtpLoading = true; this.Modelotperror = false;

    // if (this.siteSetting.signup_verify_type == 'both') {
    //   if (!this.user?.phone_verify && !this.user?.email_verify) {
    //     this.user.verify_type = 'both';
    //   } else {
    //     if (!this.user?.phone_verify && this.user?.email_verify) {
    //       this.user.verify_type = 'phone';
    //     }
    //     if (this.user?.phone_verify && !this.user?.email_verify) {
    //       this.user.verify_type = 'email';
    //     }
    //   }
    // } else {
    //   if (this.siteSetting.signup_verify_type == 'mail') {
    //     this.user.verify_type = 'email';
    //   } else {
    //     this.user.verify_type = this.siteSetting.signup_verify_type;
    //   }
    // }
    this.user.verify_type = 'phone';

    this.subs.add(
      this.userService.varifyBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.user = res;
            this.userService.saveUser(res);
            this.fetchMe();
            this.notificationService.showSuccess("otp verify successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
        this.modalService.dismissAll();
        if ((!this.user.phone_verify && this.siteSetting.signup_verify_type == 'phone') || (this.siteSetting.signup_verify_type == 'both' && !this.user.phone_verify) && this.siteSetting?.signup_verify == '1') {
          this.otpSend();
          this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', backdrop: 'static' });
        }
      }, err => this.Modelotperror = err)
    );
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  openPhoneEdit() {
    this.modalService.dismissAll();
    this.loginuser = Object.assign({}, this.user);
    this.phoneModelReferance = this.modalService.open(this.profileModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
  }

  updateUser(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isModelProfileLoading = true; this.ModelProfileerror = null;

    if (this.userService.user.phone_number != this.loginuser.phone_number) {
      this.loginuser.phone_verify = false;
      this.userService
        .disabledPhoneVerify(this.loginuser)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => {
            this.userService.saveUser(res);
            this.user = res;
          },
          (err) => { }
        );
    }

    this.userService
      .update(this.loginuser)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.notificationService.showSuccess("Phone number updated successfully!!", "Gogrubz")
          this.modalService.dismissAll();
          if ((((!this.user.phone_verify && this.siteSetting.order_verify_type == 'phone') || !this.user.email_verify && this.siteSetting.order_verify_type == 'mail') || (this.siteSetting.order_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.siteSetting?.order_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', backdrop: 'static' });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }
}
