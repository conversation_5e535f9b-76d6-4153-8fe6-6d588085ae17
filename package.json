{"name": "new-grog<PERSON>bz", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:newGrogrubz": "node dist/new-grogrubz/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^17.2.0", "@angular/common": "^17.2.0", "@angular/compiler": "^17.2.0", "@angular/core": "^17.2.0", "@angular/forms": "^17.2.0", "@angular/google-maps": "^17.3.1", "@angular/platform-browser": "^17.2.0", "@angular/platform-browser-dynamic": "^17.2.0", "@angular/platform-server": "^17.2.0", "@angular/router": "^17.2.0", "@angular/ssr": "^17.2.3", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.3", "crypto-js": "^4.2.0", "express": "^4.18.2", "jquery": "^3.7.1", "lottie-web": "^5.12.2", "ng-circle-progress": "^1.7.1", "ng-zorro-antd": "^17.3.0", "ngx-lottie": "^11.0.2", "ngx-paypal": "^11.0.0", "ngx-slick-carousel": "^17.0.0", "ngx-tabset": "^2.2.0", "ngx-toastr": "^18.0.0", "rxjs": "~7.8.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.2.3", "@angular/cli": "^17.2.3", "@angular/compiler-cli": "^17.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.3.2"}}