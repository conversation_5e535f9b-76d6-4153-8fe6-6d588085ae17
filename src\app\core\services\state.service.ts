import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ErrorHandler } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { State } from '../models/state';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class StateService {
  private url = `${environment.apiBaseUrl}states/`

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.country_id) params = params.set('country_id', options.country_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<State> {
    return this.http.get<State>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(state: State): Observable<State> {
    return this.http.post<State>(this.url, State.toFormData(state))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(state: State): Observable<State> {
    return this.http.post<State>(this.url + state.id, State.toFormData(state))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<State> {
    return this.http.delete<State>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  enable(id: string): Observable<State> {
    return this.http.post<State>(this.url + id + '/enable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  disable(id: string): Observable<State> {
    return this.http.post<State>(this.url + id + '/disable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
