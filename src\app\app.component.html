<div>
  <div class="main-loader-bg" *ngIf="loading">
    <div class="loading">
      <div class="dot">
        <!--<img class="grubz-icon" src="assets/images/grubz-icon.svg" alt="grubz-icon">-->
        G
      </div>
      <div class="dot">O</div>
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot">G</div>
      <div class="dot">r</div>
      <div class="dot">u</div>
      <div class="dot">b</div>
      <div class="dot">z</div>
      <span class="wait-text">Please Wait...</span>
    </div>
  </div>
  <router-outlet></router-outlet>
</div>

<!-- Phone-Number-Verification-Popup -->
<ng-template #otpModal let-modal>
  <div id="number-verification-popup">
    <!-- <button type="button" class="btn-close" data-bs-dismiss="modal"
      (click)="modal.dismiss('Cross click');counter=20;mySubscription.unsubscribe()">
      <svg class="fa-solid fa-xmark"></svg>
    </button> -->
    <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
      <div class="modal-body">
        <!-- <img class="verification-icon" src="assets/images/verification.png" alt="verification"> -->
        <div class="d-flex justify-content-center">
          <!-- <h6 *ngIf="user.verify_type == 'phone' || user.verify_type == 'both'">Phone Number</h6>
          <h6 class="px-2" *ngIf="user.verify_type == 'both'"> & </h6>
          <h6 *ngIf="user.verify_type == 'email' || user.verify_type == 'both'">Email</h6> -->
          <h6 class="ps-2">
            Phone Number Verification
          </h6>
        </div>
        <div class="verify-text">After sign up, we need to verify your Phone Number for security purposes
          <!-- <div class="d-flex justify-content-center">
            <span *ngIf="user.verify_type == 'phone' || user.verify_type == 'both'">Phone Number</span>
            <span class="px-2" *ngIf="user.verify_type == 'both'"> & </span>
            <span *ngIf="user.verify_type == 'email' || user.verify_type == 'both'">Email</span>
          </div>
          for security purposes -->
        </div>
        <div class="form-group">
          <nz-form-item>
            <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
              <nz-input-group>
                <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                  (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                  (keypress)="validateMobile($event)" [(ngModel)]="user.otp" required
                  placeholder="Enter 6-digit phone verification code">
              </nz-input-group>
              <ng-template #phoneVeriErrorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">
                  Please enter phone verification code!
                </ng-container>
                <!-- <ng-container *ngIf="control.hasError('minlength')">
                  Phone verification code should be 6 digit!
                </ng-container>
                <ng-container *ngIf="control.hasError('maxlength')">
                  Phone verification code should be maximum 6 digits long!
                </ng-container> -->
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- <div class="form-group" *ngIf="user.verify_type == 'email' || user.verify_type == 'both'">
          <nz-form-item>
            <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
              <nz-input-group>
                <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                  (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="email_otp" name="email_otp"
                  (keypress)="validateMobile($event)" [(ngModel)]="user.email_otp" required
                  placeholder="Enter 6-digit email verification code">
              </nz-input-group>
              <ng-template #phoneVeriErrorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">
                  Please enter email verification code!
                </ng-container>
                <ng-container *ngIf="control.hasError('minlength')">
                  statement email verification code at least 6 digit!
                </ng-container>
                <ng-container *ngIf="control.hasError('maxlength')">
                  statement email verification code maximum 6 digits long!
                </ng-container>
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div> -->

        <div class="text-center">
          <span class="d-inline-block w-100 pb-1">We sent a code to {{ user.phone_number }}.</span>
          <!-- <span class="d-flex justify-content-center">
            <span class="ps-2" *ngIf="user.verify_type == 'phone' || user.verify_type == 'both'">{{ user.phone_number
              }}</span>
            <span class="px-2" *ngIf="user.verify_type == 'both'"> & </span>
            <span *ngIf="user.verify_type == 'email' || user.verify_type == 'both'"> {{ user.username }}</span>
          </span> -->
        </div>

        <ul class="more-option">
          <li><a class="d-flex text-nowrap cursor" (click)="resendOtp()"> Resend Code</a></li>
          <li><svg class="fa-solid fa-circle"></svg></li>
          <li><a class="cursor" (click)="openPhoneEdit()"> Update Number</a></li>
        </ul>

      </div>
      <div class="modal-footer">
        <nz-form-item *ngIf="Modelotperror">
          <span class="text-danger">{{ Modelotperror }}</span>
        </nz-form-item>

        <button class="btn modal-black-btn" nz-button [disabled]="isModelOtpLoading">
          <i class="spinner-border" *ngIf="isModelOtpLoading"></i>
          Verify
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- Edit-Phone-Number-Popup -->
<ng-template #profileModal let-modal>
  <div id="edit-number-popup">
    <!-- <button type="button" class="btn-close" data-bs-dismiss="modal">
      <svg class="fa-solid fa-xmark"></svg>
    </button> -->
    <form nz-form #userForm="ngForm" (ngSubmit)="updateUser(userForm)">
      <div class="modal-body">
        <img class="verification-icon" src="assets/images/verification.png" alt="GoGrubz-verification">
        <h6 class="mb-4">Edit Phone Number</h6>
        <div class="form-group">
          <nz-form-item>
            <nz-form-control nzHasFeedback [nzErrorTip]="phoneErrorTpl">
              <nz-input-group>
                <input type="text" inputmode="numeric" class="form-control" nz-input
                  (keydown.space)="onSpaceKeyDown($event)" (keypress)="validateMobile($event)" id="phone_number"
                  name="phone_number" [(ngModel)]="loginuser.phone_number" required placeholder="Enter phone number">
              </nz-input-group>
              <ng-template #phoneErrorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">
                  Please enter mobile number!
                </ng-container>
                <ng-container *ngIf="control.hasError('minlength')">
                  statement mobile number at least 10 digit!
                </ng-container>
                <ng-container *ngIf="control.hasError('maxlength')">
                  statement mobile number maximum 11 digits long!
                </ng-container>
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      <div class="modal-footer">

        <nz-form-item *ngIf="ModelProfileerror">
          <span class="text-danger">{{ ModelProfileerror }}</span>
        </nz-form-item>

        <button class="btn modal-black-btn" nz-button>
          <i class="spinner-border" *ngIf="isModelProfileLoading"></i>
          Save
        </button>
      </div>
    </form>
  </div>
</ng-template>