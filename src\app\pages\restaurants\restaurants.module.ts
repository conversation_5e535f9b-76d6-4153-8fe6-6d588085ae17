import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RestaurantsComponent } from './restaurants.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { SharedModule } from '../../shared/shared.module';

const routes: Routes = [
  { path: '', component: RestaurantsComponent },
];
@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    SlickCarouselModule,
    SharedModule
  ],
  declarations: [RestaurantsComponent],
  exports: [RestaurantsComponent]
})
export class RestaurantModule { }
