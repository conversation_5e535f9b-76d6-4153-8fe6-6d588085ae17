import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from '../../shared/error-handler';
import { Contact } from '../models/contact';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ContactService {
  private url = environment.apiBaseUrl + 'contacts/';
  public carts: Contact[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Contact> {
    return this.http.get<Contact>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(contact: Contact): Observable<any> {
    return this.http.post<Contact>(this.url, Contact.toFormData(contact))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(contact: Contact): Observable<any> {
    return this.http.post<Contact>(this.url + contact.id, Contact.toFormData(contact))
      .pipe(catchError(ErrorHandler.handleError));
  }

  verify(contact: Contact): Observable<any> {
    return this.http.post<Contact>(this.url + contact.id + '/verify', Contact.toFormData(contact))
      .pipe(catchError(ErrorHandler.handleError));
  }

  resend(contact: Contact): Observable<any> {
    return this.http.post<Contact>(this.url + contact.id + '/resend', Contact.toFormData(contact))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Contact>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }
}
