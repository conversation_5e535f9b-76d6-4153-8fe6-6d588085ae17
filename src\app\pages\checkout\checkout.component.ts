import { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { CurrencyPipe, DOCUMENT, formatDate } from '@angular/common';
import { finalize } from 'rxjs/operators';
import { Subscription, interval } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { IPayPalConfig, ICreateOrderRequest } from 'ngx-paypal';
declare var Stripe;
import { FormGroupDirective, NgForm } from '@angular/forms';
import { User } from '../../core/models/user';
import { Restaurant } from '../../core/models/restaurant';
import { Cart } from '../../core/models/cart';
import { Surcharge } from '../../core/models/surcharge';
import { StripeCustomer } from '../../core/models/stripe-customer';
import { Order } from '../../core/models/order';
import { Voucher } from '../../core/models/voucher';
import { UserService } from '../../core/services/user.service';
import { RestaurantService } from '../../core/services/restaurant.service';
import { CartService } from '../../core/services/cart.service';
import { OrderService } from '../../core/services/order.service';
import { StripeCustomerService } from '../../core/services/stripe-customer.service';
import { VoucherService } from '../../core/services/voucher.service';
import { Meta, Title } from '@angular/platform-browser';
import { NotificationService } from '../../core/services/notification.service';
import { ModalDismissReasons, NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
// import { FacebookLoginProvider, GoogleLoginProvider, SocialAuthService } from 'angularx-social-login';
import { AddressBook } from '../../core/models/address-book';
import { AddressBookService } from '../../core/services/address-book.service';
import { AnimationOptions } from 'ngx-lottie';
import { SiteSetting } from '../../core/models/site-setting';
import { SiteSettingService } from '../../core/services/site-setting.service';
import { CategoryService } from '../../core/services/category.service';
import { Menu } from '../../core/models/menu';
import { Variant } from '../../core/models/variant';
import { MenuService } from '../../core/services/menu.service';
import { CheckoutList } from '../../core/models/checkout-list';
import { CheckoutListService } from '../../core/services/checkout-list.service';
import { environment } from '../../../environments/environment';
import { RestaurantPaymentMethod } from '../../core/models/restaurant-payment';

@Component({
  selector: 'app-checkout',
  host: { ngSkipHydration: 'true' },
  templateUrl: './checkout.component.html',
  styleUrls: ['./checkout.component.scss']
})

export class CheckoutComponent implements OnInit {
  @ViewChild('addonModal', { static: true }) addonModal: ElementRef;
  @ViewChild('timeSlotModal', { static: true }) timeSlotModal: ElementRef;
  @ViewChild('selectAddressModal', { static: true }) selectAddressModal: ElementRef;
  @ViewChild('addAddressModal', { static: true }) addAddressModal: ElementRef;
  @ViewChild('multiplePriceOfferModal', { static: true }) multiplePriceOfferModal: ElementRef;
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;
  @ViewChild('profileModal', { static: true }) profileModal: ElementRef;
  @ViewChild('placeModal', { static: true }) placeModal: ElementRef;
  @ViewChild('itemModal', { static: true }) itemModal: ElementRef;
  @ViewChild('cardInfo') cardInfo: FormGroupDirective;
  @ViewChild('itemNotAvailableModal') itemNotAvailableModal: FormGroupDirective;

  private subs = new Subscription();
  mySubscription: Subscription

  user: User;
  loginuser: User = new User();
  signupuser: User = new User();
  forgotUser: User = new User();
  restaurant: Restaurant = new Restaurant();
  siteSetting: SiteSetting = new SiteSetting();
  carts: Cart[] = [];
  surcharges: Surcharge[] = [];
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  order: Order = new Order();
  voucher: Voucher = new Voucher();
  assignVoucher: Voucher = new Voucher();
  addStripeCustomer: StripeCustomer;
  addressBooks: AddressBook[] = [];
  notDeliveryaddressBooks: AddressBook[] = [];
  addressBookAdd: AddressBook = new AddressBook();
  menuSuggested: Menu[] = [];
  menuSuggestedOriginal: Menu[] = [];
  timeSlots = [];
  checkoutLists: CheckoutList[] = [];
  selectedAddonDummy: NewAddon[] = [];
  stripePaymentMethod: RestaurantPaymentMethod = new RestaurantPaymentMethod();

  modalOptions: NgbModalOptions;
  AddressModelReferance: any;
  phoneModelReferance: any;

  payPalConfig?: IPayPalConfig;
  addonVisible: boolean = true;
  subAddonVisible: boolean = false;
  editAddonVisible: boolean = false;
  eligibleQty: number;
  surchargeAmount: number = 0;
  verifyOtp: string;
  phoneTab = false;

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;
  payElements;

  selected_checkbox_array = [];
  selectDates = [];

  paypalClientKey: string
  publishKey: string;
  isConnect: string;
  deliveryAddressId: string;
  emailOtp: string = '';

  selectedMenu: Menu;
  selectedDummyMenu: Menu;
  selectedVariant: Variant = new Variant();
  selectedDummyVariant: Variant = new Variant();
  addVariant: Variant;
  selectedQnt: number = 1;
  selectedSubAddonstring: string;
  selectedSubAddonPrice: number = 0;
  selectedAddonPrice: number = 0;
  modalReference: any;
  dummyZipcode: string;

  hasOrderTypeMismatch = false;
  hasDayMismatch = false;

  isLoading = false; error = null;
  isSignupLoading = false; errorSignup = null;
  isLoginLoading = false; errorLogin = null;
  isModelLoading = false; Modelerror = null;
  isOfferModelLoading = false; OfferModelerror = null;
  isModelOtpLoading = false; Modelotperror = null;
  isTimeSlotLoading = false; errorTimeSlot = null;
  isCheckoutListLoading = false; errorCheckoutList = null;
  isSelectAddressLoading = false; errorSelectAddress = null;
  isAddAddressLoading = false; errorAddAddress = null;
  isPostcodeLoading = false; errorPostcode = null;
  isModelProfileLoading = false; ModelProfileerror = false;
  isForgotModelLoading = false; ForgotModelerror = null;
  isOtpLoading = false; Otperror = null;
  isChangePasswordLoading = false; errorChangePassword = null;
  isAddonModelLoading = false;

  otpVisible = false;
  isCheckoutLoading = false; errorCheckout = null;
  isOfferLoading = false;
  errorPaymentMethod = null;
  errorPaypal = null;
  isEligible = false;
  errorVoucher = null;

  isUpdateCart = false;
  isSignin = true;
  isForgot = false;
  isNewPass = false;
  counter: number = 20;

  hide: boolean = true;
  chide: boolean = true;
  lhide: boolean = true;

  optionPlace: AnimationOptions = {
    path: './assets/Animations/OrderPlaced.json',
  };

  options = { query: null, page: 1, per_page: 20, };

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    private siteSettingService: SiteSettingService,
    private categoryService: CategoryService,
    private menuService: MenuService,
    public cartService: CartService,
    private orderService: OrderService,
    private stripeCustomerService: StripeCustomerService,
    private checkoutListService: CheckoutListService,
    private voucherService: VoucherService,
    private addressBookService: AddressBookService,
    private modalService: NgbModal,
    // private socialAuthService: SocialAuthService,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    // private currencyPipe: CurrencyPipe,
    private route: ActivatedRoute,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit() {
    this.fetchSiteSetting();
    this.user = JSON.parse(this.userService.getUser());
    this.order.customer_id = this.user?.id;
    this.order.order_type = this.cartService.getOrderType();
    this.fetchCarts();
    if (this.carts.length > 0) {
      this.restaurant.id = this.carts[0].restaurant_id;
    } else {
      if (typeof localStorage !== 'undefined') {
        this.restaurant.id = localStorage.getItem(environment.googleFirebase);
      }
    }

    if (this.restaurant.id) {
      this.fetchRestaurant();
    } else {
      this.router.navigateByUrl('/location/' + this.restaurantService.zipcode);
    }

    let addressBook = this.userService.getAddress();
    if (this.order.order_type == 'delivery' && addressBook) {
      this.deliveryAddressId = addressBook.id;
      this.order.address_id = addressBook.id;
      this.order.address = addressBook.flat_no + ',' + addressBook.address;
      if (addressBook.deliveryCharge > 0) {
        this.order.delivery_charge = addressBook.deliveryCharge.toFixed(2);
      } else {
        this.order.delivery_charge = 0.00;
      }
      this.order.flat_no = addressBook.flat_no;
      this.order.destination_latitude = addressBook.latitude;
      this.order.destination_longitude = addressBook.longitude;
    }


    this.order.delivery_date = this.convertToDate(new Date());
    const names = ['Sun', 'Mon', 'Tue', 'Wed', 'Thurs', 'Fri', 'Sat'];
    for (let i = 0; i < 4; i++) {
      this.selectDates.push({
        'date': new Date(new Date().setDate(new Date().getDate() + i)).getUTCDate(),
        'day': names[new Date(new Date().setDate(new Date().getDate() + i)).getDay()],
        'fullDate': this.convertToDate(new Date(new Date().setDate(new Date().getDate() + i))),
      });
    }

    if (this.user?.id) {
      this.order.customer_id = this.user?.id;
      this.order.customer_name = this.user?.first_name + ' ' + this.user?.last_name;
      this.order.customer_email = this.user?.username;
      this.order.customer_phone = this.user?.phone_number;
      this.offerCheck();
    }
  }

  fetchSiteSetting() {
    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
      }, err => this.error = err)
    );
  }

  hidePassword() {
    this.hide = !this.hide;
  }

  hideCpassword() {
    this.chide = !this.chide;
  }

  hideLpassword() {
    this.lhide = !this.lhide;
  }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.signup(form)
  }

  signup(form: NgForm) {
    this.isSignupLoading = true; this.errorSignup = null;

    this.userService.signup(this.signupuser)
      .pipe(finalize(() => (this.isSignupLoading = false)))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          this.user = res;
          this.order.customer_id = this.user?.id;
          this.order.customer_name = this.user?.first_name + ' ' + this.user?.last_name;
          this.order.customer_email = this.user?.username;
          this.order.customer_phone = this.user?.phone_number;
          this.fetchCards();
          this.fetchAddressBook();
          this.offerCheck();
        },
        (err) => {
          this.errorSignup = err;
        }
      );
  }

  loginSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.login(form)
  }

  login(form: NgForm) {
    this.isLoginLoading = true; this.errorLogin = null;

    this.userService.login(this.loginuser)
      .pipe(finalize(() => (this.isLoginLoading = false)))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          this.user = res;
          this.order.customer_id = this.user?.id;
          this.order.customer_name = this.user?.first_name + ' ' + this.user?.last_name;
          this.order.customer_email = this.user?.username;
          this.order.customer_phone = this.user?.phone_number;
          this.fetchAddressBook();
          this.offerCheck();
          this.fetchCards();
        },
        (err) => {
          this.errorLogin = err;
        }
      );
  }

  openSignin() {
    this.isSignin = true;
    this.isForgot = false;
    this.isNewPass = false;
  }

  onSubmitForgot(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.forgotPassword(form);
  }

  forgotPassword(form: NgForm) {
    this.isForgotModelLoading = true; this.ForgotModelerror = null;

    this.subs.add(
      this.userService.forgotpassword(this.forgotUser).
        pipe(finalize(() => this.isForgotModelLoading = false))
        .subscribe(
          (res) => {
            this.forgotUser = res;
            this.otpVisible = true;
          },
          (err) => {
            this.ForgotModelerror = err;
          }
        )
    );
  }

  onNewSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isOtpLoading = true; this.Otperror = null;
    this.forgotUser.verify_type = 'email';
    this.forgotUser.otp = this.forgotUser.email_otp;

    this.subs.add(
      this.userService.varifyBothOtp(this.forgotUser).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.otpVisible = false;
            this.isForgot = false;
            this.isNewPass = true;
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  handleInput(event: any, index: number = 0) {
    const digit = event.target.value.replace(/\D/g, '');
    this.emailOtp = this.emailOtp.substring(0, index) + digit + this.emailOtp.substring(index + 1);
    this.forgotUser.email_otp = this.emailOtp;

    if (index < 5 && digit.length === 1) {
      this.document.getElementById(`otp-input-${index + 1}`)?.focus();
    }
  }

  onChangePasswordSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.changePassword(form)
  }

  changePassword(form: NgForm) {
    this.isChangePasswordLoading = true; this.errorChangePassword = null;

    this.userService.changepassword(this.forgotUser)
      .pipe(finalize(() => (this.isChangePasswordLoading = false)))
      .subscribe(
        (res) => {
          this.notificationService.showSuccess("Password changed successfully.", "Gogrubz")
          this.forgotUser = new User();
          this.isForgot = false;
          this.isNewPass = false;
          this.isSignin = true;
        },
        (err) => {
          this.errorChangePassword = err;
        }
      );
  }

  loginWithFacebook(): void {
    // this.socialAuthService.signIn(FacebookLoginProvider.PROVIDER_ID);
  }

  loginWithGoogle(): void {
    // this.socialAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);
  }

  fetchCarts() {
    this.carts = this.cartService.getCart();

    if (this.carts.length <= 0) {
      this.router.navigateByUrl('/' + this.restaurant?.city_name + '/' + this.restaurant.seo_url + '/menus');
    }

    this.menuSuggested = Object.assign([], this.menuSuggestedOriginal);
    if (this.menuSuggestedOriginal.length > 0) {
      this.menuSuggestedOriginal.forEach(menu => {
        const matchingItem = this.carts.find(cart => cart.menu_id == menu.id);
        if (matchingItem) {
          var index = this.menuSuggested.indexOf(menu);
          this.menuSuggested.splice(index, 1);
        }
      });
    }
  }

  fetchRestaurant() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        this.fetchCheckoutList();
        this.surcharges = res.surcharges;
        this.order.restaurant_id = this.restaurant.id;
        if (this.restaurant.meta_title) {
          this.titleService.setTitle(this.restaurant.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_description });
        } else {
          this.titleService.setTitle(this.siteSetting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.siteSetting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.siteSetting.gogrubz_meta_description });
        }
        if (this.order.order_type == 'delivery') {
          if (this.restaurant.currentDeliveryStatus == 'Open') {
            this.order.assoonas = 'now';
          } else {
            this.order.assoonas = 'later';
            this.findTimeSlot()
          }
          this.restaurant.currentStatus = this.restaurant.currentDeliveryStatus;
        } else {
          if (this.restaurant.currentPickupStatus == 'Open') {
            this.order.assoonas = 'now';
          } else {
            this.order.assoonas = 'later';
            this.findTimeSlot()
          }
          this.restaurant.currentStatus = this.restaurant.currentPickupStatus;
        }
        if (this.restaurant?.business?.connect_service) {
          this.publishKey = this.restaurant?.business?.connect_stripe_public_key
          this.isConnect = 'connect';
        } else {
          if (this.restaurant?.site_setting?.stripe_mode == 'Test') {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkeyTest
          } else {
            this.publishKey = this.restaurant?.site_setting?.stripe_publishkey
          }
          this.isConnect = 'normal';
        }

        this.surcharges.forEach(sur => this.surchargeAmount += sur.surcharge_amount);
        this.changeTypeWise();
        this.order.source_latitude = this.restaurant.sourcelatitude
        this.order.source_longitude = this.restaurant.sourcelongitude

        this.stripePaymentMethod = this.restaurant.payment_methods.find(pm => pm.payment_id == '8');

        if (this.userService?.user?.id) {
          if (this.stripePaymentMethod.payment_status == 'Y') {
            this.fetchCards();
          } else {
            this.checkPaymentMethod("COD");
          }
          this.fetchAddressBook();
        }
        this.fetchCategories();

        if (this.publishKey) {
          this.stripe = Stripe(this.publishKey);
        }

        // if (this.restaurant?.site_setting?.paypal_mode == 'Test') {
        //   this.paypalClientKey = this.restaurant?.site_setting?.test_clientid
        // } else {
        //   this.paypalClientKey = this.restaurant?.site_setting?.live_clientid
        // }
      }, err => this.error = err)
    );
  }

  fetchCategories() {
    this.subs.add(
      this.categoryService.get({ nopaginate: 1, prefilled: 1, restaurant_id: this.restaurant.id, order_type: this.order.order_type })
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.menuSuggested = [];
            this.menuSuggestedOriginal = [];
            res.forEach(category => {
              let tempCategory = Object.assign({}, category);
              tempCategory.menu.forEach(menu => {
                if (menu?.is_suggest == 1) {
                  this.menuSuggested.push(menu);
                  this.menuSuggestedOriginal.push(menu);
                }
              });
            });
            this.fetchCarts();
          },
          (err) => { }
        )
    );
  }

  deliveryNow(status) {
    this.order.assoonas = status;
    this.order.delivery_time = '';
    if (this.order.assoonas == 'later') {
      this.findTimeSlot();
      this.modalService.open(this.timeSlotModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
    }
  }

  findTimeSlot() {
    this.isTimeSlotLoading = true; this.errorTimeSlot = null;

    this.subs.add(
      this.restaurantService.timeslot({ restaurant_id: this.restaurant.id, date: this.order.delivery_date }).
        pipe(finalize(() => { this.isTimeSlotLoading = false }))
        .subscribe(
          (res) => {
            if (this.order.order_type == 'delivery') {
              var lastChar = res.delivery.today.slice(-1);
              if (lastChar == ',') {
                res.delivery.today = res.delivery.today.slice(0, -1); // trim last character
              }
              if (res.delivery.today != '') {
                this.timeSlots = res.delivery.today.split(',');
                if (!this.order.assoonas) {
                  if (this.restaurant.currentDeliveryStatus == 'Open') {
                    this.order.assoonas = 'now';
                  } else {
                    this.order.assoonas = 'later';
                  }
                }
                this.order.delivery_time = this.timeSlots[0];
              } else {
                this.timeSlots = [];
                this.order.delivery_time = 'Close';
              }
              // this.restaurant.currentStatus = this.restaurant.currentDeliveryStatus
            } else {
              var lastChar = res.pickup.today.slice(-1);
              if (lastChar == ',') {
                res.pickup.today = res.pickup.today.slice(0, -1); // trim last character
              }
              if (res.pickup.today != '') {
                if (!this.order.assoonas) {
                  if (this.restaurant.currentDeliveryStatus == 'Open') {
                    this.order.assoonas = 'now';
                  } else {
                    this.order.assoonas = 'later';
                  }
                }
                this.timeSlots = res.pickup.today.split(',');
                this.order.delivery_time = this.timeSlots[0]
              } else {
                this.timeSlots = [];
                this.order.delivery_time = 'Close';
              }
              // this.restaurant.currentStatus = this.restaurant.currentPickupStatus
            }
          },
          (err) => {
            this.timeSlots = [];
          }
        )
    )
  }

  fetchSlot(date: string) {
    this.timeSlots = [];
    this.order.delivery_date = this.convertToDate(date);
    this.findTimeSlot();
  }

  selectTime(time: string) {
    this.order.delivery_time = time;
    this.modalService.dismissAll();
  }

  openAddress() {
    this.modalService.open(this.selectAddressModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
  }

  closeSelectAddress() {
    this.modalService.dismissAll();
  }

  showDeliveryCharge(addressBook) {
    this.userService.saveAddress(addressBook);
    this.deliveryAddressId = addressBook.id;
    this.order.address_id = addressBook.id;
    this.order.address = addressBook.flat_no + ',' + addressBook.address;
    if (addressBook.deliveryCharge > 0) {
      this.order.delivery_charge = addressBook.deliveryCharge; // .toFixed(2);
    } else {
      this.order.delivery_charge = 0.00;
    }
    this.order.flat_no = addressBook.flat_no;
    this.order.destination_latitude = addressBook.latitude;
    this.order.destination_longitude = addressBook.longitude;
  }

  openAddAddress(item) {
    if (item == null) {
      this.addressBookAdd = new AddressBook();
    } else {
      this.addressBookAdd = Object.assign({}, item);
    }
    this.modalService.dismissAll();
    this.AddressModelReferance = this.modalService.open(this.addAddressModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
  }

  deleteAddress(addressBook: AddressBook) {
    this.error = null;

    this.subs.add(this.addressBookService.delete(addressBook.id)
      .pipe(finalize(() => { }))
      .subscribe(res => {
        let addressBook = this.userService.getAddress();
        if (addressBook) {
          if (addressBook.id) {
            if (typeof localStorage !== 'undefined') {
              localStorage.removeItem(environment.address);
            }
          }
        }

        this.fetchAddressBook();
        this.notificationService.showSuccess("Address deleted successfully !!", "Gogrubz")
      }, err => { this.error = err; })
    );
  }

  fetchAddressBook() {
    this.isSelectAddressLoading = true; this.errorSelectAddress = null;

    this.subs.add(
      this.addressBookService
        .get({ customer_id: this.user?.id, restaurant_id: this.restaurant.id, nopaginate: "1" })
        .pipe(finalize(() => (this.isSelectAddressLoading = false)))
        .subscribe(
          (res) => {
            let filteredArray = []
            let dontDeliverable = []
            res.forEach(address => {
              if (address.delivery_status == 'delivery_available') {
                filteredArray.push(address);
              } else {
                dontDeliverable.push(address);
              }
            });
            this.notDeliveryaddressBooks = Object.assign([], dontDeliverable);
            this.addressBooks = Object.assign([], filteredArray);
            this.userService.saveAddress(this.addressBooks[0]);
            this.deliveryAddressId = this.addressBooks[0]?.id;
            this.order.address_id = this.addressBooks[0]?.id;
            this.order.address = this.addressBooks[0]?.flat_no + ',' + this.addressBooks[0]?.address;
            if (this.addressBooks[0]?.deliveryCharge > 0) {
              this.order.delivery_charge = this.addressBooks[0]?.deliveryCharge;
            } else {
              this.order.delivery_charge = 0.00;
            }
            this.order.flat_no = this.addressBooks[0]?.flat_no;
            this.order.destination_latitude = this.addressBooks[0]?.latitude;
            this.order.destination_longitude = this.addressBooks[0]?.longitude;
            return;
            // this.addressBooks = res.filter(item => item.delivery_status == 'delivery_available');
          },
          (err) => {
            this.deliveryAddressId = '';
            this.order.address_id = '';
            this.order.address = '';
            this.order.delivery_charge = 0.00;
            this.order.flat_no = '';
            this.order.destination_latitude = '';
            this.order.destination_longitude = '';
            this.addressBooks = [];
          }
        )
    );
  }

  findzipcode(postcode: string) {
    this.isPostcodeLoading = true; this.errorPostcode = null;
    this.addressBookAdd.address = '';

    if (!this.addressBookAdd.zipcode) {
      this.errorPostcode = 'Please enter valid postcode and press search button.';
      this.isPostcodeLoading = false;
    } else {
      this.subs.add(this.userService.postcode(postcode)
        .pipe(finalize(() => this.isPostcodeLoading = false))
        .subscribe(res => {
          var address = '';
          if (res.street) {
            address += res.street;
          }
          if (res.post_town) {
            address += ',' + res.post_town;
          }
          if (res.post_code) {
            address += ',' + res.post_code;
          }

          this.addressBookAdd.latitude = res.latitude;
          this.addressBookAdd.longitude = res.longitude;
          this.addressBookAdd.zipcode = res.post_code;
          this.addressBookAdd.address = address;
        }, err => this.errorPostcode = err)
      );
    }
  }

  onAddressSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    if (this.addressBookAdd.id != null) this.updateAddress(form);
    else this.addAddress(form);
  }

  addAddress(form: NgForm) {
    this.isAddAddressLoading = true; this.errorAddAddress = false;

    this.addressBookAdd.user_id = this.user?.id;
    this.subs.add(
      this.addressBookService.create(this.addressBookAdd).
        pipe(finalize(() => this.isAddAddressLoading = false))
        .subscribe(
          (res) => {
            this.dummyZipcode = res.zipcode;
            this.fetchValidateAddressBook();
            this.AddressModelReferance.close();
            this.addressBookAdd = new AddressBook();
            this.modalService.open(this.selectAddressModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
          },
          (err) => {
            this.notificationService.showError(err, "Gogrubz")
          }
        )
    )
  }

  updateAddress(form: NgForm) {
    this.isAddAddressLoading = true; this.errorAddAddress = null;

    this.subs.add(this.addressBookService.update(this.addressBookAdd)
      .pipe(finalize(() => this.isAddAddressLoading = false))
      .subscribe(res => {
        this.fetchAddressBook();
        this.notificationService.showSuccess("Address updated successfully !!", "Gogrubz");
        this.modalService.dismissAll();
        this.modalService.open(this.selectAddressModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
      }, err => this.errorAddAddress = err)
    );
  }

  fetchValidateAddressBook() {
    this.isSelectAddressLoading = true; this.errorSelectAddress = null;

    this.subs.add(
      this.addressBookService
        .get({ customer_id: this.user?.id, restaurant_id: this.restaurant.id, nopaginate: "1" })
        .pipe(finalize(() => (this.isSelectAddressLoading = false)))
        .subscribe(
          (res) => {
            let filteredArray = []
            let dontDeliverable = []
            res.forEach(address => {
              if (address.delivery_status == 'delivery_available') {
                filteredArray.push(address);
              } else {
                dontDeliverable.push(address);
              }
            });
            this.notDeliveryaddressBooks = Object.assign([], dontDeliverable);
            this.addressBooks = Object.assign([], filteredArray);
            if (this.addressBooks.length > 0) {
              if (this.dummyZipcode != this.addressBooks[0].zipcode) {
                this.notificationService.showError("Sorry your address is out of delivery", "Gogrubz")
              } else {
                this.notificationService.showSuccess("Address addded successfully !!", "Gogrubz")
              }
            }
            this.userService.saveAddress(this.addressBooks[0]);
            this.deliveryAddressId = this.addressBooks[0]?.id;
            this.order.address_id = this.addressBooks[0]?.id;
            this.order.address = this.addressBooks[0]?.flat_no + ',' + this.addressBooks[0]?.address;
            if (this.addressBooks[0]?.deliveryCharge > 0) {
              this.order.delivery_charge = this.addressBooks[0]?.deliveryCharge;
            } else {
              this.order.delivery_charge = 0.00;
            }
            this.order.flat_no = this.addressBooks[0]?.flat_no;
            this.order.destination_latitude = this.addressBooks[0]?.latitude;
            this.order.destination_longitude = this.addressBooks[0]?.longitude;
            return;
            // this.addressBooks = res.filter(item => item.delivery_status == 'delivery_available');
          },
          (err) => {
            this.deliveryAddressId = '';
            this.order.address_id = '';
            this.order.address = '';
            this.order.delivery_charge = 0.00;
            this.order.flat_no = '';
            this.order.destination_latitude = '';
            this.order.destination_longitude = '';
            this.addressBooks = [];
          }
        )
    );
  }

  openPhoneEdit() {
    this.modalService.dismissAll();
    this.loginuser = Object.assign({}, this.user);
    this.phoneModelReferance = this.modalService.open(this.profileModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
  }

  updateUser(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isModelProfileLoading = true; this.ModelProfileerror = null;

    if (this.userService.user.phone_number != this.loginuser.phone_number) {
      this.loginuser.phone_verify = false;
      this.userService
        .disabledPhoneVerify(this.loginuser)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => {
            this.user = res;
            this.order.customer_phone = this.user.phone_number
            this.isCheckoutLoading = false;
            this.counter = 20;
            this.mySubscription.unsubscribe();
          },
          (err) => { }
        );
    }

    this.userService
      .update(this.loginuser)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.notificationService.showSuccess("Phone number updated successfully!!", "Gogrubz")
          this.modalService.dismissAll();
          if ((((!this.user.phone_verify && this.siteSetting.order_verify_type == 'phone') || !this.user.email_verify && this.siteSetting.order_verify_type == 'mail') || (this.siteSetting.order_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.siteSetting?.order_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
      }, err => this.Modelotperror = err)
    );
  }

  otpSend() {
    this.isModelOtpLoading = true;
    this.phoneTab = true;
    if (this.siteSetting.order_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.siteSetting.order_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.siteSetting.order_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.mySubscription = interval(1000).subscribe(x => {
              this.timer();
            });
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  timer() {
    this.counter = this.counter - 1;
    if (this.counter <= 0) {
      this.mySubscription.unsubscribe();
    }
  }

  resendOtp() {
    this.isModelOtpLoading = true;
    this.phoneTab = true;
    if (this.siteSetting.order_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.siteSetting.order_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.siteSetting.order_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.counter = 20;
            this.mySubscription = interval(1000).subscribe(x => {
              this.timer();
            });
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.validateOtp(form)
  }

  validateOtp(form: NgForm) {
    this.isModelOtpLoading = true; this.Modelotperror = null

    // if (this.siteSetting.order_verify_type == 'both') {
    //   if (!this.user.phone_verify && !this.user.email_verify) {
    //     this.user.verify_type = 'both';
    //   } else {
    //     if (!this.user.phone_verify && this.user.email_verify) {
    //       this.user.verify_type = 'phone';
    //     }
    //     if (this.user.phone_verify && !this.user.email_verify) {
    //       this.user.verify_type = 'email';
    //     }
    //   }
    // } else {
    //   if (this.siteSetting.order_verify_type == 'mail') {
    //     this.user.verify_type = 'email';
    //   } else {
    //     this.user.verify_type = this.siteSetting.order_verify_type;
    //   }
    // }
    this.user.verify_type = 'phone';

    this.user.email_otp = this.user.otp
    this.subs.add(
      this.userService.varifyBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.subs.add(this.userService.me()
              .pipe(finalize(() => this.isModelOtpLoading = false))
              .subscribe(res => {
                this.user = res
                this.userService.saveUser(res);
                this.notificationService.showSuccess("OTP verified successfully!", "Gogrubz")
                this.saveOrder();
              }, err => this.Modelotperror = err)
            );
            this.modalService.dismissAll();
          },
          (err) => {
            this.Modelotperror = err;
            this.notificationService.showError(err, "Gogrubz")
          }
        )
    )
  }

  fetchCards() {
    // if (!this.isConnect) {
    //   if (this.restaurant?.business?.connect_service) {
    //     this.isConnect = 'connect';
    //   } else {
    //     this.isConnect = 'normal';
    //   }
    // }

    this.subs.add(
      this.stripeCustomerService.get({ customer_id: this.user?.id, nopaginate: "1" }) // service_type: this.isConnect, 
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
            this.order.payment_method = 'Stripe';
            this.order.card_id = this.stripeCustomers[0].id;
          },
          (err) => {
            this.stripeCustomers = [];
            this.checkPaymentMethod("Stripe");
            this.order.card_id = '';
          }
        )
    );
  }

  changeTypeWise() {
    if (this.order.order_type == 'delivery') {
      if (this.restaurant.servicecharge_delivery) {
        if (this.restaurant.service_charge_type == 1) {
          this.order.service_charge = this.restaurant.service_charge
        } else {
          const serviceCharge = (this.order.order_sub_total * this.restaurant.service_charge) / 100;
          this.order.service_charge = this.precise_round(serviceCharge, 2);
        }
      } else {
        this.order.service_charge = 0;
      }
    }
    if (this.order.order_type == 'pickup') {
      if (this.restaurant.servicecharge_picked) {
        if (this.restaurant.service_charge_type == 1) {
          this.order.service_charge = this.restaurant.service_charge
        } else {
          const serviceCharge = (this.order.order_sub_total * this.restaurant.service_charge) / 100;
          this.order.service_charge = this.precise_round(serviceCharge, 2);
        }
      } else {
        this.order.service_charge = 0;
      }
    }
  }

  selectCheckBox(offer, event, index) {
    this.OfferModelerror = false;

    if (event) {
      let sum: number = offer.quantity;
      this.selected_checkbox_array.forEach(inc => sum += inc.quantity);
      this.selected_checkbox_array.push(offer);
      if (sum <= this.eligibleQty) {
        this.isEligible = true;
      } else {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      }
    } else {
      let decsum: number = 0;
      this.selected_checkbox_array.forEach(dec => decsum += dec.quantity);
      const index = this.selected_checkbox_array.indexOf(offer);
      this.selected_checkbox_array.splice(index, 1);
      decsum = decsum - offer.quantity;
      if (decsum <= this.eligibleQty) {
        this.isEligible = true;
      } else {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      }
    }
  }

  orderType(order_type: string) {
   let oldOrderType = this.order.order_type;
    this.order.order_type = order_type;
    this.cartService.saveOrderType(order_type);
    // Reset mismatch flags
    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;
 
    this.carts = this.cartService.getCart();
    if (this.carts.length > 0) {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      this.carts.forEach(item => {
        const itemOrderType = item.product_order_type?.toLowerCase();
        const itemDaysRaw = item.product_day?.toLowerCase() || 'all';
        const itemDays = itemDaysRaw.split(',').map(day => day.trim());
        if (itemOrderType !== 'both' && itemOrderType !== order_type.toLowerCase()) {
          this.hasOrderTypeMismatch = true;
        }
        if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
          this.hasDayMismatch = true;
        }
      });
      if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
        this.modalService.dismissAll();
        this.modalReference = this.modalService.open(this.itemNotAvailableModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return order_type;
      }
 
      // ✅ Save new order type if no mismatch
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
        this.findTimeSlot();
        this.selected_checkbox_array = [];
        this.eligibleQty = 0;
        this.order.voucher_code = '';
        this.order.voucher_amount = 0;
        this.order.voucher_percentage = 0;
        if (this.user?.id) {
          this.offerCheck();
        }
        this.changeTypeWise();
        this.fetchCheckoutList();
        this.getGrandTotal();
      }
    } else {
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
        this.isOfferLoading = true;
        this.findTimeSlot();
        this.selected_checkbox_array = [];
        this.eligibleQty = 0;
        this.order.voucher_code = '';
        this.order.voucher_amount = 0;
        this.order.voucher_percentage = 0;
        if (this.user?.id) {
          this.offerCheck();
        }
        this.changeTypeWise();
        this.fetchCheckoutList();
        this.getGrandTotal();
      }
    }

    return order_type;
  }

  handleItemNotAvailable(type: string) {
    if (type === 'yes') {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });
 
      itemsToRemove.forEach(item => {
        const index = this.carts.indexOf(item);
        if (index !== -1) {
          this.carts.splice(index, 1);
          this.saveCarts(this.carts);
        }
      });
    } else {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });
 
      if (itemsToRemove.length > 0) {
        if (this.order.order_type == 'delivery') {
          this.order.order_type = 'pickup';
          this.cartService.saveOrderType('pickup'); 
        }else if (this.order.order_type == 'pickup') {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }else{
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }
      }
      this.findTimeSlot();
      this.selected_checkbox_array = [];
      this.eligibleQty = 0;
      this.order.voucher_code = '';
      this.order.voucher_amount = 0;
      this.order.voucher_percentage = 0;
      if (this.user?.id) {
        this.offerCheck();
      }
      this.changeTypeWise();
      this.fetchCheckoutList();
      this.getGrandTotal();
    }
    this.modalService.dismissAll();
  } 

  fetchCheckoutList() {
    this.isCheckoutListLoading = true; this.errorCheckoutList = null;

    this.subs.add(
      this.checkoutListService.get({ restaurant_id: this.restaurant.id, nopaginate: 1, all_type: this.order.order_type }).
        pipe(finalize(() => { this.isCheckoutListLoading = false }))
        .subscribe(
          (res) => {
            this.checkoutLists = res;
          },
          (err) => {
            this.checkoutLists = [];
          }
        )
    )
  }

  updateToOffer(offer, type, index) {
    this.OfferModelerror = false;

    if (type == 'add') {
      this.order.eligible_offers[index].quantity = offer.quantity + 1;
      let sum: number = 0;
      this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
      if (sum > this.eligibleQty) {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      } else {
        this.isEligible = true;
      }
    }
    if (type == 'update') {
      var quantity = offer.quantity - 1;
      if (quantity > 0) {
        this.order.eligible_offers[index].quantity = quantity;
        let sum: number = 0;
        this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
        if (sum > this.eligibleQty) {
          this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
          this.isEligible = false;
        } else {
          this.isEligible = true;
        }
      } else {
        this.order.eligible_offers[index].quantity = 1;
        let sum: number = 0;
        this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
        if (sum > this.eligibleQty) {
          this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
          this.isEligible = false;
        } else {
          this.isEligible = true;
        }
      }
    }
  }

  validateOffer() {
    let validatesum: number = 0;
    this.selected_checkbox_array.forEach(validate => validatesum += validate.quantity);
    if (validatesum == this.eligibleQty) {
      this.selected_checkbox_array.map((obj) => {
        obj.total_price = 0;
        obj.menu_price = 0;
        obj.menu_id = 0;
        return obj;
      })
      this.order.applied_offers = this.order.applied_offers.concat(this.selected_checkbox_array);
      this.modalService.dismissAll();
      this.isEligible = true;
    } else if (validatesum != 0 && validatesum > this.eligibleQty) {
      this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
      this.isEligible = false;
    } else if (validatesum != 0 && validatesum < this.eligibleQty) {
      this.OfferModelerror = "you have must select at least" + this.eligibleQty + " qty.";
      this.isEligible = false;
    } else {
      this.OfferModelerror = "please select product first.";
      this.isEligible = false;
    }
  }

  async initPaypal() {
    this.stripeCustomer.restaurant_id = this.order.restaurant_id;
    this.stripeCustomer.amount = this.getGrandTotal();
    this.stripeCustomer.order_id = this.order.id;
    this.isCheckoutLoading = true;
    this.subs.add(
      this.stripeCustomerService.paypal_payment_intent(this.stripeCustomer)
        .pipe(finalize(() => {  this.isCheckoutLoading = false; }))
        .subscribe(
          (res) => {
            const loader = 'auto'
            const elements = this.stripe.elements({ clientSecret: res.payment_intent_id, loader });
            const paymentElement = elements.create('payment');
            paymentElement.mount('#paypal-button-container');
            this.payElements = elements;
            this.isCheckoutLoading = false;
          },
          (err) => {
            this.errorPaymentMethod = 'Sorry your Payment Faild! Please try again';
            this.isCheckoutLoading = false;
          }
        )
    );
  }

  async initRevolutPay() {
    const amount = Math.round(this.getGrandTotal() * 100);

    this.stripeCustomer.restaurant_id = this.order.restaurant_id;
    this.stripeCustomer.amount = this.getGrandTotal();
    this.stripeCustomer.order_id = this.order.id;
        this.isCheckoutLoading = true;

    this.subs.add(
      this.stripeCustomerService.revolut_payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { this.isCheckoutLoading = false; }))
        .subscribe(
          (res) => {
            const loader = 'auto'
            const elements = this.stripe.elements({ clientSecret: res.payment_intent_id, loader });
            const paymentElement = elements.create('payment');
            paymentElement.mount('#revolut-pay-button-container');
            this.payElements = elements;
            this.isCheckoutLoading = false;
          },
          (err) => {
            this.errorPaymentMethod = 'Sorry your Payment Faild! Please try again';
            this.isCheckoutLoading = false;
          }
        )
    );
  }

  addCard(model) {
    this.isModelLoading = true;
    this.addStripeCustomer = new StripeCustomer();
    this.openModal(model);
  }

  async initApplePay() {
    const amount = Math.round(this.getGrandTotal() * 100);
    const paymentRequest = this.stripe.paymentRequest({
      country: 'GB',
      currency: 'gbp',
      total: {
        label: 'Gogrubz',
        amount: amount,
      },
      requestPayerName: true,
      requestPayerEmail: true,
    });

    const elements = this.stripe.elements();
    const prButton = elements.create('paymentRequestButton', {
      paymentRequest,
    });

    const result = await paymentRequest.canMakePayment();
    if (result) {
      prButton.mount('#payment-request-button');
    } else {
      console.error('Payment Request API not available.');
    }

    paymentRequest.on('paymentmethod', async (ev) => {
      await this.validateCheckout();
      this.isCheckoutLoading = true; this.errorCheckout = null

      if (!this.userService?.user.phone_number) {
        this.modalService.dismissAll();
        this.loginuser = Object.assign({}, this.userService?.user);
        this.modalService.open(this.profileModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        this.isCheckoutLoading = false;
        return
      }

      this.order.carts = this.order.carts.concat(this.order.applied_offers);
      this.order.status = 'Processing';
      this.order.type = 'Web';
      if (this.checkoutLists.length > 0) {
        let ckList = '';
        this.checkoutLists.forEach(checkoutList => {
          if (checkoutList.selected) {
            ckList += checkoutList.message + ',';
          }
        });

        if (this.order.order_description) {
          this.order.order_description = this.order.order_description + ',' + ckList;
        } else {
          if (ckList) {
            this.order.order_description = ckList;
          }
        }
      }

      this.subs.add(
        this.orderService.create(this.order).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.order.id = res.id
              const { paymentmethod } = ev;
              this.stripeCustomer.customer_id = this.user.id;
              this.stripeCustomer.restaurant_id = this.order.restaurant_id;
              this.stripeCustomer.amount = this.getGrandTotal();
              this.stripeCustomer.order_id = this.order.id;
              this.subs.add(
                this.stripeCustomerService.apple_payment_intent(this.stripeCustomer)
                  .pipe(finalize(() => { }))
                  .subscribe(
                    (res) => {
                      this.stripe.confirmCardPayment(res.payment_intent_id, { payment_method: ev.paymentMethod.id })
                        .then((result) => {
                          if (result.error) {
                            ev.complete('fail');
                            this.notificationService.showError("Something went wrong!", "Gogrubz");//result.error
                          } else if (result.paymentIntent.status == "succeeded") {
                            ev.complete('success');
                            this.order.payment_status = 'P';
                            this.order.paid_full = 'Yes';
                            this.order.transaction_id = result.paymentIntent.id
                            if (typeof localStorage !== 'undefined') {
                              localStorage.removeItem(environment.order);
                            }
                            this.cartService.removeCart();
                            var orderId = btoa(this.order.id);
                            this.subs.add(this.userService.me()
                              .pipe(finalize(() => { }))
                              .subscribe(res => {
                                this.userService.saveUser(res);
                              }, err => this.error = err)
                            );
                            this.modalService.dismissAll();
                            this.router.navigateByUrl(`/order-details/${orderId}`);
                          }
                        });
                    },
                    (err) => {
                      this.notificationService.showError('Sorry your Payment Faild! Please try again', "Gogrubz");
                    }
                  )
              );
            },
            (err) => {
              this.errorCheckout = err;
              this.isCheckoutLoading = false;
              this.modalService.dismissAll();
            }
          )
      )
    });
  }

  initCard() {
    var elements = this.stripe.elements();

    var style = {
      base: {
        'height': '52px',
        'fontFamily': 'Visby CF',
        'fontWeight': '700',
        'borderRadius': '10px',
        'lineHeight': '1.5',
        'fontSize': '16px',
        'color': '#000',
      }
    };

    // Card number
    this.card = elements.create('cardNumber', {
      'placeholder': 'Enter card number',
      'style': style
    });
    this.card.mount('#card-number');

    // CVC
    var cvc = elements.create('cardCvc', {
      'placeholder': 'CVC',
      'style': style
    });
    cvc.mount('#card-cvc');

    // Card expiry
    var exp = elements.create('cardExpiry', {
      'placeholder': 'Expiry date',
      'style': style
    });
    exp.mount('#card-exp');

    // Postal Code
    var postalCode = elements.create('postalCode', {
      'placeholder': 'Zip Code',
      'style': style
    });
    postalCode.mount('#postalCode');

    // this.card = elements.create('card', { hidePostalCode: true });
    // this.card.mount(this.cardInfo);
    this.isModelLoading = false;
  }

  async handleForm(e) {
    e.preventDefault();
    // this.isModelLoading = true;
    this.isCheckoutLoading = true;
    this.Modelerror = false;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        if (!result.error) {
          this.stripeCustomer.customer_id = this.order.customer_id;
          this.stripeCustomer.customer_name = this.order.customer_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = this.isConnect;
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isCheckoutLoading = false))
              .subscribe(
                (res) => {
                  this.subs.add(
                    this.stripeCustomerService.get({ customer_id: this.user?.id, nopaginate: "1" }) // service_type: this.isConnect, 
                      .pipe(finalize(() => { }))
                      .subscribe(
                        (res) => {
                          this.stripeCustomers = res;
                          this.order.payment_method = 'Stripe';
                          this.order.card_id = this.stripeCustomers[0].id;
                          this.validateCheckout();
                        },
                        (err) => {
                          this.stripeCustomers = [];
                          this.checkPaymentMethod("COD");
                        }
                      )
                  );
                  // this.notificationService.showSuccess("Card addded successfully !!", "Gogrubz")
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.Modelerror = err;
                }
              )
          )
        } else {
          this.isCheckoutLoading = false
          if (!this.order.card_id) {
            this.Modelerror = 'Please enter your card details first!';//result.error.message;
          } else {
            this.Modelerror = 'Something went wrong!';//result.error.message;
          }
        }
      });
  }

  voucherCheck() {
    this.errorVoucher = false;

    if (!this.order.voucher_code || this.order.voucher_code == undefined) {
      this.errorVoucher = 'Please enter voucher code';
    }
    else {
      this.voucher.restaurant_id = this.restaurant.id;
      this.voucher.voucher_code = this.order.voucher_code;
      this.voucher.order_type = this.order.order_type;

      this.subs.add(
        this.voucherService.find(this.voucher).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              if (res.minimum_value > this.order.order_sub_total) {
                this.errorVoucher = 'Please purchase minimum ' + res.minimum_value;
                this.order.voucher_code = '';
                this.order.voucher_percentage = 0;
                this.order.voucher_amount = 0;
                return;
              }
              this.assignVoucher = res;
              this.order.voucher_code = res.voucher_code;
              if (res.offer_mode == 'price') {
                this.order.voucher_amount = res.offer_value;
              }
              if (res.offer_mode == 'percentage') {
                this.order.voucher_percentage = res.offer_value;
                this.order.voucher_amount = this.order.order_sub_total * res.offer_value / 100;
              }
              if (res.offer_mode == 'free_delivery' && this.order.order_type == 'delivery') {
                this.order.voucher_amount = this.order.delivery_charge;
                this.order.delivery_charge = 0;
              }
              if (res.offer_mode == 'free_delivery' && this.order.order_type == 'pickup') {
                this.errorVoucher = 'Voucher not found.';
                this.order.voucher_code = '';
                this.order.voucher_percentage = 0;
                this.order.voucher_amount = 0;
                return;
              }
              this.order.offer_amount = 0;
              this.order.offer_percentage = 0;
              this.order.applied_offers = [];
            },
            (err) => {
              this.errorVoucher = err;
            }
          )
      )
    }
  }

  voucherAsssigned() {
    if (this.assignVoucher?.minimum_value > this.order.order_sub_total) {
      this.assignVoucher = new Voucher();
      this.order.voucher_code = '';
      this.order.voucher_percentage = 0;
      this.order.voucher_amount = 0;
      let order = JSON.parse(this.cartService.getOrder());
      this.order.delivery_charge = order?.delivery_charge;
      this.getGrandTotal();
      this.selected_checkbox_array = [];
      this.eligibleQty = 0;
      if (this.user?.id && !this.order.voucher_code) {
        this.offerCheck();
      }
      if (this.order.payment_method == 'Apple Pay') {
        this.initApplePay();
      }
    }
  }

  voucherRemove() {
    this.order.voucher_code = '';
    this.order.voucher_percentage = 0;
    this.order.voucher_amount = 0;
    let order = JSON.parse(this.cartService.getOrder());
    this.order.delivery_charge = order?.delivery_charge;
    this.getGrandTotal();
    this.selected_checkbox_array = [];
    this.eligibleQty = 0;
    this.assignVoucher = new Voucher();
    if (this.user?.id) {
      this.offerCheck();
    }
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  offerCheck() {
    this.order.carts = this.carts;
    this.order.restaurant_id = this.restaurant.id;

    this.subs.add(
      this.orderService.offercheck(this.order).
        pipe(finalize(() => this.isUpdateCart = false))
        .subscribe(
          (res) => {
            this.order = res;
            this.getGrandTotal();
            // this.initPaypal();
            if (this.order.eligible_offers.length > 0) {
              this.modalService.open(this.multiplePriceOfferModal, { backdrop: 'static', backdropClass: 'customBackdrop', });
              this.eligibleQty = this.order.eligible_offers[0].quantity;
            }

            let addressBook = this.userService.getAddress();
            if (this.order.order_type == 'delivery' && addressBook) {
              this.deliveryAddressId = addressBook.id;
              this.order.address_id = addressBook.id;
              this.order.address = addressBook.flat_no + ',' + addressBook.address;
              if (addressBook.deliveryCharge > 0) {
                this.order.delivery_charge = addressBook.deliveryCharge;
              } else {
                this.order.delivery_charge = 0.00;
              }
              this.order.flat_no = addressBook.flat_no;
              this.order.destination_latitude = addressBook.latitude;
              this.order.destination_longitude = addressBook.longitude;
            }
            this.isOfferLoading = false;
          },
          (err) => {
            this.isOfferLoading = false;
          }
        )
    )
  }

  charityAdd(event) {
    if (event.target.checked === true) {
      this.order.charity_amount = this.restaurant?.site_setting?.charity_amount;
      this.order.charity_message = this.restaurant?.site_setting?.charity_message;
    } else {
      this.order.charity_amount = 0;
      this.order.charity_message = '';
    }
    this.getGrandTotal();
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  redeemAdd(event) {
    if (event.target.checked === true) {
      this.order.reward_used = 'Y';
      this.order.reward_offer = this.order.rewardPoint;
      this.order.reward_offer_percentage = this.order.rewardPercentage;
    } else {
      this.order.reward_used = 'N';
      this.order.reward_offer = 0;
      this.order.reward_offer_percentage = 0;
    }
    this.getGrandTotal();
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  walletAdd(event) {
    if (event.target.checked === true) {

    } else {
      this.order.wallet_amount = 0;
    }
    this.getGrandTotal();
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  driverTip(tip: number) {
    if (tip > 0) {
      this.order.driver_tip = tip;
    } else {
      this.order.driver_tip = 0;
    }
    this.getGrandTotal();
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  checkPaymentMethod(payment_method_name: string) {
    this.errorPaymentMethod = null;
    this.errorPaypal = null;
    this.order.payment_method = payment_method_name;
    this.order.card_id = '';
    if (this.order.payment_method == 'Stripe') {
      this.initCard();
    } else if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    } else if (this.order.payment_method == 'Paypal') {
      this.initPaypal();
    } else if (this.order.payment_method == 'Revolut Pay') {
      this.initRevolutPay();
    }
    // if (this.order.payment_method == 'Stripe' && this.stripeCustomers.length > 0) {
    //   this.order.card_id = this.stripeCustomers[0].id;
    // } else {
    //   this.order.card_id = '';
    // }
  }

  cardPaymentMethod(cardId: string) {
    this.errorPaymentMethod = null;
    this.errorPaypal = null;
    this.order.payment_method = 'Stripe';
    this.order.card_id = cardId;
  }

  async validateCheckout() {
    this.errorPaymentMethod = false;

    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;
 
    const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
    this.carts.forEach(item => {
      const itemOrderType = item.product_order_type?.toLowerCase();
      const itemDaysRaw = item.product_day?.toLowerCase() || 'all';
      const itemDays = itemDaysRaw.split(',').map(day => day.trim());
      if (itemOrderType !== 'both' && itemOrderType !== this.order.order_type.toLowerCase()) {
        this.hasOrderTypeMismatch = true;
      }
      if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
        this.hasDayMismatch = true;
      }
    });
    if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
      this.modalService.dismissAll();
      this.modalReference = this.modalService.open(this.itemNotAvailableModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
      return;
    }

    if (this.carts.length > 0) {
      if (this.carts[0].restaurant_id != this.restaurant.id) {
        this.modalService.open(this.itemModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return;
      }
    }

    if (!this.order.payment_method) {
      window.scroll(0, 0);
      this.errorPaymentMethod = 'Please select payment method';
      this.isCheckoutLoading = false;
      return
    }

    if (this.order.delivered_time == 'later' && !this.order.delivery_time) {
      window.scroll(0, 0);
      this.errorPaymentMethod = 'Please select ' + this.order.order_type + ' time';
      this.deliveryNow('later');
      this.isCheckoutLoading = false;
    }

    if (this.order.delivery_time == 'Close') {
      window.scroll(0, 0);
      this.errorPaymentMethod = 'Please select ' + this.order.order_type + ' time';
      this.deliveryNow('later');
      this.isCheckoutLoading = false;
    }

    if (this.order.assoonas == 'now') {
      let now = new Date();
      let hours = now.getHours();
      let minute = now.getMinutes();
      let ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12;
      this.order.delivery_time = hours + ":" + minute + " " + ampm;
    }

    if (this.order.order_type == 'delivery' && !this.order.address_id) {
      window.scroll(0, 0);
      this.errorPaymentMethod = 'Please select delivery address';
      this.openAddress();
      this.isCheckoutLoading = false;
    }

    if (this.order.payment_method != 'Stripe') {
      this.order.card_id = '';
      this.order.transaction_id = '';
    }

    if (this.order.order_type != 'delivery') {
      this.order.address_id = '';
      this.order.address = '';
      this.order.flat_no = '';
      this.order.destination_latitude = '';
      this.order.destination_longitude = '';
    }

    if (this.surcharges.length > 0) {
      this.order.surcharges = this.surcharges;
      this.order.surcharge_amount = this.surchargeAmount;
    }

    if (!this.errorPaymentMethod) {

      if (this.order.payment_wallet) {
        this.order.payment_wallet = 'Yes';
        if (this.user?.wallet_amount >= this.order.order_grand_total) {
          this.order.card_id = '';
          this.order.transaction_id = '';
          this.order.payment_method = 'Wallet';
          this.order.split_payment = 'No';
          this.order.payment_status = 'P';
          this.order.paid_full = 'Yes';
          this.saveOrder();
        } else {
          this.order.split_payment = 'Yes';
        }
      }

      if (this.order.payment_method == 'Stripe' && !this.order.card_id) {
        window.scroll(0, 0);
        this.errorPaymentMethod = 'Please select any payment method or credit card.';
        this.isCheckoutLoading = false;
        return
      }

      if (this.order.payment_method == 'Stripe') {
        this.order.payment_status = 'P';
        this.order.paid_full = 'Yes';
        // this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.order.card_id);
        // this.createPaymentIntent(this.stripeCustomer);
        this.saveOrder();
      } else if (this.order.payment_method == 'Paypal' || this.order.payment_method == 'Revolut Pay') {
        this.order.payment_status = 'P';
        this.order.paid_full = 'Yes';
        this.processRevolutAndPaypalPayment();
      } else if (this.order.payment_method == 'COD') {
        this.order.payment_status = 'NP';
        this.saveOrder();
      } else {
      }
    }
  }

  cartempty() {
    this.carts = [];
    this.cartService.saveCart(this.carts);
    this.cartService.removeCart();
    this.modalService.dismissAll();
    this.router.navigateByUrl('/' + this.restaurant.city_name + '/' + this.restaurant.seo_url + '/menus');
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    // || event.keyCode == 32 
    if (event.keyCode == 189 || event.keyCode == 9 || event.keyCode == 8 || event.keyCode == 48 || (event.keyCode >= 97 && event.keyCode <= 122) || (event.keyCode >= 65 && event.keyCode <= 90)) {
    } else {
      event.preventDefault();
    }
  }

  saveOrder() {
    this.isCheckoutLoading = true; this.errorCheckout = null

    if (!this.userService?.user.phone_number) {
      this.modalService.dismissAll();
      this.loginuser = Object.assign({}, this.userService?.user);
      this.modalService.open(this.profileModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
      this.isCheckoutLoading = false;
      return
    }

    if (this.order.payment_method == 'COD') {
      if (!this.userService?.user.phone_verify && (this.siteSetting.order_verify_type == 'phone' || this.siteSetting.order_verify_type == 'both') && this.siteSetting?.order_verify == '1') {
        this.otpSend();
        this.modalService.open(this.otpModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        this.isCheckoutLoading = false;
        return
      }
    }

    this.order.carts = this.order.carts.concat(this.order.applied_offers);
    this.order.status = 'Pending';
    this.order.type = 'Web';
    if (this.order.payment_method == 'COD') {
      this.order.payment_method = 'cod';
    }

    if (this.checkoutLists.length > 0) {
      let ckList = '';
      this.checkoutLists.forEach(checkoutList => {
        if (checkoutList.selected) {
          ckList += checkoutList.message + ',';
        }
      });

      if (this.order.order_description) {
        this.order.order_description = this.order.order_description + ',' + ckList;
      } else {
        if (ckList) {
          this.order.order_description = ckList;
        }
      }
    }
    // this.modalService.open(this.placeModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
    this.subs.add(
      this.orderService.create(this.order).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            if (this.order.payment_method == 'Stripe') {
              this.order.id = res.id;
              this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.order.card_id);
              this.createPaymentIntent(this.stripeCustomer);
            } else {
              this.isCheckoutLoading = false;
              if (typeof localStorage !== 'undefined') {
                localStorage.removeItem(environment.order);
              }
              this.cartService.removeCart();
              var orderId = btoa(res.id);
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isCheckoutLoading = false))
                .subscribe(res => {
                  this.userService.saveUser(res);
                }, err => this.errorCheckout = err)
              );
              this.modalService.dismissAll();
              this.router.navigateByUrl(`/order-details/${orderId}`);
            }
          },
          (err) => {
            this.errorCheckout = err;
            this.isCheckoutLoading = false;
            this.modalService.dismissAll();
          }
        )
    )
  }

  async processRevolutAndPaypalPayment() {
    this.isCheckoutLoading = true;
    this.errorCheckout = null;

    try {

      if (!this.userService?.user.phone_number) {
        this.modalService.dismissAll();
        this.loginuser = Object.assign({}, this.userService?.user);
        this.modalService.open(this.profileModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        this.isCheckoutLoading = false;
        return
      }
      // First save the order to get an order ID
      this.order.carts = this.order.carts.concat(this.order.applied_offers);
      this.order.status = 'Processing';
      this.order.type = 'Web';

      if (this.checkoutLists.length > 0) {
        let ckList = '';
        this.checkoutLists.forEach(checkoutList => {
          if (checkoutList.selected) {
            ckList += checkoutList.message + ',';
          }
        });

        if (this.order.order_description) {
          this.order.order_description = this.order.order_description + ',' + ckList;
        } else {
          if (ckList) {
            this.order.order_description = ckList;
          }
        }
      }

      // Create order first
      this.subs.add(
        this.orderService.create(this.order).
          pipe(finalize(() => { }))
          .subscribe(
            async (res) => {
              this.order.id = res.id;
              // Now process Revolut Pay payment
              const { error, paymentIntent } = await this.stripe.confirmPayment({
                elements: this.payElements,
                confirmParams: {
                  return_url: `${window.location.origin}/order-details/${btoa(res.id)}`,
                },
              });

              if (error) {
                this.notificationService.showError("Something went wrong!", "Gogrubz");//result.error
                this.isCheckoutLoading = false;
              } else {
                this.order.payment_status = 'P';
                this.order.paid_full = 'Yes';
                this.order.transaction_id = paymentIntent.id;
                if (typeof localStorage !== 'undefined') {
                  localStorage.removeItem(environment.order);
                }
                this.cartService.removeCart();
                var orderId = btoa(this.order.id);
                this.subs.add(this.userService.me()
                  .pipe(finalize(() => { }))
                  .subscribe(res => {
                    this.userService.saveUser(res);
                  }, err => this.error = err)
                );
                this.modalService.dismissAll();
                this.isCheckoutLoading = false;
              }
            },
            (err) => {
              this.errorCheckout = err;
              this.isCheckoutLoading = false;
              this.modalService.dismissAll();
            }
          )
      );
    } catch (error) {
      this.errorCheckout = error;
      this.isCheckoutLoading = false;
      this.modalService.dismissAll();
    }
  }

  async createPaymentIntent(stripeCustomerNew) {
    this.isCheckoutLoading = true;

    this.stripeCustomer = stripeCustomerNew;
    this.stripeCustomer.restaurant_id = this.order.restaurant_id;
    this.stripeCustomer.amount = this.getGrandTotal();
    this.stripeCustomer.order_id = this.order.id;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.errorPaymentMethod = 'Sorry your Payment Faild! Please try again';
            this.isCheckoutLoading = false;
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isCheckoutLoading = false;
        this.errorPaymentMethod = 'Something went wrong!';//result.error.message;
      } else {
        if (result.paymentIntent.status === 'succeeded') {
          this.order.payment_status = 'P';
          this.order.paid_full = 'Yes';
          this.order.transaction_id = result.paymentIntent.id
          // this.saveOrder();
          // this.isCheckoutLoading = false;
          if (typeof localStorage !== 'undefined') {
            localStorage.removeItem(environment.order);
          }
          this.cartService.removeCart();
          var orderId = btoa(this.order.id);
          this.subs.add(this.userService.me()
            .pipe(finalize(() => this.isCheckoutLoading = false))
            .subscribe(res => {
              this.userService.saveUser(res);
            }, err => this.error = err)
          );
          this.modalService.dismissAll();
          this.router.navigateByUrl(`/order-details/${orderId}`);
        }
      }
    });
  }

  openModal(model) {
    this.selectedSubAddonstring = '';
    this.selectedSubAddonPrice = 0;

    this.modalReference = this.modalService.open(model, this.modalOptions);
    this.modalReference.result.then(
      (result) => {
        for (let mainAddon of this.selectedVariant.main_addons) {
          for (let subAddon of mainAddon.sub_addons) {
            if (mainAddon.selectedSubAddonId == subAddon.id) {
              console.log(subAddon.subaddons_name + " is selected")
            }
            if (subAddon.selected) {
              console.log(subAddon.subaddons_name + " is selected")
            }
          }
        }
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
    // this.modalService.open(model, this.modalOptions).result.then(
    //   (result) => {
    //     console.log(`Save  ${this.getDismissReason(result)}`);
    //   },
    //   (reason) => {
    //     console.log(`Dismissed ${this.getDismissReason(reason)}`);
    //   }
    // );
    this.initCard()
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  addItemToCart(model, menu, menuId) {
    this.selectedMenu = menu;
    const cart = this.carts.find(cart => cart.menu_id == menu.id);
    var index = this.carts.indexOf(cart);

    if (this.carts.length > 0) {
      if (this.carts[0].restaurant_id != this.restaurant.id) {
        this.modalService.open(this.itemModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return;
      }
    }

    if (!cart || this.carts[index].subaddons_name) {
      if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'single') {
        let cart = new Cart();
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant.id;
        cart.menu_name = this.selectedMenu.menu_name;
        if (this.selectedMenu.product_percentage > 0) {
          cart.menu_price = this.selectedMenu?.variants[0]?.orginal_price - (this.selectedMenu?.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = this.selectedMenu?.variants[0]?.orginal_price - (this.selectedMenu?.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
        } else {
          cart.menu_price = this.selectedMenu?.variants[0]?.orginal_price;
          cart.total_price = this.selectedMenu?.variants[0]?.orginal_price;
        }
        cart.quantity = 1;
        cart.customer_id = this.user?.id;
        if (this.restaurant?.image_type == 'Yes') {
          cart.image_url = menu.image_url;
        } else {
          cart.image_url = '';
        }
        this.carts.push(cart);
        this.saveCarts(this.carts);
        // this.notification.info('item added!', null);
      } else if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'multiple') {
        this.fetchMenuItem(model, menuId);
      } else if (this.selectedMenu.menu_addon == 'Yes' && this.selectedMenu.price_option == 'single') {
        this.fetchVartiantItem(model, menuId, this.selectedMenu?.variants[0].id);
      } else {
        this.fetchVartiantItem(model, menuId, this.selectedMenu?.variants[0].id);
      }
    } else {
      var index = this.carts.indexOf(cart);
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
      this.saveCarts(this.carts);
    }
    this.fetchCarts();
  }

  fetchMenuItem(model, menuId) {
    this.isModelLoading = true;

    this.subs.add(
      this.menuService
        .show(menuId)
        .pipe(finalize(() => (this.isModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            this.openModal(model);
          },
          (err) => { }
        )
    );
  }

  fetchVartiantItem(model, menuId, varaintId) {
    this.isAddonModelLoading = true;

    this.subs.add(
      this.menuService.show(menuId)
        .pipe(finalize(() => (this.isAddonModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            if (this.selectedMenu?.variants.length > 1) {
              this.selectedVariant = this.selectedMenu?.variants.find(
                (varint) => varint.id == varaintId
              );
              this.addVariant = this.selectedVariant;
              if (this.selectedVariant.main_addons) {
                this.openModal(model);
              } else {
                this.selectedSubAddonstring = '';
                this.selectedSubAddonPrice = 0;
              }
            } else {
              this.selectedVariant = this.selectedMenu?.variants[0];
              this.modalReference = this.modalService.open(this.addonModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
            }
          },
          (err) => { }
        )
    );
  }

  fetchVartiantAddonItem(model, menuId, varaintId) {
    this.isAddonModelLoading = true; this.selectedQnt = 1;

    this.subs.add(
      this.menuService.show(menuId)
        .pipe(finalize(() => (this.isAddonModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            this.selectedSubAddonstring = '';
            this.selectedSubAddonPrice = 0;
            this.selectedAddonPrice = 0;
            this.subAddonVisible = false;
            this.selectedAddonDummy = [];
            if (this.selectedMenu?.variants.length > 1) {
              this.selectedVariant = this.selectedMenu?.variants.find(
                (varint) => varint.id == varaintId
              );
              if (this.selectedVariant.main_addons) {
                this.subAddonVisible = true;
                // this.openModal(model);
              } else {
                this.selectedSubAddonstring = '';
                this.selectedSubAddonPrice = 0;
                this.selectedAddonPrice = 0;
              }
            } else {
              this.selectedVariant = this.selectedMenu?.variants[0];
              this.modalReference = this.modalService.open(this.addonModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
            }
          },
          (err) => { }
        )
    );
  }

  editAddon() {
    this.subAddonVisible = true;
    this.editAddonVisible = true;
    this.selectedMenu = Object.assign({}, this.selectedDummyMenu);
    this.selectedVariant = Object.assign({}, this.selectedDummyVariant);
  }

  closeAll() {
    this.modalService.dismissAll();
    this.selectedAddonPrice = 0;
    this.selectedQnt = 1;
    this.selectedAddonDummy = [];
    this.subAddonVisible = false;
    this.editAddonVisible = false;
    this.selectedVariant = new Variant();
    this.addVariant = null;
  }

  validateMultiple(menuModal) {
    if (!this.selectedVariant.id) {
      this.selectedVariant.required_error = true;
      return;
    }

    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }

        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id)
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id)
          return;
        }

        if (subAddonCount == 0 && mainAddon.mainaddons_mini_count == 0 && this.addonVisible) {
          this.selectedSubAddonPrice = this.selectedVariant.orginal_price;
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id)
          return;
        }
      }
    }

    if (this.selectedMenu.menu_addon == 'No') {
      this.selectedSubAddonstring = this.selectedVariant.sub_name;
      this.selectedSubAddonPrice = this.selectedVariant.orginal_price;
    }

    const cart = this.carts.find(cart => cart.menu_id == this.selectedMenu.id);
    var index = this.carts.indexOf(cart);
    if (cart && this.carts[index].subaddons_name == this.selectedSubAddonstring) {
      var index = this.carts.indexOf(cart);
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
    } else {
      let cart = new Cart();
      cart.menu_id = this.selectedMenu.id;
      cart.restaurant_id = this.restaurant.id;
      cart.menu_name = this.selectedMenu.menu_name;
      cart.subaddons_name = this.selectedSubAddonstring;
      cart.menu_price = this.selectedSubAddonPrice;
      cart.total_price = this.selectedQnt * this.selectedSubAddonPrice;
      cart.total_price.toFixed(2);
      cart.quantity = this.selectedQnt;
      cart.customer_id = this.user?.id;
      if (this.restaurant?.image_type == 'Yes') {
        cart.image_url = this.selectedMenu.image_url;
      } else {
        cart.image_url = '';
      }
      this.carts.push(cart);
    }
    this.saveCarts(this.carts);
    // this.notification.info('item added!', null);
    this.modalService.dismissAll();
  }

  validate(addonModel) {
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }
        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          mainAddon.min_error = true;
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          mainAddon.max_error = true;
          return;
        }
      }
    }
    this.addonVisible = false;
    // create cart menu addon string 
    var mainAddonCount: number = 0;
    this.selectedSubAddonstring = '';
    this.selectedSubAddonPrice = 0;
    this.selectedAddonPrice = 0;
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        var subAddonCount: number = 0;
        if (mainAddonCount <= 0) {
          // this.selectedSubAddonstring = this.selectedVariant.sub_name + ',';
          if (this.selectedMenu.product_percentage > 0) {
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + (this.selectedVariant.orginal_price - (this.selectedVariant.orginal_price * this.selectedMenu.product_percentage / 100));
          } else {
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + this.selectedVariant.orginal_price;
          }
        }
        for (let subAddon of mainAddon.sub_addons) {
          if (subAddonCount == 0 && (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id)) {
            this.selectedSubAddonstring += mainAddon.mainaddons_name;
          }
          if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
            this.selectedSubAddonstring += ' (' + subAddon.subaddons_name + ') +';
            // + '(' + this.currencyPipe.transform(subAddon.subaddons_price, 'GBP', 'symbol', '1.2-2') + ')' 
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + subAddon.subaddons_price;
            subAddonCount = subAddonCount + 1;
          }
        }
        mainAddonCount = mainAddonCount + 1;
        var lastChar = this.selectedSubAddonstring.slice(-1);
        if (lastChar == '+') {
          this.selectedSubAddonstring = this.selectedSubAddonstring.slice(0, -1); // trim last character
        }

        if ((mainAddonCount) != this.selectedVariant?.main_addons?.length) {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
              this.selectedSubAddonstring = this.selectedSubAddonstring + ",";
            }
          }
        }
      }
    } else {
      if (this.selectedMenu.product_percentage > 0) {
        this.selectedSubAddonPrice = this.selectedVariant?.orginal_price - (this.selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        this.selectedSubAddonPrice = this.selectedVariant?.orginal_price;
      }
    }

    if (this.selectedMenu?.variants?.length <= 1) {
      const cart = this.carts.find(cart => cart.menu_id == this.selectedMenu.id);
      var index = this.carts.indexOf(cart);
      if (cart && this.carts[index].subaddons_name == this.selectedSubAddonstring) {
        var index = this.carts.indexOf(cart);
        this.carts[index].quantity = this.carts[index].quantity + 1;
        this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
        this.carts[index].total_price.toFixed(2);
      } else {
        let cart = new Cart();
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant.id;
        cart.menu_name = this.selectedMenu.menu_name;
        cart.subaddons_name = this.selectedSubAddonstring;
        cart.menu_price = this.selectedSubAddonPrice;
        cart.total_price = this.selectedQnt * this.selectedSubAddonPrice;
        cart.total_price.toFixed(2);
        cart.quantity = this.selectedQnt;
        cart.customer_id = this.user?.id;
        cart.image_url = this.selectedMenu.image_url;
        this.carts.push(cart);
      }
      if (this.selectedMenu?.variants?.length <= 1) {
        this.modalReference.close();
      } else {
        this.subAddonVisible = false;
      }
      this.saveCarts(this.carts);
    }

    if (this.selectedMenu?.variants?.length <= 1) {
      this.modalReference.close();
    } else {
      this.subAddonVisible = false;
    }
  }

  addprice(event, price, Id) {
    if (!this.editAddonVisible) {
      if (this.selectedAddonDummy.length > 0) {
        const addon = this.selectedAddonDummy.find(addon => addon.id == Id);
        var index = this.selectedAddonDummy.indexOf(addon);
        if (addon) {
          this.selectedAddonPrice = this.selectedAddonPrice - addon.price;
          this.selectedAddonPrice = this.selectedAddonPrice + price;
          this.selectedAddonDummy[index].price = price;
        } else {
          let addonObject: NewAddon = new NewAddon();
          addonObject.id = Id;
          addonObject.price = price;
          this.selectedAddonDummy.push(addonObject);
          this.selectedAddonPrice = this.selectedAddonPrice + price;
        }
      } else {
        let addonObject: NewAddon = new NewAddon();
        addonObject.id = Id;
        addonObject.price = price;
        this.selectedAddonDummy.push(addonObject);
        this.selectedAddonPrice = this.selectedAddonPrice + price;
      }
    }
  }

  addCheckboxPrice(event, price) {
    if (!this.editAddonVisible) {
      if (event.checked) {
        this.selectedAddonPrice = this.selectedAddonPrice + price;
      } else {
        this.selectedAddonPrice = this.selectedAddonPrice - price;
      }
    }
  }

  saveCarts(carts: Cart[]) {
    this.isUpdateCart = true;

    this.cartService.saveCart(carts);
    this.fetchCarts();
    this.selectedMenu = null;
    this.selectedSubAddonstring = null;
    this.selectedSubAddonPrice = 0;
    this.selectedAddonPrice = 0;
    this.editAddonVisible = false;
    this.subAddonVisible = false;
    this.selectedAddonDummy = [];
    this.selectedVariant = new Variant();
    this.addVariant = null;
    this.selectedQnt = 1;

    this.carts = this.cartService.getCart();
    this.selected_checkbox_array = [];
    this.eligibleQty = 0;
    if (this.userService?.user?.id) {
      if (!this.assignVoucher?.id) {
        this.offerCheck();
      } else {
        this.isUpdateCart = false;
      }
    } else {
      this.isUpdateCart = false;
    }
    this.changeTypeWise();
    this.getGrandTotal();
    if (this.user?.id) {
      this.voucherAsssigned();
    }
    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  updateSelected(event: string) {
    if (event == 'add') {
      this.selectedQnt = this.selectedQnt + 1;
    }

    if (event == 'remove') {
      var quantity = this.selectedQnt - 1;
      if (quantity > 0) {
        this.selectedQnt = quantity;
      } else {
        this.selectedQnt = 1;
      }
    }
  }

  updateToCart(cart: Cart, index, event: string) {
    this.isUpdateCart = true;

    if (event == 'add') {
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;
      if (quantity > 0) {
        this.carts[index].quantity = quantity;
        this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
        this.carts[index].total_price.toFixed(2);
      } else {
        this.carts.splice(index, 1);
      }
    }

    if (event == 'delete') {
      this.carts.splice(index, 1);
    }
    this.cartService.saveCart(this.carts);
    this.carts = this.cartService.getCart();

    this.selected_checkbox_array = [];
    this.eligibleQty = 0;
    if (this.user?.id) {
      if (!this.assignVoucher?.id) {
        this.offerCheck();
      } else {
        this.isUpdateCart = false;
      }
    } else {
      this.isUpdateCart = false;
    }
    this.changeTypeWise();
    this.getGrandTotal();
    if (this.user?.id) {
      this.voucherAsssigned();
    }

    if (this.carts.length <= 0) {
      this.router.navigateByUrl('/' + this.restaurant?.city_name + '/' + this.restaurant.seo_url + '/menus');
    }
    // this.order.voucher_code = '';
    // this.order.voucher_amount = 0;
    // this.order.voucher_percentage = 0;

    this.menuSuggested = Object.assign([], this.menuSuggestedOriginal);
    if (this.menuSuggestedOriginal.length > 0) {
      this.menuSuggestedOriginal.forEach(menu => {
        const matchingItem = this.carts.find(cart => cart.menu_id === menu.id);
        if (matchingItem) {
          var index = this.menuSuggested.indexOf(menu);
          this.menuSuggested.splice(index, 1);
        }
      });
    }

    if (this.order.payment_method == 'Apple Pay') {
      this.initApplePay();
    }
  }

  getGrandTotal() {
    if (this.userService?.user?.id) {
      this.user = this.userService?.user;
    }
    let grandTotal = 0;
    this.changeTypeWise();

    let addressBook = this.userService.getAddress();
    if (this.order.order_type == 'delivery' && addressBook && this.addressBooks) {
      const addId = this.addressBooks.find(obj => obj.id === addressBook.id);
      if (addId) {
        this.deliveryAddressId = addressBook.id;
        this.order.address_id = addressBook.id;
        this.order.address = addressBook.flat_no + ',' + addressBook.address;
        if (addressBook.deliveryCharge > 0 && this.assignVoucher.offer_mode != 'free_delivery') {
          this.order.delivery_charge = addressBook.deliveryCharge > 0 ? addressBook.deliveryCharge : 0;
        } else {
          this.order.delivery_charge = 0.00;
        }
        this.order.flat_no = addressBook.flat_no;
        this.order.destination_latitude = addressBook.latitude;
        this.order.destination_longitude = addressBook.longitude;
      } else {
        this.deliveryAddressId = '';
        this.order.address_id = '';
        this.order.address = '';
        this.order.flat_no = '';
        this.order.destination_latitude = '';
        this.order.destination_longitude = '';
        this.order.delivery_charge = 0;
      }
    } else {
      this.deliveryAddressId = '';
      this.order.address_id = '';
      this.order.address = '';
      this.order.flat_no = '';
      this.order.destination_latitude = '';
      this.order.destination_longitude = '';
      this.order.delivery_charge = 0;
    }
    this.order.service_charge = this.order.service_charge > 0 ? this.order.service_charge : 0;
    this.order.charity_amount = this.order.charity_amount > 0 ? this.order.charity_amount : 0;
    this.order.driver_tip = this.order.driver_tip > 0 ? this.order.driver_tip : 0;
    this.order.voucher_amount = this.order.voucher_amount > 0 ? this.order.voucher_amount : 0;
    this.order.offer_amount = this.order.offer_amount > 0 ? this.order.offer_amount : 0;
    this.order.wallet_amount = this.order.wallet_amount > 0 ? this.order.wallet_amount : 0;
    this.order.reward_offer = this.order.reward_offer > 0 ? this.order.reward_offer : 0;

    let subtotal = 0;
    this.carts.forEach(item => {
      subtotal = subtotal + item.total_price;
    });
    this.order.order_sub_total = subtotal;
    grandTotal = this.order.order_sub_total + this.order.delivery_charge + this.surchargeAmount + this.order.service_charge + this.order.driver_tip - this.order.voucher_amount - this.order.offer_amount + this.order.charity_amount - this.order.reward_offer;
    grandTotal = this.precise_round(grandTotal, 2);
    this.order.order_grand_total = grandTotal - this.order.charity_amount;

    if (this.order.payment_wallet) {
      if (this.user.wallet_amount <= grandTotal) {
        this.order.wallet_amount = this.user.wallet_amount;
      } else {
        this.order.wallet_amount = grandTotal;
      }
    }
    grandTotal = grandTotal - this.order.wallet_amount;
    if (grandTotal <= 0) {
      grandTotal = 0;
    }
    return grandTotal;
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  convertNumber(event) {
    var val = parseFloat(event);
    var val1 = (val).toFixed(2);
    // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.modalService.dismissAll();
    this.subs.unsubscribe();
  }
}

class NewAddon {
  id: string;
  price: number;
}
