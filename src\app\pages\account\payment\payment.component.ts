import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { NotificationService } from '../../../core/services/notification.service';
declare var Stripe;
import { environment } from '../../../../environments/environment';
import { User } from '../../../core/models/user';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { Restaurant } from '../../../core/models/restaurant';
import { StripeCustomer } from '../../../core/models/stripe-customer';
import { StripeCustomerService } from '../../../core/services/stripe-customer.service';
import { SiteSettingService } from '../../../core/services/site-setting.service';
import { SiteSetting } from '../../../core/models/site-setting';

@Component({
  selector: 'app-payment',
  host: { ngSkipHydration: 'true' },
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss'],
})
export class PaymentComponent implements OnInit, OnDestroy {
  @ViewChild('cardInfo') cardInfo: ElementRef;

  subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  siteSetting: SiteSetting = new SiteSetting();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  addStripeCustomer: StripeCustomer = new StripeCustomer();
  modalOptions: NgbModalOptions;
  previousPage: any;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;

  addAmount: number = 0;
  cardId: string;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    public userService: UserService,
    private siteSettingService: SiteSettingService,
    private stripeCustomerService: StripeCustomerService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user?.id) {
      this.router.navigateByUrl('/');
    }
    this.fetchSiteSetting();
    this.fetchCards();
  }

  fetchSiteSetting() {

    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
        if (this.siteSetting.stripe_mode == 'Test') {
          var publishKey = this.siteSetting.stripe_publishkeyTest
        } else {
          var publishKey = this.siteSetting.stripe_publishkey
        }
        this.stripe = Stripe(publishKey);
      }, err => this.error = err)
    );
  }

  fetchCards() {
    this.isLoading = true;

    this.subs.add(
      this.stripeCustomerService.get({ customer_id: this.user.id, nopaginate: "1" })//service_type: 'normal',
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
          },
          (err) => {
            this.stripeCustomers = [];
          }
        )
    );
  }

  addCard(model) {
    this.isModelLoading = true;
    this.openModal(model);
  }

  initCard() {
    // this.cardElement = <HTMLInputElement>(
    //   document.getElementById('customCardElement')
    // );
    var elements = this.stripe.elements();

    var style = {
      base: {
        'height': '52px',
        'fontFamily': 'Visby CF',
        'fontWeight': '700',
        'borderRadius': '10px',
        'lineHeight': '1.5',
        'fontSize': '16px',
        'color': '#000',
      }
    };

    // Card number
    this.card = elements.create('cardNumber', {
      'placeholder': 'Enter card number',
      'style': style
    });
    this.card.mount('#card-number');

    // CVC
    var cvc = elements.create('cardCvc', {
      'placeholder': 'CVC',
      'style': style
    });
    cvc.mount('#card-cvc');

    // Card expiry
    var exp = elements.create('cardExpiry', {
      'placeholder': 'Expiry date',
      'style': style
    });
    exp.mount('#card-exp');

    // Postal Code
    var postalCode = elements.create('postalCode', {
      'placeholder': 'Zip Code',
      'style': style
    });
    postalCode.mount('#postalCode');

    // this.card = elements.create('card', { hidePostalCode: true });
    // this.card.mount(this.cardInfo);
    this.isModelLoading = false;
  }

  async handleForm(e) {
    e.preventDefault();
    this.isModelLoading = true;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        // this.createPaymentIntent(result.paymentMethod.id);
        if (!result.error) {
          this.stripeCustomer.customer_id = this.user.id;
          this.stripeCustomer.customer_name = this.user.first_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = 'connect';
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isModelLoading = false))
              .subscribe(
                (res) => {
                  this.fetchCards();
                  this.notificationService.showSuccess("Card added successfully !!", "Gogrubz")
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.Modelerror = err;
                }
              )
          )
        } else {
          this.isModelLoading = false
          this.Modelerror = 'Something went wrong!';//result.error.message;
        }
      });
  }

  deleteCard(stripeCustomer: StripeCustomer) {
    this.isLoading = true; this.error = null;

    this.subs.add(this.stripeCustomerService.delete(stripeCustomer.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.fetchCards();
        this.notificationService.showSuccess("Card deleted successfully !!", "Gogrubz")
      }, err => { this.error = err; })
    );
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
    this.initCard()
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  getLastDigit(expYear) {
    return expYear.toString().slice(-2);
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); this.modalService.dismissAll(); }
}
