<div class="inner-page">
    <section class="popular-cuisines-section" (paste)="(false)" (copy)="(false)" *ngIf="!isLoading">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="main-heading">
                        <h5>
                            Popular Cuisines
                        </h5>
                    </div>
                </div>
            </div>

            <div class="popular-slider" *ngIf="cuisines?.length > 0">
                <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfig">
                    <div ngxSlickItem class="popular-slider-item slide" *ngFor="let cuisine of cuisines">
                        <div class="popular-item" (click)="filterCuisines(cuisine)">
                            <!-- <div class="popular-item-bg-two"
                                [style.background-color]="cuisine.selected ? 'black' : cuisine.second_bg"></div>
                            <div class="popular-item-bg-one"
                                [style.background-color]="cuisine.selected ? '#282828' : cuisine.first_bg"></div> -->
                            <div class="popular-item-bg-two" [style.background-color]="cuisine.second_bg"></div>
                            <div class="popular-item-bg-one" [style.background-color]="cuisine.first_bg"></div>
                            <div class="popular-item-name">
                                <img [src]="cuisine.image_url" onerror="this.src='./assets/images/burger.png';"
                                    [alt]="cuisine.cuisine_name">
                                <p>{{ cuisine.cuisine_name | titlecase }} <img class="check-arrow"
                                        src="assets/images/right.svg" *ngIf="cuisine.selected"
                                        alt="Go-Grubz-Right-Image" loading="lazy"></p>
                            </div>
                        </div>
                    </div>
                </ngx-slick-carousel>
            </div>
        </div>
    </section>

    <section id="filter-section" class="filter-section-bg">
        <div class="filter-section" (paste)="(false)" (copy)="(false)">
            <div class="container">
                <div class="filter-slide" *ngIf="!isLoading">
                    <button class="filter-slide-button d-lg-none" (click)="closeFilter()">
                        <svg class="fa-solid fa-chevron-left"></svg>
                    </button>

                    <ul class="filter-list">
                        <li>
                            <button data-bs-toggle="modal" data-bs-target="#FilterModal">
                                <img class="black-icon" src="assets/images/filters.svg" alt="Go-Grubz-Filter-Image"
                                    loading="lazy">
                                <img class="white-icon" src="assets/images/white-filters.svg"
                                    alt="Go-Grubz-filter-image" loading="lazy">
                                Filters
                                <svg class="fa-solid fa-chevron-down"></svg>
                            </button>
                        </li>
                        <li ngbDropdown class="filter-box">
                            <button ngbDropdownToggle class="dropdown" [ngClass]="{'active' : filters.sortBy}">
                                <img class="black-icon" src="assets/images/filter.svg" alt="Go-Grubz-Filter-Image"
                                    loading="lazy">
                                <img class="white-icon" src="assets/images/white-filter.svg" alt="Go-Grubz-filter-image"
                                    loading="lazy">
                                Sort By
                                <svg class="fa-solid fa-chevron-down"></svg>
                            </button>
                            <div ngbDropdownMenu class="dropdown-menu">
                                <ul>
                                    <li ngbDropdownItem>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio1">
                                                <input type="radio" class="form-check-input" id="radio1" name="optradio"
                                                    (ngModelChange)="restaurantFilter('new')"
                                                    [(ngModel)]="filters.sortBy" value="new">Most popular
                                            </label>
                                        </div>
                                    </li>
                                    <li ngbDropdownItem>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio2">
                                                <input type="radio" class="form-check-input" id="radio2" name="optradio"
                                                    (ngModelChange)="restaurantFilter('distance')"
                                                    [(ngModel)]="filters.sortBy" value="distance">Distance
                                            </label>
                                        </div>
                                    </li>
                                    <li ngbDropdownItem>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio3">
                                                <input type="radio" class="form-check-input" id="radio3" name="optradio"
                                                    (ngModelChange)="restaurantFilter('average_rating')"
                                                    [(ngModel)]="filters.sortBy" value="average_rating">Highest Rating
                                            </label>
                                        </div>
                                    </li>
                                    <li ngbDropdownItem>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio4">
                                                <input type="radio" class="form-check-input" id="radio4" name="optradio"
                                                    (ngModelChange)="restaurantFilter('minimum_order')"
                                                    [(ngModel)]="filters.sortBy" value="minimum_order">Minimum Order
                                            </label>
                                        </div>
                                    </li>
                                    <li ngbDropdownItem>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio5">
                                                <input type="radio" class="form-check-input" id="radio5" name="optradio"
                                                    (ngModelChange)="restaurantFilter('estimate_time')"
                                                    [(ngModel)]="filters.sortBy" value="estimate_time">Delivery Time
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li>
                            <button (click)="filters.fastDelivery=!filters.fastDelivery;restaurantFilter();"
                                [ngClass]="{'active' : filters.fastDelivery}">
                                <img class="black-icon" src="assets/images/fast-delivery.svg"
                                    alt="Go-Grubz-Delivery-Image" loading="lazy">
                                <img class="white-icon" src="assets/images/white-fast-delivery.svg"
                                    alt="Go-Grubz-delivery-image" loading="lazy">
                                Fast Delivery
                            </button>
                        </li>
                        <li ngbDropdown class="dietary-dropdown" *ngIf="dietaries?.length > 0">
                            <button ngbDropdownToggle>
                                <img class="black-icon" src="assets/images/dietary.svg" alt="Go-Grubz-Dietary-Image"
                                    loading="lazy">
                                <img class="white-icon" src="assets/images/white-dietary.svg"
                                    alt="Go-Grubz-dietary-image" loading="lazy">
                                Dietary
                                <svg class="fa-solid fa-chevron-down"></svg>
                            </button>
                            <div ngbDropdownMenu class="dropdown-menu">
                                <ul>
                                    <li ngbDropdownItem class="active" *ngFor="let dietary of dietaries">
                                        <button (click)="filters.dietary=dietary.id;restaurantFilter();">
                                            {{ dietary.name }}
                                            <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                *ngIf="filters.dietary == dietary.id" loading="lazy">
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li ngbDropdown class="dietary-dropdown">
                            <button ngbDropdownToggle
                                [ngClass]="{'active' : filters.rating.threestar || filters.rating.fourstar || filters.rating.fivestar}">
                                <img class="black-icon" src="assets/images/rating.svg" alt="Go-Grubz-rating-image"
                                    loading="lazy">
                                <img class="white-icon" src="assets/images/white-rating.svg" alt="Go-Grubz-rating-image"
                                    loading="lazy">
                                <span *ngIf="filters.rating.threestar">3+ </span>
                                <span *ngIf="filters.rating.fourstar">4+ </span>
                                <span *ngIf="filters.rating.fivestar">5 </span>
                                Rating
                                <svg class="fa-solid fa-chevron-down"></svg>
                            </button>
                            <div ngbDropdownMenu class="dropdown-menu">
                                <ul>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.rating.fivestar}">
                                        <button class="d-flex"
                                            (click)="filters.rating.fivestar=!filters.rating.fivestar;restaurantFilter();">
                                            <span class="d-flex align-items-center">
                                                5
                                                <ul class="star-icons">
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                </ul>
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">
                                            </span>
                                        </button>
                                    </li>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.rating.fourstar}">
                                        <button class="d-flex"
                                            (click)="filters.rating.fourstar=!filters.rating.fourstar;restaurantFilter();">
                                            <span class="d-flex align-items-center">
                                                4+
                                                <ul class="star-icons">
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                </ul>
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">
                                            </span>
                                        </button>
                                    </li>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.rating.threestar}">
                                        <button class="d-flex"
                                            (click)="filters.rating.threestar=!filters.rating.threestar;restaurantFilter();">
                                            <span class="d-flex align-items-center">
                                                3+
                                                <ul class="star-icons">
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                    <li>
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </li>
                                                </ul>
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">
                                            </span>
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li>
                            <button (click)="filters.offer=!filters.offer;restaurantFilter();"
                                [ngClass]="{'active' : filters.offer}">
                                <img class="black-icon" src="assets/images/offers.svg" alt="Go-Grubz-offers-image"
                                    loading="lazy">
                                <img class="white-icon" src="assets/images/white-offers.svg" alt="Go-Grubz-offers-image"
                                    loading="lazy">
                                Offers
                            </button>
                        </li>
                        <li ngbDropdown class="dietary-dropdown">
                            <button ngbDropdownToggle
                                [ngClass]="{'active' : filters.deliveryFee.one || filters.deliveryFee.two || filters.deliveryFee.five || filters.deliveryFee.ten}">
                                <svg class="fa-solid fa-sterling-sign"></svg>
                                Delivery Fee
                                <svg class="fa-solid fa-chevron-down"></svg>
                            </button>
                            <div ngbDropdownMenu class="dropdown-menu">
                                <ul>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.one}">
                                        <button
                                            (click)="filters.deliveryFee.one=!filters.deliveryFee.one;restaurantFilter();">
                                            Less than
                                            <span>                                                
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">
                                                <span>£1</span>
                                            </span>
                                        </button>
                                    </li>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.two}">
                                        <button
                                            (click)="filters.deliveryFee.two=!filters.deliveryFee.two;restaurantFilter();">
                                            Less than
                                            <span>                                                
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">                                            
                                                <span>£2</span>
                                            </span>
                                        </button>
                                    </li>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.five}">
                                        <button
                                            (click)="filters.deliveryFee.five=!filters.deliveryFee.five;restaurantFilter();">
                                            Less than
                                            <span>
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                loading="lazy">
                                                <span>£5</span>                                                
                                            </span>
                                        </button>
                                    </li>
                                    <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.ten}">
                                        <button
                                            (click)="filters.deliveryFee.ten=!filters.deliveryFee.ten;restaurantFilter();">
                                            Less than
                                            <span>
                                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                    loading="lazy">
                                                <span>£10</span>                                                
                                            </span>
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
                <!--<div class="filter-slide-close-bg" (click)="closeFilter()"></div>-->
                <div class="filter-mobile-view">
                    <div class="search-for-restaurant">
                        <svg class="fa-solid fa-magnifying-glass"></svg>
                        <input class="form-control" type="text" [(ngModel)]="searchData"
                            (keyup)='search($event.target.value)' placeholder="Search for a restaurant or cuisine">
                        <span class="close-icon" (click)="clearSearch()" *ngIf="searchData">
                            <svg class="fa-regular fa-xmark-circle search-close"></svg>
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="location-box d-lg-none">
                            <ul>
                                <li><svg class="fa-solid fa-location-dot"></svg></li>
                                <li>{{ restaurant.zipcode }}</li>
                                <li class="dot"><svg class="fa-solid fa-circle"></svg></li>
                                <li><button (click)="serchVisibility();">Change</button></li>
                            </ul>
                            <div class="location-search-box" *ngIf="searchVisible">
                                <form nz-form #fetchForm="ngForm" (ngSubmit)="restaurantFetch(fetchForm)">
                                    <div class="search-for-restaurant">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback
                                                nzErrorTip="Please enter postcode or delivery address!">
                                                <nz-input-group>
                                                    <svg class="fa-solid fa-magnifying-glass"></svg>
                                                    <input class="form-control" nz-input name="zipcode" id="zipcode"
                                                        [(ngModel)]="searchRestaurant.zipcode" required
                                                        (input)="formatPostcode($event)"
                                                        placeholder="Search for location" type="text">
                                                    <!-- (keydown.space)="onSpaceKeyDown($event)" -->
                                                    <button class="btn" nz-button>
                                                        <i class="spinner-border" *ngIf="isLoading"></i>
                                                        <svg class="fa-regular fa-paper-plane"></svg>
                                                    </button>
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>

                                    <nz-form-item *ngIf="error">
                                        <span class="text-danger">{{ error }}</span>
                                    </nz-form-item>
                                </form>
                            </div>
                            <div class="location-search-box-close-bg" (click)="serchVisibility()"></div>
                        </div>
                        <div class="filter-btn">
                            <button class="filter-slide-button" (click)="showFilter()">
                                Filter <img src="assets/images/filter.png" alt="Go-Grubz-Filter-Image">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="filter-slide d-lg-none" *ngIf="!isLoading">
        <button class="filter-slide-button d-lg-none" (click)="closeFilter()">
            <svg class="fa-solid fa-chevron-left"></svg>
        </button>

        <ul class="filter-list">
            <li>
                <button data-bs-toggle="modal" data-bs-target="#FilterModal">
                    <img class="black-icon" src="assets/images/filters.svg" alt="Go-Grubz-Filter-Image" loading="lazy">
                    <img class="white-icon" src="assets/images/white-filters.svg" alt="Go-Grubz-filter-image"
                        loading="lazy">
                    Filters
                    <svg class="fa-solid fa-chevron-down"></svg>
                </button>
            </li>
            <li ngbDropdown class="filter-box">
                <button ngbDropdownToggle class="dropdown" [ngClass]="{'active' : filters.sortBy}">
                    <img class="black-icon" src="assets/images/filter.svg" alt="Go-Grubz-Filter-Image" loading="lazy">
                    <img class="white-icon" src="assets/images/white-filter.svg" alt="Go-Grubz-filter-image"
                        loading="lazy">
                    Sort By
                    <svg class="fa-solid fa-chevron-down"></svg>
                </button>
                <div ngbDropdownMenu class="dropdown-menu">
                    <ul>
                        <li ngbDropdownItem>
                            <div class="form-check">
                                <label class="form-check-label" for="radio1">
                                    <input type="radio" class="form-check-input" id="radio1" name="optradio"
                                        (ngModelChange)="restaurantFilter('new')" [(ngModel)]="filters.sortBy"
                                        value="new">Most popular
                                </label>
                            </div>
                        </li>
                        <li ngbDropdownItem>
                            <div class="form-check">
                                <label class="form-check-label" for="radio2">
                                    <input type="radio" class="form-check-input" id="radio2" name="optradio"
                                        (ngModelChange)="restaurantFilter('distance')" [(ngModel)]="filters.sortBy"
                                        value="distance">Distance
                                </label>
                            </div>
                        </li>
                        <li ngbDropdownItem>
                            <div class="form-check">
                                <label class="form-check-label" for="radio3">
                                    <input type="radio" class="form-check-input" id="radio3" name="optradio"
                                        (ngModelChange)="restaurantFilter('average_rating')"
                                        [(ngModel)]="filters.sortBy" value="average_rating">Highest Rating
                                </label>
                            </div>
                        </li>
                        <li ngbDropdownItem>
                            <div class="form-check">
                                <label class="form-check-label" for="radio4">
                                    <input type="radio" class="form-check-input" id="radio4" name="optradio"
                                        (ngModelChange)="restaurantFilter('minimum_order')" [(ngModel)]="filters.sortBy"
                                        value="minimum_order">Minimum Order
                                </label>
                            </div>
                        </li>
                        <li ngbDropdownItem>
                            <div class="form-check">
                                <label class="form-check-label" for="radio5">
                                    <input type="radio" class="form-check-input" id="radio5" name="optradio"
                                        (ngModelChange)="restaurantFilter('estimate_time')" [(ngModel)]="filters.sortBy"
                                        value="estimate_time">Delivery Time
                                </label>
                            </div>
                        </li>
                    </ul>
                </div>
            </li>
            <li>
                <button (click)="filters.fastDelivery=!filters.fastDelivery;restaurantFilter();"
                    [ngClass]="{'active' : filters.fastDelivery}">
                    <img class="black-icon" src="assets/images/fast-delivery.svg" alt="Go-Grubz-Delivery-Image"
                        loading="lazy">
                    <img class="white-icon" src="assets/images/white-fast-delivery.svg" alt="Go-Grubz-delivery-image"
                        loading="lazy">
                    Fast Delivery
                </button>
            </li>
            <li ngbDropdown class="dietary-dropdown">
                <button ngbDropdownToggle>
                    <img class="black-icon" src="assets/images/dietary.svg" alt="Go-Grubz-Dietary-Image" loading="lazy">
                    <img class="white-icon" src="assets/images/white-dietary.svg" alt="Go-Grubz-dietary-image"
                        loading="lazy">
                    Dietary
                    <svg class="fa-solid fa-chevron-down"></svg>
                </button>
                <div ngbDropdownMenu class="dropdown-menu">
                    <ul>
                        <li ngbDropdownItem class="active">
                            <button>
                                <!-- <a> -->
                                Halal
                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                <!-- </a> -->
                            </button>
                        </li>
                        <li ngbDropdownItem class="active">
                            <button>
                                <!-- <a> -->
                                Vegetarian
                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                <!-- </a> -->
                            </button>
                        </li>
                        <li ngbDropdownItem>
                            <button>
                                <!-- <a> -->
                                Vegan
                                <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                <!-- </a> -->
                            </button>
                        </li>
                    </ul>
                </div>
            </li>
            <li ngbDropdown class="dietary-dropdown">
                <button ngbDropdownToggle
                    [ngClass]="{'active' : filters.rating.threestar || filters.rating.fourstar || filters.rating.fivestar}">
                    <img class="black-icon" src="assets/images/rating.svg" alt="Go-Grubz-rating-image" loading="lazy">
                    <img class="white-icon" src="assets/images/white-rating.svg" alt="Go-Grubz-rating-image"
                        loading="lazy">
                    <span *ngIf="filters.rating.threestar">3+ </span>
                    <span *ngIf="filters.rating.fourstar">4+ </span>
                    <span *ngIf="filters.rating.fivestar">5 </span>
                    Rating
                    <svg class="fa-solid fa-chevron-down"></svg>
                </button>
                <div ngbDropdownMenu class="dropdown-menu">
                    <ul>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.rating.fivestar}">
                            <button class="d-flex align-items-center"
                                (click)="filters.rating.fivestar=!filters.rating.fivestar;restaurantFilter();">
                                <span class="d-flex align-items-center">
                                    5
                                    <ul class="star-icons">
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                    </ul>
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>
                            </button>
                        </li>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.rating.fourstar}">
                            <button class="d-flex align-items-center"
                                (click)="filters.rating.fourstar=!filters.rating.fourstar;restaurantFilter();">
                                <span class="d-flex align-items-center">
                                    4+
                                    <ul class="star-icons">
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                    </ul>
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>
                            </button>
                        </li>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.rating.threestar}">
                            <button class="d-flex align-items-center"
                                (click)="filters.rating.threestar=!filters.rating.threestar;restaurantFilter();">
                                <span class="d-flex align-items-center">
                                    3+
                                    <ul class="star-icons">
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                        <li>
                                            <svg class="fa-solid fa-star"></svg>
                                        </li>
                                    </ul>
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>                                    
                            </button>
                        </li>
                    </ul>
                </div>
            </li>
            <li>
                <button (click)="filters.offer=!filters.offer;restaurantFilter();" [ngClass]="{'active' : filters.offer}">
                    <img class="black-icon" src="assets/images/offers.svg" alt="Go-Grubz-offers-image" loading="lazy">
                    <img class="white-icon" src="assets/images/white-offers.svg" alt="Go-Grubz-offers-image"
                        loading="lazy">
                    Offers
                </button>
            </li>
            <li ngbDropdown class="dietary-dropdown">
                <button ngbDropdownToggle
                    [ngClass]="{'active' : filters.deliveryFee.one || filters.deliveryFee.two || filters.deliveryFee.five || filters.deliveryFee.ten}">
                    <svg class="fa-solid fa-sterling-sign"></svg>
                    Delivery Fee
                    <svg class="fa-solid fa-chevron-down"></svg>
                </button>
                <div ngbDropdownMenu class="dropdown-menu">
                    <ul>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.one}">
                            <button (click)="filters.deliveryFee.one=!filters.deliveryFee.one;restaurantFilter();">
                                Less than                                
                                <span>
                                    £1
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>
                            </button>
                        </li>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.two}">
                            <button (click)="filters.deliveryFee.two=!filters.deliveryFee.two;restaurantFilter();">
                                Less than                                
                                <span>
                                    £2
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>
                            </button>
                        </li>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.five}">
                            <button (click)="filters.deliveryFee.five=!filters.deliveryFee.five;restaurantFilter();">
                                Less than                                
                                <span>
                                    £5
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>                                
                            </button>
                        </li>
                        <li ngbDropdownItem [ngClass]="{'active' : filters.deliveryFee.ten}">
                            <button (click)="filters.deliveryFee.ten=!filters.deliveryFee.ten;restaurantFilter();">
                                Less than                                
                                <span>
                                    £10
                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image" loading="lazy">
                                </span>                                
                            </button>
                        </li>
                    </ul>
                </div>
            </li>
        </ul>
    </div>
    <div class="filter-slide-close-bg d-lg-none" (click)="closeFilter()"></div>
    <div class="clearfix"></div>

    <div class="loader-height d-flex align-items-center justify-content-center" *ngIf="isLoading">
        <div class="grubz-loader main-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="most-popular-section" (paste)="(false)" (copy)="(false)" *ngIf="!isLoading">
        <div class="container">
            <div class="row"
                *ngIf="popularRestaurants?.length > 0 && !isLoading && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">
                <div class="col-12">
                    <div class="main-heading product-list-heading">
                        <h5>
                            Most Popular
                        </h5>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>

            <div class="most-popular-slider"
                *ngIf="popularRestaurants?.length > 0 && !isLoading  && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">
                <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfigPopular">
                    <div ngxSlickItem class="most-popular-item slide"
                        *ngFor="let popularRestaurant of popularRestaurants">
                        <div class="product-box">
                            <div class="product-image">
                                <div class="product-logo" (click)="redirectRestaurant(popularRestaurant)">
                                    <img [src]="popularRestaurant.image_url" [alt]="popularRestaurant.restaurant_name"
                                        onerror="this.src='./assets/favicon.png';">
                                </div>
                                <div class="favourite-icon" *ngIf="loginUser?.id">
                                    <button class="unlike-btn" *ngIf="!checkFav(popularRestaurant?.id)">
                                        <img (click)="favourite(true,popularRestaurant)"
                                            src="./assets/images/unlike.svg" alt="Go-Grubz-unlike-image" loading="lazy">
                                    </button>
                                    <button class="like-btn" *ngIf="checkFav(popularRestaurant?.id)">
                                        <img (click)="favourite(false,popularRestaurant)" src="./assets/images/like.svg"
                                            alt="Go-Grubz-unlike-image" loading="lazy">
                                    </button>
                                </div>
                                <img class="product-large-image" (click)="redirectRestaurant(popularRestaurant)"
                                    [src]="popularRestaurant.promotion" [alt]="popularRestaurant.restaurant_name"
                                    onerror="this.src='./assets/images/product-3.png';">
                                <div class="restaurant-not-open-status-bg"
                                    *ngIf="popularRestaurant?.currentStatus == 'Closed'"
                                    (click)="redirectRestaurant(popularRestaurant)">
                                    <h6
                                        *ngIf="popularRestaurant?.currentStatus == 'Closed' && (popularRestaurant?.restaurant_delivery == 'Yes' || popularRestaurant?.restaurant_pickup == 'Yes') && popularRestaurant?.online_order == 'Yes'">
                                        Closed</h6>
                                    <h6
                                        *ngIf="popularRestaurant?.currentStatus == 'PreOrder' && (popularRestaurant?.restaurant_delivery == 'Yes' || popularRestaurant?.restaurant_pickup == 'Yes') && popularRestaurant?.online_order == 'Yes'">
                                        PreOrder</h6>
                                    <h6
                                        *ngIf="(popularRestaurant?.restaurant_delivery == 'No' && popularRestaurant?.restaurant_pickup == 'No') || popularRestaurant?.online_order == 'No'">
                                        Currently Unavailable</h6>
                                    <button class="btn cursor" *ngIf="popularRestaurant.currentStatus == 'Closed'">
                                        Schedule Order
                                    </button>
                                </div>
                            </div>
                            <div class="product-content">
                                <h5>{{popularRestaurant.restaurant_name}}</h5>
                                <ul class="rating-list">
                                    <li>
                                        {{popularRestaurant.average_rating > 0 ? (popularRestaurant.average_rating |
                                        number
                                        : '1.2-2') :
                                        0.00
                                        }}
                                        <img src="assets/images/star.svg" alt="Go-Grubz-star-image" loading="lazy">
                                        <span>
                                            ({{popularRestaurant.total_reviews > 0 ?
                                            popularRestaurant.total_reviews:'0'}})</span>
                                    </li>
                                    <li>
                                        {{popularRestaurant.distance | number : '1.2-2'}} miles
                                    </li>
                                    <li>
                                        {{popularRestaurant.estimate_time}} mins
                                        <!-- -{{popularRestaurant.pickup_estimate_time}} -->
                                    </li>
                                </ul>
                                <ul class="times-list">
                                    <li>
                                        £{{popularRestaurant.minimum_order | number : '1.2-2'}} Min
                                    </li>
                                    <li>
                                        £{{ popularRestaurant.deliveryCharges }} Delivery
                                    </li>
                                </ul>
                                <div class="discount-offer" *ngIf="popularRestaurant?.offer_texts?.length > 0">
                                    <img src="assets/images/discount-icon.svg" alt="Go-Grubz-Discount-Image"
                                        loading="lazy">
                                    {{popularRestaurant?.offer_texts[0]?.offer_name}}
                                </div>
                            </div>
                        </div>
                    </div>
                </ngx-slick-carousel>
            </div>

            <div class="row"
                *ngIf="offerRestaurants?.length > 0 && !isLoading  && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">
                <div class="col-12">
                    <div class="main-heading product-list-heading">
                        <h5>
                            Offers For You
                        </h5>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="offer-for-you-slider"
                *ngIf="offerRestaurants?.length > 0 && !isLoading  && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">
                <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfigPopular">
                    <div ngxSlickItem class="most-popular-item slide" *ngFor="let offerRestaurant of offerRestaurants">
                        <div class="product-box">
                            <div class="product-image">
                                <div class="product-logo" (click)="redirectRestaurant(offerRestaurant)">
                                    <img [src]="offerRestaurant.image_url" [alt]="offerRestaurant.restaurant_name"
                                        onerror="this.src='./assets/favicon.png';">
                                </div>
                                <div class="favourite-icon" *ngIf="loginUser?.id">
                                    <button class="unlike-btn" *ngIf="!checkFav(offerRestaurant?.id)">
                                        <img (click)="favourite(true,offerRestaurant)" src="./assets/images/unlike.svg"
                                            alt="Go-Grubz-unlike-image" loading="lazy">
                                    </button>
                                    <button class="like-btn" *ngIf="checkFav(offerRestaurant?.id)">
                                        <img (click)="favourite(false,offerRestaurant)" src="./assets/images/like.svg"
                                            alt="Go-Grubz-unlike-image" loading="lazy">
                                    </button>
                                </div>
                                <img class="product-large-image" (click)="redirectRestaurant(offerRestaurant)"
                                    [src]="offerRestaurant.promotion" [alt]="offerRestaurant.restaurant_name"
                                    onerror="this.src='./assets/images/product-3.png';">
                                <div class="restaurant-not-open-status-bg"
                                    *ngIf="offerRestaurant?.currentStatus == 'Closed'"
                                    (click)="redirectRestaurant(offerRestaurant)">
                                    <h6
                                        *ngIf="offerRestaurant?.currentStatus == 'Closed' && (offerRestaurant?.restaurant_delivery == 'Yes' || offerRestaurant?.restaurant_pickup == 'Yes') && offerRestaurant?.online_order == 'Yes'">
                                        Closed</h6>
                                    <h6
                                        *ngIf="offerRestaurant?.currentStatus == 'PreOrder' && (offerRestaurant?.restaurant_delivery == 'Yes' || offerRestaurant?.restaurant_pickup == 'Yes') && offerRestaurant?.online_order == 'Yes'">
                                        PreOrder</h6>
                                    <h6
                                        *ngIf="(offerRestaurant?.restaurant_delivery == 'No' && offerRestaurant?.restaurant_pickup == 'No') || offerRestaurant?.online_order == 'No'">
                                        Currently Unavailable</h6>
                                    <button class="btn cursor" *ngIf="offerRestaurant.currentStatus == 'Closed'">
                                        Schedule Order
                                    </button>
                                </div>
                            </div>
                            <div class="product-content">
                                <h5>{{offerRestaurant.restaurant_name}}</h5>
                                <ul class="rating-list">
                                    <li>
                                        {{offerRestaurant.average_rating > 0 ? (offerRestaurant.average_rating | number
                                        : '1.2-2') :
                                        0.00
                                        }}
                                        <img src="assets/images/star.svg" alt="Go-Grubz-star-image" loading="lazy">
                                        <span>
                                            ({{offerRestaurant.total_reviews > 0 ?
                                            offerRestaurant.total_reviews:'0'}})</span>
                                    </li>
                                    <li>
                                        {{offerRestaurant.distance | number : '1.2-2'}} miles
                                    </li>
                                    <li>
                                        {{offerRestaurant.estimate_time}} mins
                                        <!-- -{{offerRestaurant.pickup_estimate_time}} -->
                                    </li>
                                </ul>
                                <ul class="times-list">
                                    <li>
                                        £{{offerRestaurant.minimum_order | number : '1.2-2'}} Min
                                    </li>
                                    <li>
                                        £{{ offerRestaurant.deliveryCharges }} Delivery
                                    </li>
                                </ul>
                                <div class="discount-offer" *ngIf="offerRestaurant?.offer_texts?.length > 0">
                                    <img src="assets/images/discount-icon.svg" alt="Go-Grubz-Discount-Image"
                                        loading="lazy">
                                    {{offerRestaurant?.offer_texts[0]?.offer_name}}
                                </div>
                            </div>
                        </div>
                    </div>
                </ngx-slick-carousel>
            </div>

            <div class="main-heading product-list-heading" id='allStore' *ngIf="restaurants?.length > 0 && !isLoading">
                <h5>
                    All Stores
                </h5>
            </div>

            <div class="row" *ngIf="restaurants?.length <= 0 && !isLoading">
                <div class="dont-have-order inner-page text-center mt-3">
                    <p>No restaurant found.</p>
                </div>
            </div>

            <div class="row" *ngIf="restaurants?.length > 0 && !isLoading">

                <div class="col-sm-6 col-lg-4 col-xl-3" *ngFor="let restaurant of restaurants">
                    <div class="product-box cursor">
                        <div class="product-image">
                            <div class="product-logo" (click)="redirectRestaurant(restaurant)">
                                <img [src]="restaurant.image_url" [alt]="restaurant.restaurant_name"
                                    onerror="this.src='./assets/favicon.png';">
                            </div>
                            <div class="favourite-icon" *ngIf="loginUser?.id">
                                <button class="unlike-btn" *ngIf="!checkFav(restaurant?.id)">
                                    <img (click)="favourite(true,restaurant)" src="./assets/images/unlike.svg"
                                        alt="Go-Grubz-unlike-image" loading="lazy">
                                </button>
                                <button class="like-btn" *ngIf="checkFav(restaurant?.id)">
                                    <img (click)="favourite(false,restaurant)" src="./assets/images/like.svg"
                                        alt="Go-Grubz-unlike-image" loading="lazy">
                                </button>
                            </div>
                            <img class="product-large-image" (click)="redirectRestaurant(restaurant)"
                                [src]="restaurant.promotion" [alt]="restaurant.restaurant_name"
                                onerror="this.src='./assets/images/product-3.png';">
                            <div class="restaurant-not-open-status-bg" *ngIf="restaurant?.currentStatus == 'Closed'"
                                (click)="redirectRestaurant(restaurant)">
                                <h6
                                    *ngIf="restaurant?.currentStatus == 'Closed' && (restaurant?.restaurant_delivery == 'Yes' || restaurant?.restaurant_pickup == 'Yes') && restaurant?.online_order == 'Yes'">
                                    Closed</h6>
                                <h6
                                    *ngIf="restaurant?.currentStatus == 'PreOrder' && (restaurant?.restaurant_delivery == 'Yes' || restaurant?.restaurant_pickup == 'Yes') && restaurant?.online_order == 'Yes'">
                                    PreOrder</h6>
                                <h6
                                    *ngIf="(restaurant?.restaurant_delivery == 'No' && restaurant?.restaurant_pickup == 'No') || restaurant?.online_order == 'No'">
                                    Currently Unavailable</h6>
                                <button class="btn cursor" *ngIf="restaurant.currentStatus == 'Closed'">
                                    Schedule Order
                                </button>
                            </div>
                        </div>
                        <div class="product-content" (click)="redirectRestaurant(restaurant)">
                            <h5>{{restaurant.restaurant_name}}</h5>
                            <ul class="rating-list">
                                <li>
                                    {{restaurant.average_rating > 0 ? (restaurant.average_rating | number : '1.2-2') :
                                    0.00
                                    }}
                                    <img src="assets/images/star.svg" alt="Go-Grubz-star-image" loading="lazy"> <span>
                                        ({{restaurant.total_reviews > 0 ? restaurant.total_reviews:'0'}})</span>
                                </li>
                                <li>
                                    {{restaurant.distance | number : '1.2-2'}} miles
                                </li>
                                <li>
                                    {{restaurant.estimate_time}} mins
                                    <!-- -{{restaurant.pickup_estimate_time}} -->
                                </li>
                            </ul>
                            <ul class="times-list">
                                <li>
                                    £{{restaurant.minimum_order | number : '1.2-2'}} Min
                                </li>
                                <li>
                                    £{{ restaurant.deliveryCharges }} Delivery
                                </li>
                            </ul>
                            <div class="discount-offer" *ngIf="restaurant?.offer_texts?.length > 0">
                                <img src="assets/images/discount-icon.svg" alt="Go-Grubz-Discount-Image" loading="lazy">
                                {{restaurant?.offer_texts[0]?.offer_name}}
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="section-seprate-line"></div>

            <div class="main-heading product-list-heading"
                *ngIf="closeRestaurants?.length > 0 && !isLoading && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy  && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">
                <h5>
                    Not open currently
                </h5>
            </div>

            <div class="row"
                *ngIf="closeRestaurants?.length > 0 && !isLoading && !filters.fastDelivery && !filters.rating.threestar && !filters.rating.fourstar && !filters.rating.fivestar && !filters.deliveryFee.one && !filters.deliveryFee.two && !filters.deliveryFee.five && !filters.deliveryFee.ten && !filters.sortBy && !filters.dietary && !filters.offer && !searchData && selected_array.length <= 0">

                <div class="col-sm-6 col-lg-4 col-xl-3" *ngFor="let closeRestaurant of closeRestaurants">
                    <div class="product-box cursor">
                        <div class="product-image">
                            <div class="product-logo" (click)="redirectRestaurant(closeRestaurant)">
                                <img [src]="closeRestaurant.image_url" [alt]="closeRestaurant.restaurant_name"
                                    onerror="this.src='./assets/favicon.png';">
                            </div>
                            <div class="favourite-icon" *ngIf="loginUser?.id">
                                <button class="unlike-btn" *ngIf="!checkFav(closeRestaurant?.id)">
                                    <img (click)="favourite(true,closeRestaurant)" src="./assets/images/unlike.svg"
                                        alt="Go-Grubz-unlike-image" loading="lazy">
                                </button>
                                <button class="like-btn" *ngIf="checkFav(closeRestaurant?.id)">
                                    <img (click)="favourite(false,closeRestaurant)" src="./assets/images/like.svg"
                                        alt="Go-Grubz-unlike-image" loading="lazy">
                                </button>
                            </div>
                            <img class="product-large-image" (click)="redirectRestaurant(closeRestaurant)"
                                [src]="closeRestaurant.promotion" [alt]="closeRestaurant.restaurant_name"
                                onerror="this.src='./assets/images/product-3.png';">
                            <div class="restaurant-not-open-status-bg"
                                *ngIf="closeRestaurant?.currentStatus == 'Closed'"
                                (click)="redirectRestaurant(closeRestaurant)">
                                <h6
                                    *ngIf="closeRestaurant?.currentStatus == 'Closed' && (closeRestaurant?.restaurant_delivery == 'Yes' || closeRestaurant?.restaurant_pickup == 'Yes') && closeRestaurant?.online_order == 'Yes'">
                                    Closed</h6>
                                <h6
                                    *ngIf="closeRestaurant?.currentStatus == 'PreOrder' && (closeRestaurant?.restaurant_delivery == 'Yes' || closeRestaurant?.restaurant_pickup == 'Yes') && closeRestaurant?.online_order == 'Yes'">
                                    PreOrder</h6>
                                <h6
                                    *ngIf="(closeRestaurant?.restaurant_delivery == 'No' && closeRestaurant?.restaurant_pickup == 'No') || closeRestaurant?.online_order == 'No'">
                                    Currently Unavailable</h6>
                                <button class="btn cursor" *ngIf="closeRestaurant.currentStatus == 'Closed'">
                                    Schedule Order
                                </button>
                            </div>
                        </div>
                        <div class="product-content" (click)="redirectRestaurant(closeRestaurant)">
                            <h5>{{closeRestaurant.restaurant_name}}</h5>
                            <ul class="rating-list">
                                <li>
                                    {{closeRestaurant.average_rating > 0 ? (closeRestaurant.average_rating | number :
                                    '1.2-2') : 0.00
                                    }}
                                    <img src="assets/images/star.svg" alt="Go-Grubz-star-image" loading="lazy"> <span>
                                        ({{closeRestaurant.total_reviews > 0 ?
                                        closeRestaurant.total_reviews:'0'}})</span>
                                </li>
                                <li>
                                    {{closeRestaurant.distance | number : '1.2-2'}} miles
                                </li>
                                <li>
                                    {{closeRestaurant.estimate_time}} mins
                                    <!-- -{{closeRestaurant.pickup_estimate_time}} -->
                                </li>
                            </ul>
                            <ul class="times-list">
                                <li>
                                    £{{closeRestaurant.minimum_order | number : '1.2-2'}} Min
                                </li>
                                <li>
                                    £{{ closeRestaurant.deliveryCharges }} Delivery
                                </li>
                            </ul>
                            <div class="discount-offer" *ngIf="closeRestaurant?.offer_texts?.length > 0">
                                <img src="assets/images/discount-icon.svg" alt="Go-Grubz-Discount-Image" loading="lazy">
                                {{closeRestaurant?.offer_texts[0]?.offer_name}}
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="FilterModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal">
                    <svg class="fa-solid fa-xmark"></svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="filter-popup-list">
                    <ul class="nav nav-tabs">
                        <li>
                            <a class="active" data-bs-toggle="tab" href="#sort-by-tab">
                                Sort By
                            </a>
                        </li>
                        <li>
                            <a data-bs-toggle="tab" href="#rating-tab">
                                Ratings
                            </a>
                        </li>
                        <li>
                            <a data-bs-toggle="tab" href="#delivery-fee-tab">
                                Delivery Fee
                            </a>
                        </li>
                        <li>
                            <a data-bs-toggle="tab" href="#dietary-tab">
                                Dietary
                            </a>
                        </li>
                        <!-- <li>
                            <a data-bs-toggle="tab" href="#offers-tab">
                                Offers
                            </a>
                        </li> -->
                    </ul>
                </div>

                <div class="tab-content">
                    <div id="sort-by-tab" class="tab-pane active">
                        <div class="filter-content">
                            <div class="filter-category-list">
                                <ul>
                                    <li>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio1">
                                                <input type="radio" class="form-check-input" id="radio11"
                                                    name="optradios" (ngModelChange)="restaurantFilter('new')"
                                                    [(ngModel)]="filters.sortBy" value="new">Most popular
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio2">
                                                <input type="radio" class="form-check-input" id="radio12"
                                                    name="optradios" (ngModelChange)="restaurantFilter('distance')"
                                                    [(ngModel)]="filters.sortBy" value="distance">Distance
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio3">
                                                <input type="radio" class="form-check-input" id="radio13"
                                                    name="optradios"
                                                    (ngModelChange)="restaurantFilter('average_rating')"
                                                    [(ngModel)]="filters.sortBy" value="average_rating">Highest Rating
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio4">
                                                <input type="radio" class="form-check-input" id="radio14"
                                                    name="optradios" (ngModelChange)="restaurantFilter('minimum_order')"
                                                    [(ngModel)]="filters.sortBy" value="minimum_order">Minimum Order
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="form-check">
                                            <label class="form-check-label" for="radio5">
                                                <input type="radio" class="form-check-input" id="radio15"
                                                    name="optradios" (ngModelChange)="restaurantFilter('estimate_time')"
                                                    [(ngModel)]="filters.sortBy" value="estimate_time">Delivery Time
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div id="rating-tab" class="tab-pane">
                        <div class="filter-content">
                            <div class="filter-rating-list" (click)="filters.rating.fivestar=!filters.rating.fivestar;">
                                <ul class="star-icons">
                                    <li>
                                        <span>
                                            5
                                        </span>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                </ul>
                                <img *ngIf="filters.rating.fivestar" class="check-icon" src="assets/images/check.svg"
                                    alt="Go-Grubz-Check-Image" loading="lazy">
                            </div>
                            <div class="filter-rating-list" (click)="filters.rating.fourstar=!filters.rating.fourstar;">
                                <ul class="star-icons">
                                    <li>
                                        <span>
                                            4+
                                        </span>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                </ul>
                                <img *ngIf="filters.rating.fourstar" class="check-icon" src="assets/images/check.svg"
                                    alt="Go-Grubz-Check-Image" loading="lazy">
                            </div>
                            <div class="filter-rating-list"
                                (click)="filters.rating.threestar=!filters.rating.threestar;">
                                <ul class="star-icons">
                                    <li>
                                        <span>
                                            3+
                                        </span>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                    <li>
                                        <svg class="fa-solid fa-star"></svg>
                                    </li>
                                </ul>
                                <img *ngIf="filters.rating.threestar" class="check-icon" src="assets/images/check.svg"
                                    alt="Go-Grubz-Check-Image" loading="lazy">
                            </div>

                        </div>
                    </div>

                    <div id="delivery-fee-tab" class="tab-pane">
                        <div class="filter-content">
                            <div class="delivery-fee-list">
                                <ul>
                                    <li class="dropdown-item">
                                        <button class="cursor"
                                            (click)="filters.deliveryFee.one=!filters.deliveryFee.one;">Less than
                                            <img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" *ngIf="filters.deliveryFee.one"
                                                loading="lazy">
                                            <span>£1</span>
                                        </button>
                                    </li>
                                    <li class="dropdown-item">
                                        <button class="cursor"
                                            (click)="filters.deliveryFee.two=!filters.deliveryFee.two;">Less than
                                            <img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" *ngIf="filters.deliveryFee.two"
                                                loading="lazy">
                                            <span>£2</span>
                                        </button>
                                    </li>
                                    <li class="dropdown-item">
                                        <button class="cursor"
                                            (click)="filters.deliveryFee.five=!filters.deliveryFee.five;">Less than
                                            <img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" *ngIf="filters.deliveryFee.five"
                                                loading="lazy">
                                            <span>£5</span>
                                        </button>
                                    </li>
                                    <li class="dropdown-item">
                                        <button class="cursor"
                                            (click)="filters.deliveryFee.ten=!filters.deliveryFee.ten;">Less than
                                            <img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" *ngIf="filters.deliveryFee.ten"
                                                loading="lazy">
                                            <span>£10</span>
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div id="dietary-tab" class="tab-pane">
                        <div class="filter-content">
                            <div class="delivery-fee-list">
                                <ul>
                                    <li class="dropdown-item active">
                                        <button class="cursor">Halal<img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" loading="lazy"></button>
                                    </li>
                                    <li class="dropdown-item active">
                                        <button class="cursor">Vegetarian<img class="check-icon"
                                                src="assets/images/check.svg" alt="Go-Grubz-check-image"
                                                loading="lazy"></button>
                                    </li>
                                    <li class="dropdown-item">
                                        <button class="cursor">Vegan<img class="check-icon" src="assets/images/check.svg"
                                                alt="Go-Grubz-Check-Image" loading="lazy"></button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" (click)="clearFilter();"
                            data-bs-dismiss="modal">Clear Filters</button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Apply</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>