import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Menu } from '../models/menu';
import { environment } from '../../../environments/environment';
import { ErrorHandler } from '../../shared/error-handler';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  private url = environment.apiBaseUrl + 'menu/';
  constructor(private http: HttpClient) { }
  get(options: any = {}): Observable<any> {
    let params = new HttpParams();
    return this.http
      .get<any>(this.url, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Menu> {
    return this.http
      .get<Menu>(this.url + id, {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
