import { Component, HostListener, OnInit, ElementRef, ViewChild, Inject } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { Restaurant } from '../../core/models/restaurant';
import { NgForm } from '@angular/forms';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { environment } from '../../../environments/environment';
import { CityService } from '../../core/services/city.service';
import { City } from '../../core/models/city';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'app-home',
  host: { ngSkipHydration: 'true' },
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})

export class HomeComponent implements OnInit {
  private subs = new Subscription();

  cities: City[] = [];
  restaurants: Restaurant[] = [];
  restaurant: Restaurant = new Restaurant();
  dashboardDetails: any;

  center: google.maps.LatLngLiteral = { lat: 52.56910257184157, lng: -1.9816220306280343 };
  zoom = 12;
  markers: Marker[] = [];

  isLoading = false; error = null;
  isMapLoading = false; errorMap = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  slideConfig = {
    "slidesToShow": 3,
    "slidesToScroll": 3,
    "infinite": true,
    "draggable": false,
    // "swipeToSlide": true,
    "dots": false,
    useTransform: false,
    cssEase: 'linear',
    // "autoplay": true,
    // "arrows": false,
    // "autoplaySpeed": 1500,
    "responsive": [
      {
        breakpoint: 1500,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        }
      },
      {
        breakpoint: 1199,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          draggable: true
        }
      }
    ]
  };

  @ViewChild('.banner-bg') bannerElement!: ElementRef;

  constructor(
    private el: ElementRef,
    private restaurantService: RestaurantService,
    public userService: UserService,
    private citiesService: CityService,
    private route: ActivatedRoute,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  scrollBannerToTop() {
    // Get the top position of the banner
    const bannerTopPosition = this.document.querySelector('.banner-bg')?.getBoundingClientRect().bottom || 0;
    // Calculate the position to scroll to
    const scrollToPosition = window.scrollY + bannerTopPosition;
    // Smooth scroll to the calculated position
    window.scrollTo({ top: scrollToPosition, behavior: 'smooth' });
  }

  ngOnInit() {
    // if (this.restaurantService.zipcode) {
    //   this.router.navigateByUrl('/location/' + this.restaurantService.zipcode);
    // }
    this.findLocation();
    this.fetchDashboard();
    this.fetchRestaurants();
    this.fetchCities();
    // this.document.cookie = "myCookie=myValue; SameSite=None; Secure";
  }

  findLocation() {
    // if (navigator.geolocation) {
    //   navigator.geolocation.getCurrentPosition((position) => {
    //     let geocoder = new google.maps.Geocoder();
    //     let latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
    //     let request = { LatLng: latlng };
    //     geocoder.geocode({ location: latlng }, (results, status) => {
    //       if (status === google.maps.GeocoderStatus.OK) {
    //         let result = results[0];
    //         let rsltAdrComponent = result.address_components;
    //         let resultLength = rsltAdrComponent.length;
    //         if (result != null) {
    //           this.restaurant.zipcode = rsltAdrComponent[resultLength - 1].short_name;
    //           // this.restaurantService.saveZipCode(this.restaurant.zipcode.trim());
    //           // this.router.navigateByUrl('/location/' + this.restaurant.zipcode.trim());
    //         }
    //       }
    //     });
    //   });
    // }
  }

  onCity(name) {
    this.restaurant.city_name = name
    localStorage.setItem(environment.zipcode, name);
    this.restaurantService.zipcode = name;
    this.router.navigateByUrl('/location/' + name);
    // this.restaurantService.findCity(this.restaurant)
    //   .pipe(finalize(() => (this.isLoading = false)))
    //   .subscribe(
    //     (res) => {
    //       if (typeof localStorage !== 'undefined') {
    //         localStorage.setItem(environment.zipcode, res.post_code);
    //         this.restaurantService.zipcode = res.post_code;
    //       }
    //       this.router.navigateByUrl('/location/' + res.post_code);
    //     },
    //     (err) => {
    //       alert('location not found, please try another city.');
    //     }
    //   );
  }

  fetchRestaurants() {
    this.isMapLoading = true; this.errorMap = null;

    this.subs.add(this.restaurantService.mini({ nopaginate: 1 })
      .pipe(finalize(() => this.isMapLoading = false))
      .subscribe(res => {
        this.restaurants = res;
        this.restaurants.forEach(rest => {
          let markerObject: Marker = new Marker();
          if (rest.sourcelatitude && rest.sourcelongitude) {
            markerObject.title = rest.restaurant_name.trim();
            markerObject.label = rest.restaurant_name.charAt(0).toUpperCase();
            markerObject.position = { lat: Number(rest.sourcelatitude), lng: Number(rest.sourcelongitude) };
            this.markers.push(markerObject);
          }
        });
      }, err => { this.errorMap = err; })
    );
  }

  fetchDashboard() {
    this.subs.add(this.userService.allCount()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.dashboardDetails = res;
      }, err => this.error = err)
    );
  }

  fetchCities() {
    this.error = null;

    this.subs.add(this.citiesService.history({ nopaginate: 1 })
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.cities = res;
      }, err => { this.cities = []; this.error = err; })
    );
  }

  restaurantFetch(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.restaurantService.saveZipCode(this.restaurant.zipcode.replace(/\s/g, ""));
    this.router.navigateByUrl('/location/' + this.restaurant.zipcode.replace(/\s/g, ""));
    // this.fetchRestaurants(form);
  }

  clearPostcode() {
    this.restaurant.zipcode = '';
  }

  formatPostcode(event: any) {
    let input = event.target.value.toUpperCase().replace(/\s+/g, '');
    if (input.length > 3 && input.length <= 7) {
      input = input.slice(0, -3) + ' ' + input.slice(-3);
    }
    this.restaurant.zipcode = input;
  }

  // fetchRestaurants(form: NgForm) {
  //   this.isLoading = true; this.error = null;

  //   const loginUser = JSON.parse(this.userService.getUser());
  //   this.restaurant.user_id = loginUser?.id

  //   this.restaurantService.find(this.restaurant)
  //     .pipe(finalize(() => (this.isLoading = false)))
  //     .subscribe(
  //       (res) => {
  //         localStorage.setItem(environment.zipcode, this.restaurant.zipcode);
  //         this.router.navigateByUrl('/location/' + this.restaurant.zipcode);
  //       },
  //       (err) => {
  //         this.error = err;
  //       }
  //     );
  // }

  nFormatter(num) {
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(1).replace(/\.0$/, '') + 'G';
    }
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return num;
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); }
}

class Marker {
  position: google.maps.LatLngLiteral;
  label: string;
  title: string;
}

