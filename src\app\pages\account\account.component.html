<section class="inner-page pb-5 mb-2">
    <div class="container">

        <div class="row">
            <div class="col-xl-3 col-lg-3 col-md-4">
                <div class="account-nav-list">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="profile"
                                href="profile">
                                Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="orders"
                                href="orders">
                                Orders
                            </a>
                        </li>
                        <li class="nav-item" *ngIf="siteSetting?.wallet_available == 'Yes'">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="wallet"
                                href="wallet">
                                Wallet
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']"
                                routerLink="payment-methods" href="payment-methods">
                                Payment Methods
                            </a>
                        </li>
                        <li class="nav-item" *ngIf="reward?.reward_option == 'Yes'">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="reward-points"
                                href="#reward-points">
                                Reward Points
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="reservations"
                                href="reservations">
                                Reservations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="address-book"
                                href="address-book">
                                Address Book
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="favourites"
                                href="favourites">
                                Favourites
                            </a>
                        </li>
                        <li class="nav-item" *ngIf="referral.referral_option == 'Yes'">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']"
                                routerLink="invite-friends" href="invite-friends">
                                Invite Friends
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="preferences"
                                href="preferences">
                                Contact Preferences
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" nzMatchRouter [routerLinkActive]="['active']" routerLink="help"
                                href="help">
                                Help
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" routerLink="/add-restaurant" href="/add-restaurant">
                                Add Your Restaurant
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-xl-9 col-lg-9 col-md-8 ps-xl-0">
                <div class="tab-content">
                    <router-outlet></router-outlet>
                </div>
            </div>
        </div>

    </div>
</section>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>