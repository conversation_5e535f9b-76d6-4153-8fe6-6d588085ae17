import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AccountComponent } from './account.component';
import { ProfileComponent } from './profile/profile.component';
import { SharedModule } from '../../shared/shared.module';
import { OrdersComponent } from './orders/orders.component';
import { AddressComponent } from './address/address.component';
import { BookingsComponent } from './bookings/bookings.component';
import { PaymentComponent } from './payment/payment.component';
import { WalletComponent } from './wallet/wallet.component';
import { ReferralComponent } from './referral/referral.component';
import { RewardsComponent } from './rewards/rewards.component';
import { HelpComponent } from './help/help.component';
import { FavouriteComponent } from './favourites/favourites.component';
import { ContactPreferenceComponent } from './contact-preferences/contact-preferences.component';

const routes: Routes = [
  { path: '', component: ProfileComponent },
  { path: 'profile', component: ProfileComponent, data: { selectedTab: 0 } },
  { path: 'orders', component: OrdersComponent, data: { selectedTab: 1 } },
  { path: 'address-book', component: AddressComponent, data: { selectedTab: 2 } },
  { path: 'reservations', component: BookingsComponent, data: { selectedTab: 3 } },
  { path: 'payment-methods', component: PaymentComponent, data: { selectedTab: 4 } },
  { path: 'wallet', component: WalletComponent, data: { selectedTab: 5 } },
  { path: 'invite-friends', component: ReferralComponent, data: { selectedTab: 6 } },
  { path: 'reward-points', component: RewardsComponent, data: { selectedTab: 7 } },
  { path: 'help', component: HelpComponent, data: { selectedTab: 8 } },
  { path: 'favourites', component: FavouriteComponent, data: { selectedTab: 9 } },
  { path: 'preferences', component: ContactPreferenceComponent, data: { selectedTab: 10 } },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    SharedModule
  ],
  declarations: [
    AccountComponent,
    ProfileComponent,
    OrdersComponent,
    AddressComponent,
    BookingsComponent,
    PaymentComponent,
    WalletComponent,
    ReferralComponent,
    RewardsComponent,
    HelpComponent,
    FavouriteComponent,
    ContactPreferenceComponent,
  ],
  exports: [AccountComponent]
})
export class AccountModule { }
