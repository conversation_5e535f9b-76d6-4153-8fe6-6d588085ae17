<header *ngIf="router.url == '' || router.url == '/' || router.url.includes('home')" class="header-bg header-main">
  <div class="container">
    <div class="header-left">
      <div class="logo">
        <button class="toggle" data-bs-toggle="dropdown" (click)="showMenu()">
          <span></span>
        </button>

        <div class="navigation-dropdown">
          <button class="close" (click)="closeMenu()">
            <svg class="fa-solid fa-chevron-left"></svg>
          </button>
          <div class="logo">
            <button (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">
              <img src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
            </button>
          </div>
          <div class="navigation-list">
            <ul>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">Home</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  href="/account/orders" routerLink="/account/orders">Orders</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/locations"
                  href="/locations">Locations</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/contact"
                  href="/contact">Contact us</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/about" href="/about">About
                  Us</a>
              </li>
              <!-- <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter href="/cuisines">Popular Cuisines</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/careers"
                  target="_blank">Job Opportunities</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/hardware"
                  target="_blank">Shop Hardware</a>
              </li> -->
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  routerLink="/account/help" href="/account/help">Help</a>
              </li>
              <li class="d-lg-none" *ngIf="this.userService.user?.id">
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">
                  Add Your Restaurant
                </a>
              </li>
            </ul>
          </div>
          <div class="your-restaurant-box">
            <!-- <p>
              <a (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">
                Add your Restaurant
              </a>
            </p>
            <p>
              <a href="https://www.ubsidi.com/epos/" target="_blank">
                Partner Center
              </a>
            </p> -->
            <div class="download-grubz-app">
              <div class="grubz-app-icon">
                <img src="assets/images/grubz-icon.svg" alt="grubz-icon">
              </div>
              <div class="grubz-download-app-text">
                <p>Download the Go Grubz app to use on your smartphone.</p>
              </div>
            </div>
            <div class="app-buttons">
              <ul>
                <li>
                  <a class="ios-btn cursor" href="https://apps.apple.com/in/app/go-grubz/id6484270752" target="_blank">
                    <img src="assets/images/apple.svg" alt="apple">
                    iOS
                  </a>
                </li>
                <li>
                  <a class="android-btn cursor" href="https://play.google.com/store/apps/details?id=com.gogrubzuk"
                    target="_blank">
                    <img src="assets/images/android.svg" alt="android">
                    Android
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="navigation-drawer-close-bg" (click)="closeMenu()"></div>
        <button routerLink="/" href="/">
          <img class="white-logo" src="assets/images/logo.svg" alt="GoGrubz-logo" loading="lazy">
          <img class="red-logo" src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
        </button>
      </div>

      <div class="navigation-box">
        <ul>
          <li [routerLinkActive]="['active']"><a *ngIf="this.userService.user?.id" nzMatchRouter href="/account/orders"
              routerLink="/account/orders">Orders</a></li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/locations" href="/locations">Locations</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/contact" href="/contact">Contact Us</a></li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/about" href="/about">About Us</a></li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/add-restaurant" href="/add-restaurant">Add
              your restaurant</a></li>
        </ul>
      </div>
    </div>

    <div class="header-right">
      <ul>
        <li class="add-cart-btn">
          <button class="basket-toggle-btn basket-toggle" (click)="showCart()">
            <img class="white-cart-image" src="assets/images/white-add-cart.svg" alt="GoGrubz-cart-image"
              loading="lazy">
            <img class="red-cart-image" src="assets/images/add-cart.svg" alt="GoGrubz-cart-image" loading="lazy">
            <span>
              {{cartService?.qty > 0 ? cartService?.qty : 0}}
            </span>
          </button>
        </li>

        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#signup-modal">Sign Up</a>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#login-modal">Sign In</a>
        </li>
        <li class="account-dropdown" *ngIf="this.userService.user?.id">
          <a #profileButton class="cursor" data-bs-toggle="dropdown" href="/account/profile">
            <span>{{this.userService.user?.first_name}}</span><svg class="fa-regular fa-user ms-1"></svg>
          </a>
          <div class="account-dropdown-content">
            <ul class="dropdown-menu account-dropdown-list" #profileMenu [ngClass]="{'show' : isProfile == true}">
              <li><a class="dropdown-item cursor" href="account/profile" routerLink="/account/profile"
                  (click)="isProfile = !this.isProfile">Profile</a></li>
              <li><a class="dropdown-item cursor" href="account/orders" routerLink="/account/orders"
                  (click)="isProfile = !this.isProfile">Orders</a></li>
              <li><a class="dropdown-item cursor" href="account/wallet" routerLink="/account/wallet"
                  (click)="isProfile = !this.isProfile">Wallet</a></li>
              <li><a class="dropdown-item cursor" href="account/paymant-methods" routerLink="/account/payment-methods"
                  (click)="isProfile = !this.isProfile">Payment
                  Methods</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/reservations" routerLink="/account/reservations"
                  (click)="isProfile = !this.isProfile">Reservations</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/invite-friends" routerLink="/account/invite-friends"
                  (click)="isProfile = !this.isProfile">Invite Friends
                </a>
              </li>
              <li><a class="dropdown-item cursor" href="account/add-restaurant" routerLink="/account/add-restaurant"
                  (click)="isProfile = !this.isProfile">Add Your Restaurant </a>
              </li>
              <!--<li><a class="dropdown-item cursor" href="account/profile" routerLink="/account/profile"
                (click)="isProfile = !this.isProfile"> View Account</a></li>-->
              <li>
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
            </ul>
          </div>
        </li>
        <!-- <li *ngIf="this.userService.user?.id">
          <a class="cursor" (click)="logout()">
            Logout
          </a>
        </li> -->
      </ul>
    </div>
  </div>
</header>

<header *ngIf="router.url.includes('about') || router.url.includes('add-restaurant')" class="header-bg black-header">
  <div class="container">
    <div class="header-left">
      <div class="logo">
        <button class="toggle" data-bs-toggle="dropdown" (click)="showMenu()">
          <span></span>
        </button>

        <div class="navigation-dropdown">
          <button class="close" (click)="closeMenu()">
            <svg class="fa-solid fa-chevron-left"></svg>
          </button>
          <div class="logo">
            <button (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">
              <img class="black-logo" src="assets/images/black-logo.svg" alt="GoGrubz-logo" loading="lazy">
            </button>
          </div>
          <div class="navigation-list">
            <ul>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">Home</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  href="/account/orders" routerLink="/account/orders">Orders</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/locations"
                  href="/locations">Locations</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/contact"
                  href="/contact">Contact us</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/about" href="/about">About
                  Us</a>
              </li>
              <!-- <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter href="/cuisines">Popular Cuisines</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/careers"
                  target="_blank">Job Opportunities</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/hardware"
                  target="_blank">Shop Hardware</a>
              </li> -->
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  routerLink="/account/help" href="/account/help">Help</a>
              </li>
              <li class="d-lg-none" *ngIf="this.userService.user?.id">
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">Add
                  Your Restaurant</a>
              </li>
            </ul>
          </div>
          <div class="your-restaurant-box">
            <!-- <p><a (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">Add your Restaurant</a></p>
            <p><a href="https://www.ubsidi.com/epos/" target="_blank">Partner Center</a></p> -->
            <div class="download-grubz-app">
              <div class="grubz-app-icon">
                <img src="assets/images/grubz-icon.svg" alt="grubz-icon">
              </div>
              <div class="grubz-download-app-text">
                <p>Download the Go Grubz app to use on your smartphone.</p>
              </div>
            </div>
            <div class="app-buttons">
              <ul>
                <li>
                  <a class="ios-btn cursor" href="https://apps.apple.com/in/app/go-grubz/id6484270752" target="_blank">
                    <img src="assets/images/apple.svg" alt="apple">
                    iOS
                  </a>
                </li>
                <li>
                  <a class="android-btn cursor" href="https://play.google.com/store/apps/details?id=com.gogrubzuk"
                    target="_blank">
                    <img src="assets/images/android.svg" alt="android">
                    Android
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="navigation-drawer-close-bg" (click)="closeMenu()"></div>

        <button routerLink="/" href="/">
          <img class="black-logo" src="assets/images/black-logo.svg" alt="GoGrubz-logo" loading="lazy">
        </button>
      </div>

      <div class="navigation-box">
        <ul>
          <li [routerLinkActive]="['active']"><a *ngIf="this.userService.user?.id" nzMatchRouter href="/account/orders"
              routerLink="/account/orders">
              Orders</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/locations" href="/locations"> Locations</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/contact" href="/contact"> Contact Us</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/about" href="/about"> About Us</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/add-restaurant" href="/add-restaurant"> Add
              your restaurant</a>
          </li>
        </ul>
      </div>
    </div>

    <div class="header-right">
      <ul>
        <li class="add-cart-btn">
          <button class="basket-toggle-btn basket-toggle" (click)="showCart()">
            <img src="assets/images/white-add-cart.svg" alt="GoGrubz-cart-image" loading="lazy">
            <span>
              {{cartService?.qty > 0 ? cartService?.qty : 0}}
            </span>
          </button>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#signup-modal">Sign Up</a>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#login-modal">Sign In</a>
        </li>
        <li class="account-dropdown" *ngIf="this.userService.user?.id">
          <a #profileButton class="cursor" data-bs-toggle="dropdown" href="/account/profile">
            <span>{{this.userService.user?.first_name}}</span> <svg class="fa-regular fa-user ms-1"></svg>
          </a>
          <div class="account-dropdown-content">
            <ul class="dropdown-menu account-dropdown-list" #profileMenu [ngClass]="{'show' : isProfile == true}">
              <li><a class="dropdown-item cursor" href="account/profile" routerLink="/account/profile"
                  (click)="isProfile = !this.isProfile">Profile</a></li>
              <li><a class="dropdown-item cursor" href="account/orders" routerLink="/account/orders"
                  (click)="isProfile = !this.isProfile">Orders</a></li>
              <li><a class="dropdown-item cursor" href="account/wallet" routerLink="/account/wallet"
                  (click)="isProfile = !this.isProfile">Wallet</a></li>
              <li><a class="dropdown-item cursor" href="account/paymant-methods" routerLink="/account/payment-methods"
                  (click)="isProfile = !this.isProfile">Payment
                  Methods</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/reservations" routerLink="/account/reservations"
                  (click)="isProfile = !this.isProfile">Reservations</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/invite-friends" routerLink="/account/invite-friends"
                  (click)="isProfile = !this.isProfile">Invite Friends
                </a>
              </li>
              <li><a class="dropdown-item cursor" href="account/add-restaurant" routerLink="/account/add-restaurant"
                  (click)="isProfile = !this.isProfile">Add Your Restaurant </a>
              </li>
              <li>
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
            </ul>
          </div>
        </li>
        <!-- <li *ngIf="this.userService.user?.id">
          <a class="cursor" (click)="logout()">
            Logout
          </a>
        </li> -->
      </ul>
    </div>
  </div>
</header>

<header *ngIf="router.url.includes('contact')" class="header-bg red-header">
  <div class="container">
    <div class="header-left">
      <div class="logo">
        <button class="toggle" data-bs-toggle="dropdown" (click)="showMenu()">
          <span></span>
        </button>

        <div class="navigation-dropdown">
          <button class="close" (click)="closeMenu()">
            <svg class="fa-solid fa-chevron-left"></svg>
          </button>
          <div class="logo">
            <button (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">
              <img class="black-logo" src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
            </button>
          </div>
          <div class="navigation-list">
            <ul>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">Home</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  href="/account/orders" routerLink="/account/orders">Orders</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/locations"
                  href="/locations">Locations</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/contact"
                  href="/contact">Contact us</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/about" href="/about">About
                  Us</a>
              </li>
              <!-- <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter href="/cuisines">Popular Cuisines</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/careers"
                  target="_blank">Job Opportunities</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/hardware"
                  target="_blank">Shop Hardware</a>
              </li> -->
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  routerLink="/account/help" href="/account/help">Help</a>
              </li>
              <li class="d-lg-none" *ngIf="this.userService.user?.id">
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
              <li>
                <a class="dropdown-item cursor" (click)="closeMenu()" routerLink="/add-restaurant"
                  href="/add-restaurant">
                  Add Your Restaurant
                </a>
              </li>
            </ul>
          </div>
          <div class="your-restaurant-box">
            <!-- <p>
              <a (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">
                Add your Restaurant
              </a>
            </p>
            <p>
              <a href="https://www.ubsidi.com/epos/" target="_blank">
                Partner Center
              </a>
            </p> -->
            <div class="download-grubz-app">
              <div class="grubz-app-icon">
                <img src="assets/images/grubz-icon.svg" alt="grubz-icon">
              </div>
              <div class="grubz-download-app-text">
                <p>Download the Go Grubz app to use on your smartphone.</p>
              </div>
            </div>
            <div class="app-buttons">
              <ul>
                <li>
                  <a class="ios-btn cursor" href="https://apps.apple.com/in/app/go-grubz/id6484270752" target="_blank">
                    <img src="assets/images/apple.svg" alt="apple">
                    iOS
                  </a>
                </li>
                <li>
                  <a class="android-btn cursor" href="https://play.google.com/store/apps/details?id=com.gogrubzuk"
                    target="_blank">
                    <img src="assets/images/android.svg" alt="android">
                    Android
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="navigation-drawer-close-bg" (click)="closeMenu()"></div>
        <button routerLink="/" href="/">
          <img src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
        </button>
      </div>

      <div class="navigation-box">
        <ul>
          <li [routerLinkActive]="['active']"><a *ngIf="this.userService.user?.id" nzMatchRouter href="/account/orders"
              routerLink="/account/orders">
              Orders</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/locations" href="/locations"> Locations</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/contact" href="/contact"> Contact Us</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/about" href="/about"> About Us</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/add-restaurant" href="/add-restaurant"> Add
              your restaurant</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="header-right">
      <ul>
        <li class="add-cart-btn">
          <button class="basket-toggle-btn basket-toggle" (click)="showCart()">
            <img src="assets/images/white-add-cart.svg" alt="GoGrubz-cart-image" loading="lazy">
            <span>
              {{cartService?.qty > 0 ? cartService?.qty : 0}}
            </span>
          </button>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#signup-modal">
            Sign Up
          </a>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#login-modal">
            Sign In
          </a>
        </li>
        <li class="account-dropdown" *ngIf="this.userService.user?.id">
          <a #profileButton class="cursor" data-bs-toggle="dropdown" href="/account/profile">
            <span>{{this.userService.user?.first_name}}</span> <svg class="fa-regular fa-user ms-1"></svg>
          </a>
          <div class="account-dropdown-content">
            <ul class="dropdown-menu account-dropdown-list" #profileMenu [ngClass]="{'show' : isProfile == true}">
              <li><a class="dropdown-item cursor" href="account/profile" routerLink="/account/profile"
                  (click)="isProfile = !this.isProfile">Profile</a></li>
              <li><a class="dropdown-item cursor" href="account/orders" routerLink="/account/orders"
                  (click)="isProfile = !this.isProfile">Orders</a></li>
              <li><a class="dropdown-item cursor" href="account/wallet" routerLink="/account/wallet"
                  (click)="isProfile = !this.isProfile">Wallet</a></li>
              <li><a class="dropdown-item cursor" href="account/paymant-methods" routerLink="/account/payment-methods"
                  (click)="isProfile = !this.isProfile">Payment
                  Methods</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/reservations" routerLink="/account/reservations"
                  (click)="isProfile = !this.isProfile">Reservations</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/invite-friends" routerLink="/account/invite-friends"
                  (click)="isProfile = !this.isProfile">Invite Friends
                </a>
              </li>
              <li><a class="dropdown-item cursor" href="account/add-restaurant" routerLink="/account/add-restaurant"
                  (click)="isProfile = !this.isProfile">Add Your Restaurant </a>
              </li>
              <li>
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
            </ul>
          </div>
        </li>
        <!-- <li *ngIf="this.userService.user?.id">
          <a class="cursor" (click)="logout()">
            Logout
          </a>
        </li> -->
      </ul>
    </div>
  </div>
</header>

<header
  *ngIf="router.url != '' && router.url != '/' && !router.url.includes('home') && !router.url.includes('about') && !router.url.includes('add-restaurant') && !router.url.includes('contact')"
  class="header-bg header-two">
  <div class="container">
    <div class="header-left">
      <div class="logo">
        <button class="toggle" data-bs-toggle="dropdown" (click)="showMenu()">
          <span></span>
        </button>

        <div class="navigation-dropdown navigation-dropdown-two">
          <button class="close" (click)="closeMenu()">
            <svg class="fa-solid fa-chevron-left"></svg>
          </button>
          <div class="logo">
            <button (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">
              <img src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
            </button>
          </div>
          <div class="navigation-list">
            <ul>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/" href="/">Home</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  href="/account/orders" routerLink="/account/orders">Orders</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/locations"
                  href="/locations">Locations</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/contact"
                  href="/contact">Contact us</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter routerLink="/about" href="/about">About
                  Us</a>
              </li>
              <!--<li>
                <a class="dropdown-item" (click)="closeMenu()" nzMatchRouter href="/cuisines">Popular Cuisines</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/careers"
                  target="_blank">Job Opportunities</a>
              </li>
              <li>
                <a class="dropdown-item" (click)="closeMenu()" href="https://www.ubsidi.com/web/hardware"
                  target="_blank">Shop Hardware</a>
              </li>-->
              <li>
                <a class="dropdown-item" (click)="closeMenu()" *ngIf="this.userService.user?.id" nzMatchRouter
                  routerLink="/account/help" href="/account/help">Help</a>
              </li>
              <li class="d-lg-none" *ngIf="this.userService.user?.id">
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
              <li>
                <a class="dropdown-item cursor" (click)="closeMenu()" routerLink="/add-restaurant"
                  href="/add-restaurant">
                  Add Your Restaurant
                </a>
              </li>
            </ul>
          </div>
          <div class="your-restaurant-box">
            <!-- <p>
              <a (click)="closeMenu()" routerLink="/add-restaurant" href="/add-restaurant">
                Add your Restaurant
              </a>
            </p>
            <p>
              <a href="https://www.ubsidi.com/epos/" target="_blank">
                Partner Center
              </a>
            </p> -->
            <div class="download-grubz-app">
              <div class="grubz-app-icon">
                <img src="assets/images/grubz-icon.svg" alt="grubz-icon">
              </div>
              <div class="grubz-download-app-text">
                <p>Download the Go Grubz app to use on your smartphone.</p>
              </div>
            </div>
            <div class="app-buttons">
              <ul>
                <li>
                  <a class="ios-btn cursor" href="https://apps.apple.com/in/app/go-grubz/id6484270752" target="_blank">
                    <img src="assets/images/apple.svg" alt="apple">
                    iOS
                  </a>
                </li>
                <li>
                  <a class="android-btn cursor" href="https://play.google.com/store/apps/details?id=com.gogrubzuk"
                    target="_blank">
                    <img src="assets/images/android.svg" alt="android">
                    Android
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="navigation-drawer-close-bg" (click)="closeMenu()"></div>
        <button nzMatchRouter routerLink="/" href="/">
          <img src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
        </button>
      </div>

      <div class="navigation-box" *ngIf="router.url.includes('locations')">
        <ul>
          <li [routerLinkActive]="['active']"><a *ngIf="this.userService.user?.id" nzMatchRouter href="/account/orders"
              routerLink="/account/orders">
              Orders</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/locations" href="/locations"> Locations</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/contact" href="/contact"> Contact Us</a>
          </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/about" href="/about"> About Us</a> </li>
          <li [routerLinkActive]="['active']"><a nzMatchRouter routerLink="/add-restaurant" href="/add-restaurant"> Add
              your restaurant</a>
          </li>
        </ul>
      </div>

    </div>
    <div class="header-middle-box">
      <div class="location-box"
        *ngIf="!router.url.includes('locations') && (router.url.includes('location') || router.url.includes('menus'))">
        <ul>
          <li><svg class="fa-solid fa-location-dot"></svg></li>
          <li>{{ restaurantService.zipcode }}</li>
          <li class="dot"><svg class="fa-solid fa-circle"></svg></li>
          <li>
            <button class="cursor" (click)="serachVisibled()">Change</button>
          </li>
        </ul>
        <div class="location-search-box" *ngIf="searchVisible">
          <form nz-form #fetchForm="ngForm" (ngSubmit)="restaurantFetch(fetchForm)">
            <nz-form-item>
              <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode!">
                <nz-input-group>
                  <div class="search-for-restaurant">
                    <svg class="fa-solid fa-magnifying-glass"></svg>
                    <input class="form-control" nz-input name="zipcode" id="zipcode"
                      [(ngModel)]="searchRestaurant.zipcode" required (input)="formatPostcode($event)"
                      placeholder="Search for location" type="text">
                    <button class="btn" nz-button>
                      <!-- (keydown.space)="onSpaceKeyDown($event)" -->
                      <i class="spinner-border" *ngIf="isLoading"></i>
                      <svg class="fa-regular fa-paper-plane"></svg>
                    </button>
                  </div>
                </nz-input-group>
              </nz-form-control>
            </nz-form-item>

            <nz-form-item *ngIf="error">
              <span class="text-danger">{{ error }}</span>
            </nz-form-item>
          </form>
        </div>
        <div class="location-search-box-close-bg" (click)="serachVisibled()"></div>
      </div>

      <div *ngIf="!router.url.includes('locations') && router.url.includes('location')" class="search-for-restaurant">
        <nz-input-group>
          <svg class="fa-solid fa-magnifying-glass"></svg>
          <input class="form-control" [(ngModel)]="searchData" name="searchData" id="searchData"
            placeholder="Search for a restaurant or cuisines" (input)="searchResturant($event.target.value)"
            type="text">
          <span (click)="clearSearch()" *ngIf="searchData">
            <svg class="fa-regular fa-xmark-circle search-close"></svg>
          </span>
        </nz-input-group>
      </div>
    </div>

    <div class="header-right">
      <ul>
        <li class="add-cart-btn" *ngIf="router.url.includes('reservation')">
          <button class="basket-toggle-btn" (click)="showCart()">
            <img class="white-cart-image" src="assets/images/white-add-cart.svg" alt="GoGrubz-cart-image"
              loading="lazy">
            <img class="red-cart-image" src="assets/images/add-cart.svg" alt="GoGrubz-cart-image" loading="lazy">
            <span>
              {{cartService?.qty > 0 ? cartService?.qty : 0}}
            </span>
          </button>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#signup-modal">
            Sign Up
          </a>
        </li>
        <li *ngIf="!this.userService.user?.id">
          <a data-bs-toggle="modal" href="#login-modal">
            Sign In
          </a>
        </li>
        <li class="account-dropdown" *ngIf="this.userService.user?.id">
          <a #profileButton class="cursor" [ngClass]="{'active' : router.url.includes('account')}"
            data-bs-toggle="dropdown" href="/account/profile">
            <span>{{this.userService.user?.first_name}}</span> <svg class="fa-regular fa-user ms-1"></svg>
          </a>
          <div class="account-dropdown-content">
            <ul class="dropdown-menu account-dropdown-list" #profileMenu [ngClass]="{'show' : isProfile == true}">
              <li><a class="dropdown-item cursor" href="account/profile" routerLink="/account/profile"
                  (click)="isProfile = !this.isProfile">Profile</a></li>
              <li><a class="dropdown-item cursor" href="account/orders" routerLink="/account/orders"
                  (click)="isProfile = !this.isProfile">Orders</a></li>
              <li><a class="dropdown-item cursor" href="account/wallet" routerLink="/account/wallet"
                  (click)="isProfile = !this.isProfile">Wallet</a></li>
              <li><a class="dropdown-item cursor" href="account/paymant-methods" routerLink="/account/payment-methods"
                  (click)="isProfile = !this.isProfile">Payment
                  Methods</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/reservations" routerLink="/account/reservations"
                  (click)="isProfile = !this.isProfile">Reservations</a>
              </li>
              <li><a class="dropdown-item cursor" href="account/invite-friends" routerLink="/account/invite-friends"
                  (click)="isProfile = !this.isProfile">Invite Friends
                </a>
              </li>
              <li><a class="dropdown-item cursor" href="account/add-restaurant" routerLink="/account/add-restaurant"
                  (click)="isProfile = !this.isProfile">Add Your Restaurant </a>
              </li>
              <li>
                <button class="dropdown-item cursor logout-btn" (click)="logout()">Logout</button>
              </li>
            </ul>
          </div>
        </li>
        <!-- <li *ngIf="this.userService.user?.id">
            <a (click)="logout()">
              Logout
            </a>
          </li> -->
      </ul>
    </div>
  </div>
</header>

<div class="add-cart-box">
  <div class="cart-top">
    <button class="close basket-toggle-btn basket-toggle cursor" (click)="closeCart()">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="top-cart-name">
      <div class="cart-logo" *ngIf="restaurant?.restaurant_name">
        <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
          onerror="this.src='./assets/favicon.png';">
      </div>
      <div class="cart-name" *ngIf="restaurant?.restaurant_name">
        <h6>Your Basket<img src="assets/images/cart.svg" alt="Go-Grubz-Cart-Image" loading="lazy"></h6>
        <p>{{ restaurant?.restaurant_name }}</p>
      </div>
      <div class="cart-name" *ngIf="!restaurant?.restaurant_name">
        <h6>Your Basket<img src="assets/images/cart.svg" alt="Go-Grubz-Cart-Image" loading="lazy"></h6>
      </div>
    </div>
    <div class="checkout-btn">
      <button class="cursor" *ngIf="cartService?.qty <= 0"
        style="background-color:#f4f3f3 !important;border-color:#f4f3f3;">
        <span style="color: #8f8f8a;">Checkout<svg class="fa-solid fa-arrow-right"></svg></span>
        <span style="color: #8f8f8a;">{{ convertNumber( cartService?.grandTotal) }}</span>
      </button>
      <a class="cursor" (click)="closeCart()" href="/checkout" routerLink="checkout" *ngIf="cartService?.qty > 0"
        [class.disabled]="cartService?.qty > 0">
        <span>Checkout<svg class="fa-solid fa-arrow-right"></svg></span>
        <span>{{ convertNumber( cartService?.grandTotal) }}</span>
      </a>
    </div>
  </div>

  <div class="cart-middle" *ngIf="cartService?.qty == 0">
    <div class="empty-cart-cls text-center py-5">
      <img src="assets/cartitem.png" alt="GoGrubz-cart-item" loading="lazy">
      <p><strong>No Item(s) Added</strong></p>
    </div>
  </div>

  <div class="cart-middle" *ngIf="cartService?.qty > 0">
    <div class="cart-item" *ngFor="let cart of cartService?.carts; let i=index;">
      <div class="cart-image-item" *ngIf="cart?.image_url">
        <img [src]="cart?.image_url" [alt]="cart.menu_name">
      </div>
      <div class="cart-content">
        <h6>{{cart.menu_name}}</h6>
        <ul *ngIf="cart.subaddons_name">
          <li>{{cart.subaddons_name}}</li>
        </ul>
        <p>{{convertNumber(cart?.total_price)}}</p>
      </div>
      <div class="cart-add-item">
        <ul>
          <li>
            <button (click)="updateToCart(cart,i,'remove')">
              <svg class="fa-solid fa-minus"></svg>
              <!-- <svg class="fa-solid fa-trash-can"></svg> -->
            </button>
          </li>
          <li>
            <span>{{ cart.quantity }}</span>
          </li>
          <li>
            <button (click)="updateToCart(cart,i,'add')"><svg class="fa-solid fa-plus"></svg></button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="add-cart-box-remove-bg" (click)="closeCart()"></div>

<div>
  <router-outlet></router-outlet>
</div>

<section class="footer-bg">
  <div class="row">
    <div class="col-xl-6 col-lg-4 col-md-3 col-sm-12">
      <div class="footer-box text-center text-md-start">
        <button nzMatchRouter>
          <img class="footer-logo" src="assets/images/footer-logo.svg" alt="GoGrubz-logo" loading="lazy">
        </button>
      </div>
    </div>
    <div class="col-xl-2 col-lg-3 col-md-3 col-sm-12">
      <div class="footer-box">
        <ul>
          <li>
            <a routerLink="/contact" href="/contact">
              Get Help
            </a>
          </li>
          <li>
            <a routerLink="/add-restaurant" href="/add-restaurant">
              Add your restaurant
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="col-xl-2 col-lg-3 col-md-3 col-sm-12">
      <div class="footer-box">
        <ul>
          <li>
            <a routerLink="/locations" href="/locations">
              Restaurants near me
            </a>
          </li>
          <li>
            <a routerLink="/locations" href="/locations">
              View all cities
            </a>
          </li>
          <li>
            <button (click)="findLocation()">
              Pickup near me
            </button>
          </li>
          <li>
            <a routerLink="/about" href="/about">
              About Us
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class="col-xl-2 col-lg-2 col-md-3 col-sm-12">
      <div class="h-100 d-flex align-items-center justify-content-lg-center">
        <div class="follow-us">
          <p>FOLLOW US</p>
          <ul>
            <li>
              <a href="https://www.facebook.com/profile.php?id=61555751249921" target="_blank">
                <svg class="fa-brands fa-facebook-f"></svg>
              </a>
            </li>
            <li>
              <a href="https://www.instagram.com/gogrubz/" target="_blank">
                <svg class="fa-brands fa-instagram"></svg>
              </a>
            </li>
            <li>
              <a routerLink="/" href="/" target="_blank">
                <svg class="fa-brands fa-linkedin-in"></svg>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="col-lg-12">
      <div class="copy-right">
        <p>
          © 2023 All Rights Reserved.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Login-Modal -->
<div class="modal fade" id="login-modal">
  <div class="modal-dialog">
    <div class="modal-content">
      <button type="button" class="btn-close" #closeModal data-bs-dismiss="modal" (click)="handleCancel()">
        <svg class="fa-solid fa-xmark"></svg>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Sign In</h5>
        <p class="dont-have-account-text">
          Don’t have an account? <button data-bs-toggle="modal" href="#signup-modal">Sign up here.</button>
        </p>

        <form nz-form #loginForm="ngForm" (ngSubmit)="loginSubmit(loginForm)" nzLayout="vertical">
          <div class="form-group">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="ShellemailErrorTpl">
                <nz-input-group>
                  <input class="form-control" type="email" email="true"
                    pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #shellLogEmail="ngModel" nz-input
                    name="lusername" id="lusername" (keydown.space)="onSpaceKeyDown($event)"
                    [(ngModel)]="secondUser.username" required placeholder="Email Address">
                </nz-input-group>
                <ng-template #ShellemailErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter your email!
                  </ng-container>
                  <ng-container
                    *ngIf="control.hasError('email') || (!control.hasError('required') && shellLogEmail.touched) || (!control.hasError('required') && !shellLogEmail.valid)">
                    Email must be a valid email address
                  </ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div class="form-group">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="passwordErrorTpl">
                <nz-input-group>
                  <input class="form-control" [type]="lhide ? 'password' : 'text'" nz-input name="password"
                    (keydown.space)="onSpaceKeyDown($event)" id="password" [(ngModel)]="secondUser.password" required
                    placeholder="Password">
                </nz-input-group>

                <ng-template #passwordErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter your password!
                  </ng-container>
                  <ng-container *ngIf="control.hasError('minlength')">
                    Password must be atleast 6 characters long!
                  </ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <span class="icon" (click)="hideLpassword()">
              <img src="assets/images/eye.svg" *ngIf="lhide" alt="Go-Grubz-Eye-Image" loading="lazy">
              <img src="assets/images/eye-off.svg" *ngIf="!lhide" alt="Go-Grubz-Eye-Off-Image" loading="lazy">
            </span>
          </div>

          <nz-form-item *ngIf="error">
            <span class="signup-here-error-text mb-2 d-inline-block">{{ error }}</span>
          </nz-form-item>
          <div class="forgot-password text-end">
            <span data-bs-toggle="modal" data-bs-target="#forgot-password-popup" class="cursor">Forgot Password?</span>
          </div>
          <button class="btn" nz-button [disabled]="isLoading">
            <!-- <i class="spinner-border" *ngIf="isLoading"></i> -->
            Sign In
          </button>

          <!-- <div class="or-option"
            *ngIf="siteSetting.google_login == 'Y' || siteSetting.facebook_login == 'Y' || siteSetting.apple_login == 'Y'">
            <span>or</span>
          </div>

          <div class="socials-login-options">
            <ul>
              <li *ngIf="siteSetting.google_login == 'Y'">
                <a class="google-btn" (click)="loginWithGoogle()">
                  <img src="assets/images/google.svg" alt="google">
                  Continue with Google
                </a>
              </li>
              <li *ngIf="siteSetting.facebook_login == 'Y'">
                <a class="facebook-btn" (click)="loginWithFacebook()">
                  <img src="assets/images/facebook.svg" alt="google">
                  Continue with Facebook
                </a>
              </li>
              <li *ngIf="siteSetting.apple_login == 'Y'">
                <a class="apple-btn">
                  <img src="assets/images/white-apple.svg" alt="google">
                  Continue with Apple
                </a>
              </li>
            </ul>
          </div> -->
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Sign-up-Modal -->
<div class="modal fade" id="signup-modal">
  <div class="modal-dialog">
    <div class="modal-content">
      <button type="button" class="btn-close" #closeSignUpModal data-bs-dismiss="modal" (click)="handleCancel()">
        <svg class="fa-solid fa-xmark"></svg>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Sign Up</h5>
        <p class="dont-have-account-text">
          Already have an account? <button data-bs-toggle="modal" href="#login-modal">Sign in here.</button>
        </p>
        <form nz-form #signupForm="ngForm" (ngSubmit)="onSubmit(signupForm)" nzLayout="vertical">
          <div class="row">

            <div class="col-md-6">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="ShellSignFirstErrorTpl">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input name="first_name" id="first_name"
                        (keydown)="onSpaceKeyDown($event)" [(ngModel)]="user.first_name" required
                        placeholder="First Name">
                    </nz-input-group>
                    <ng-template #ShellSignFirstErrorTpl let-control>
                      <!-- <ng-container *ngIf="control.hasError('required')">
                        Please enter first name!
                      </ng-container> -->
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your first name!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter your last name!">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input name="last_name" id="last_name"
                        (keydown)="onSpaceKeyDown($event)" [(ngModel)]="user.last_name" required
                        placeholder="Last Name">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="ShellSignemailErrorTpl">
                    <nz-input-group>
                      <input class="form-control" type="email" email="true"
                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #shellSignEmail="ngModel" nz-input
                        name="username" id="username" (keydown.space)="onSpaceKeyDown($event)"
                        [(ngModel)]="user.username" required placeholder="Email Address">
                    </nz-input-group>
                    <ng-template #ShellSignemailErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your email!
                      </ng-container>
                      <ng-container
                        *ngIf="control.hasError('email') || (!control.hasError('required') && shellSignEmail.touched) || (!control.hasError('required') && !shellSignEmail.valid)">
                        Email must be a valid email address
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter phone number!">
                    <nz-input-group>
                      <input class="form-control" type="text" inputmode="numeric" nz-input name="phone_number"
                        (keydown.space)="onSpaceKeyDown($event)" id="phone_number" (keypress)="validateMobile($event)"
                        [(ngModel)]="user.phone_number" required placeholder="Mobile Number">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="passwordRegErrorTpl">
                    <nz-input-group>
                      <input class="form-control" [type]="hide ? 'password' : 'text'" nz-input name="password"
                        (keydown.space)="onSpaceKeyDown($event)" id="password" [(ngModel)]="user.password" minlength="6"
                        required placeholder="Password">
                      <span class="icon" (click)="hidePassword()">
                        <img class="cursor" src="assets/images/eye.svg" *ngIf="hide" alt="Go-Grubz-Eye-Image"
                          loading="lazy">
                        <img class="cursor" src="assets/images/eye-off.svg" *ngIf="!hide" alt="Go-Grubz-Eye-Off-Image"
                          loading="lazy">
                      </span>
                    </nz-input-group>

                    <ng-template #passwordRegErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your password!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('minlength')">
                        Password must be atleast 6 characters long!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                    <nz-input-group>
                      <input class="form-control" [type]="chide ? 'password' : 'text'" nz-input name="confirmPassword"
                        (keydown.space)="onSpaceKeyDown($event)" id="confirmPassword" [(ngModel)]="user.confirmPassword"
                        required placeholder="Confirm Password">
                      <span class="icon" (click)="hideCpassword()">
                        <img class="cursor" src="assets/images/eye.svg" *ngIf="chide" alt="Go-Grubz-Eye-Image"
                          loading="lazy">
                        <img class="cursor" src="assets/images/eye-off.svg" *ngIf="!chide" alt="Go-Grubz-Eye-Off-Image"
                          loading="lazy">
                      </span>
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter referred code!">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input name="referred_by" id="referred_by"
                        (keydown.space)="onSpaceKeyDown($event)" [(ngModel)]="user.referred_by"
                        placeholder="Referral Code (optional)">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>

          <div class="terms-conditions">
            <p>
              By signing up or using “Continue with Google, Facebook, or Apple,” you agree to Go
              Grubz’s <a href="/terms-condition" target="_blank">Terms and Conditions</a> and
              <a href="/privacy-policy" target="_blank">Privacy Policy.</a>
            </p>
          </div>

          <nz-form-item *ngIf="SignModelerror">
            <span class="text-danger">{{ SignModelerror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isSignModelLoading">
            <!-- <i class="spinner-border" *ngIf="isSignModelLoading"></i> -->
            Sign Up
          </button>

          <!-- <div class="or-option"
            *ngIf="siteSetting.google_login == 'Y' || siteSetting.facebook_login == 'Y' || siteSetting.apple_login == 'Y'">
            <span>or</span>
          </div>

          <div class="socials-login-options">
            <ul>
              <li *ngIf="siteSetting.google_login == 'Y'">
                <a class="google-btn" (click)="loginWithGoogle()">
                  <img src="assets/images/google.svg" alt="google">
                  Continue with Google
                </a>
              </li>
              <li *ngIf="siteSetting.facebook_login == 'Y'">
                <a class="facebook-btn" (click)="loginWithFacebook()">
                  <img src="assets/images/facebook.svg" alt="google">
                  Continue with Facebook
                </a>
              </li>
              <li *ngIf="siteSetting.apple_login == 'Y'">
                <a class="apple-btn">
                  <img src="assets/images/white-apple.svg" alt="google">
                  Continue with Apple
                </a>
              </li>
            </ul>
          </div> -->

        </form>
      </div>

    </div>
  </div>
</div>

<!-- Forgot-Password-Modal -->
<div class="modal fade" id="forgot-password-popup">
  <div class="modal-dialog">
    <div class="modal-content">
      <button type="button" class="btn-close" #closeForgotUpModal data-bs-dismiss="modal" (click)="handleCancel()">
        <svg class="fa-solid fa-xmark"></svg>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Forgot Password</h5>
        <!-- <p class="dont-have-account-text">Already have an account? <button data-bs-toggle="modal" href="#login-modal">Sign in
          here.</button></p> -->

        <form nz-form #forgotForm="ngForm" (ngSubmit)="onSubmitForgot(forgotForm)" *ngIf="!otpVisible">
          <p class="dont-have-account-text">Please enter the e-mail address used to register. We will send your new
            OTP to that address.</p>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="emailErrorTpl">
                    <nz-input-group>
                      <input class="form-control" type="email" email="true" nz-input [(ngModel)]="forgotUser.username"
                        required id="otp-verification-email" name="otp-verification-email"
                        placeholder="Enter your email">
                      <!-- (keydown.space)="onSpaceKeyDown($event)"  pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #shellForEmail="ngModel"  -->
                    </nz-input-group>
                    <ng-template #emailErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your email!
                      </ng-container>
                      <ng-container
                        *ngIf="control.hasError('email') || (!control.hasError('required') && shellForEmail.touched) || (!control.hasError('required') && !shellForEmail.valid)">
                        Please enter a valid email address!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>

          <nz-form-item *ngIf="Modelerror">
            <span class="text-danger">{{ Modelerror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isModelLoading">
            <i class="spinner-border" *ngIf="isModelLoading"></i>
            Send
          </button>
          <div class="back-to-login">
            <span>Back to</span> <a class="cursor" data-bs-toggle="modal" href="#login-modal">Sign in</a>
          </div>
        </form>

        <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)" *ngIf="otpVisible">
          <p class="dont-have-account-text text-success"> Check your email for OTP.</p>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label class="form-label">
                  Enter OTP
                </label>
                <!-- <div class="form-otp-list">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 0)" name="otpInput-1" id="otpInput1" #textbox1
                    (keyup.enter)="focusNext(textbox1)" autocomplete="off">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 1)" name="otpInput-2" id="otpInput2" #textbox2
                    (keyup.enter)="focusNext(textbox2)" autocomplete="off">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 2)" name="otpInput-3" id="otpInput3" #textbox3
                    (keyup.enter)="focusNext(textbox3)" autocomplete="off">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 3)" name="otpInput-4" id="otpInput4" #textbox4
                    (keyup.enter)="focusNext(textbox4)" autocomplete="off">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 4)" name="otpInput-5" id="otpInput5" #textbox5
                    (keyup.enter)="focusNext(textbox5)" autocomplete="off">
                  <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
                    (input)="handleInput($event, 5)" name="otpInput-6" id="otpInput6" #textbox6
                    (keyup.enter)="focusNext(textbox6)" autocomplete="off">
                </div>
                <input type="text" nz-input [value]="emailOtp" hidden readonly /> -->

                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                    <nz-input-group>
                      <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                        (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="email_otp" name="email_otp"
                        (keypress)="validateMobile($event)" [(ngModel)]="forgotUser.email_otp" required
                        placeholder="Enter 6-digit code">
                    </nz-input-group>
                    <ng-template #phoneVeriErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter verification code!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('minlength')">
                        statement verification code at least 6 digit!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('maxlength')">
                        statement verification code maximum 6 digits long!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>

              </div>
            </div>
          </div>

          <nz-form-item *ngIf="Otperror">
            <span class="text-danger">{{ Otperror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isOtpLoading">
            <i class="spinner-border" *ngIf="isOtpLoading"></i>
            Verify
          </button>
          <div class="resend-code text-center" (click)="forgotPassword(forgotForm)">
            <a class="cursor">Resend OTP</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- New-Password-Modal -->
<ng-template #newPassword let-modal>
  <div id="new-password-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body login-body">
      <h5 class="login-title">New Password</h5>
      <!-- <p class="dont-have-account-text">Already have an account? <button data-bs-toggle="modal" href="#login-modal">Sign in
          here.</button></p> -->
      <form nz-form #changePasswordForm="ngForm" (ngSubmit)="onChangePasswordSubmit(changePasswordForm)">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter New Password"> -->
              <nz-form-item>
                <nz-form-control nzHasFeedback [nzErrorTip]="passwordChnageErrorTpl">
                  <nz-input-group>
                    <input type="password" id="password" name="password" [(ngModel)]="forgotUser.password" required
                      minlength="6" placeholder="Enter new password" class="form-control">
                  </nz-input-group>

                  <ng-template #passwordChnageErrorTpl let-control>
                    <ng-container *ngIf="control.hasError('required')">
                      Please enter your current password!
                    </ng-container>
                    <ng-container *ngIf="control.hasError('minlength')">
                      Password must be atleast 6 characters long!
                    </ng-container>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter Confirm Password"> -->
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                  <nz-input-group>
                    <input type="password" id="confirmPassword" name="confirmPassword"
                      [(ngModel)]="forgotUser.confirmPassword" required placeholder="Enter your confirm password"
                      class="form-control">
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <nz-form-item *ngIf="errorChangePassword">
          <span class="text-danger">{{ errorChangePassword }}</span>
        </nz-form-item>

        <button class="btn" nz-button [disabled]="isChangePasswordLoading">
          <i class="spinner-border" *ngIf="isChangePasswordLoading"></i>
          Submit
        </button>
      </form>
    </div>
  </div>
</ng-template>