import { Component, HostListener, Inject, OnInit, Renderer2 } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { Restaurant } from '../../core/models/restaurant';
import { Cuisines } from '../../core/models/cuisines';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { User } from '../../core/models/user';
import { DOCUMENT } from '@angular/common';
import { NgForm } from '@angular/forms';
import { Dietary } from '../../core/models/dietaries';
import { ShellComponent } from '../../shell/shell.component';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-restaurants',
  host: { ngSkipHydration: 'true' },
  templateUrl: './restaurants.component.html',
  styleUrls: ['./restaurants.component.scss']
})

export class RestaurantsComponent implements OnInit {
  private subs = new Subscription();

  loginUser: User;
  restaurant: Restaurant = new Restaurant();
  searchRestaurant: Restaurant = new Restaurant();
  restaurants: Restaurant[] = [];
  popularRestaurants: Restaurant[] = [];
  offerRestaurants: Restaurant[] = [];
  closeRestaurants: Restaurant[] = [];
  favRestaurants: Restaurant[] = [];
  originalRestaurants: Restaurant[] = [];
  cuisines: Cuisines[] = [];
  dietaries: Dietary[] = [];
  selected_array = [];
  searchData: string = '';
  searchVisible = false;

  filters = {
    fastDelivery: false,
    rating: {
      threestar: false,
      fourstar: false,
      fivestar: false,
    },
    deliveryFee: {
      one: false,
      two: false,
      five: false,
      ten: false,
    },
    sortBy: null,
    offer: false,
    dietary: null,
  };

  isLoading = false; error = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private restaurantService: RestaurantService,
    public userService: UserService,
    private route: ActivatedRoute,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private renderer: Renderer2,
    private shellComponent: ShellComponent
  ) { }

  ngOnInit() {
    this.loginUser = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.zipcode = localStorage.getItem(environment.zipcode);
    }

    this.restaurantService.searchData$.subscribe((data) => {
      this.searchData = data;
      this.search(this.searchData);
    });

    if (!this.restaurant.zipcode) {
      this.router.navigateByUrl('/');
    } else {
      this.fetchRestaurants()
    }
  }

  @HostListener('contextmenu', ['$event'])
  onContextMenu(event: Event): void {
    event.preventDefault();
  }

  @HostListener('paste', ['$event']) blockPaste(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('copy', ['$event']) blockCopy(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('cut', ['$event']) blockCut(e: KeyboardEvent) {
    e.preventDefault();
  }

  slideConfig = {
    "slidesToShow": 7,
    "slidesToScroll": 7,
    "infinite": true,
    "draggable": false,
    // "swipeToSlide": true,
    "dots": false,
    useTransform: false,
    cssEase: 'linear',
    // "autoplay": true,
    // "arrows": false,
    // "autoplaySpeed": 1500,
    "responsive": [
      {
        breakpoint: 1501,
        settings: {
          slidesToShow: 9,
          slidesToScroll: 9,
        }
      },
      {
        breakpoint: 1331,
        settings: {
          slidesToShow: 8,
          slidesToScroll: 8,
        }
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 6,
          slidesToScroll: 6,
          "infinite": true,
          "draggable": true,
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 5,
          slidesToScroll: 5,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 4,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          draggable: true
        }
      }
    ]
  };

  slideConfigPopular = {
    "slidesToShow": 4,
    "slidesToScroll": 1,
    "infinite": false,
    "draggable": false,
    // "swipeToSlide": true,
    useTransform: false,
    cssEase: 'linear',
    "responsive": [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          "draggable": true,
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  fetchRestaurants() {
    this.isLoading = true; this.error = null;

    this.restaurant.user_id = this.loginUser?.id
    this.restaurantService.find(this.restaurant)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.restaurants = res.restaurants
          this.originalRestaurants = res.restaurants

          this.popularRestaurants = [];
          this.offerRestaurants = [];
          this.closeRestaurants = [];
          this.restaurants.forEach(restaurant => {
            if (restaurant?.new == '1') {
              this.popularRestaurants.push(restaurant);
            }
            if (restaurant?.offer_texts.length > 0) {
              this.offerRestaurants.push(restaurant);
            }
            if (restaurant?.currentStatus == 'Closed') {
              this.closeRestaurants.push(restaurant);
            }
          });

          this.cuisines = res.cuisines;
          this.dietaries = res.dietaries;
          this.favRestaurants = res.favoriteRestaurants
        },
        (err) => {
          // this.error = err;
        }
      );
  }

  serchVisibility() {
    this.searchVisible = !this.searchVisible;
    if (this.searchVisible) {
      this.renderer.addClass(this.document.body, 'open-searchbar');
    } else {
      this.renderer.removeClass(this.document.body, 'open-searchbar');
    }
  }

  restaurantFetch(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.restaurantService.saveZipCode(this.searchRestaurant.zipcode.replace(/\s/g, ""));
    this.searchVisible = false;
    window.location.href = "location/" + this.searchRestaurant.zipcode.replace(/\s/g, "");
  }

  formatPostcode(event: any) {
    let input = event.target.value.toUpperCase().replace(/\s+/g, '');
    if (input.length > 3 && input.length <= 7) {
      input = input.slice(0, -3) + ' ' + input.slice(-3);
    }
    this.searchRestaurant.zipcode = input;
  }

  restaurantFilter(event) {
    this.restaurants = Object.assign([], this.originalRestaurants);

    if (event) {
      this.filters.sortBy = event;
    }
    if (this.filters.sortBy) {
      if (this.filters.sortBy == 'new') {
        let filteredArray = [];
        this.restaurants.forEach(restaurant => {
          if (restaurant?.new == '1') {
            filteredArray.push(restaurant);
          }
        });
        this.restaurants = Object.assign([], filteredArray);
      }

      if (this.filters.sortBy == 'average_rating') {
        let filteredArray = [];
        filteredArray = Object.assign([], this.restaurants);
        filteredArray.sort((a, b) => b.average_rating - a.average_rating);
        this.restaurants = Object.assign([], filteredArray);
      }

      if (this.filters.sortBy == 'distance') {
        let filteredArray = [];
        filteredArray = Object.assign([], this.restaurants);
        filteredArray.sort((a, b) => a.distance - b.distance);
        this.restaurants = Object.assign([], filteredArray);
      }

      if (this.filters.sortBy == 'minimum_order') {
        let filteredArray = [];
        filteredArray = Object.assign([], this.restaurants);
        filteredArray.sort((a, b) => a.minimum_order - b.minimum_order);
        this.restaurants = Object.assign([], filteredArray);
      }

      if (this.filters.sortBy == 'estimate_time') {
        let filteredArray = [];
        filteredArray = Object.assign([], this.restaurants);
        filteredArray.sort((a, b) => a.estimate_time - b.estimate_time);
        this.restaurants = Object.assign([], filteredArray);
      }
    }

    if (this.filters.fastDelivery) {
      let filteredArray = [];
      filteredArray = Object.assign([], this.restaurants);
      filteredArray.sort((a, b) => a.estimate_time.localeCompare(b.estimate_time));
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.rating.threestar) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.average_rating >= 3) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.rating.fourstar) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (!this.filters.rating.threestar && restaurant?.average_rating >= 4) {
          filteredArray.push(restaurant);
        }
        else if (this.filters.rating.threestar && restaurant?.average_rating >= 3) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.rating.fivestar) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (this.filters.rating.threestar && !this.filters.rating.fourstar && restaurant?.average_rating >= 3) {
          filteredArray.push(restaurant);
        }
        else if (this.filters.rating.threestar && this.filters.rating.fourstar && restaurant?.average_rating >= 3) {
          filteredArray.push(restaurant);
        }
        else if (!this.filters.rating.threestar && this.filters.rating.fourstar && restaurant?.average_rating >= 4) {
          filteredArray.push(restaurant);
        }
        else if (!this.filters.rating.threestar && !this.filters.rating.fourstar && restaurant?.average_rating >= 5) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.deliveryFee.one) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.deliveryCharges <= 1) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.deliveryFee.two) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.deliveryCharges <= 2) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.deliveryFee.five) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.deliveryCharges <= 5) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.deliveryFee.ten) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.deliveryCharges <= 10) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.offer) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant?.offer_texts.length > 0) {
          filteredArray.push(restaurant);
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    if (this.filters.dietary) {
      let filteredArray = [];
      this.restaurants.forEach(restaurant => {
        if (restaurant.restaurant_dietary.includes(this.filters.dietary)) {
          filteredArray.push(Object.assign({}, restaurant));
        }
      });
      this.restaurants = Object.assign([], filteredArray);
    }

    this.closeFilter();
  }

  clearFilter() {
    this.filters = {
      fastDelivery: false,
      rating: {
        threestar: false,
        fourstar: false,
        fivestar: false,
      },
      deliveryFee: {
        one: false,
        two: false,
        five: false,
        ten: false,
      },
      sortBy: null,
      offer: false,
      dietary: null,
    };
    this.restaurants = Object.assign([], this.originalRestaurants);
    this.closeFilter();
  }

  filterCuisines(cuisine: Cuisines) {
    const cus = this.selected_array.find(cu => cu.id === cuisine.id);
    this.restaurants = this.originalRestaurants

    if (!cus) {
      this.selected_array.push(cuisine);
      const selectedIds = this.selected_array.map(({ id }) => id);
      this.cuisines.forEach(cuisi => {
        if (cuisi.id == cuisine.id) {
          cuisi.selected = true;
        }
      });

      let filteredArray = []
      selectedIds.forEach(val => {
        const filteredArray1 = this.restaurants.filter(value => value.restaurant_cuisine.includes(val));
        filteredArray1.forEach(filteArr => {
          const fillRest = filteredArray.find(rest => rest.id === filteArr.id);
          if (!fillRest) {
            filteredArray.push(filteArr);
          }
        });
      });

      this.restaurants = Object.assign([], filteredArray);
    } else {
      const index = this.selected_array.indexOf(cus);
      this.selected_array.splice(index, 1);
      this.cuisines.forEach(cuisi => {
        if (cuisi.id == cuisine.id) {
          cuisi.selected = false;
        }
      });
      const selectedIds = this.selected_array.map(({ id }) => id);
      let filteredArray = []
      selectedIds.forEach(val => {
        const filteredArray1 = this.restaurants.filter(value => value.restaurant_cuisine.includes(val));
        filteredArray1.forEach(filteArr => {
          const fillRest = filteredArray.find(rest => rest.id === filteArr.id);
          if (!fillRest) {
            filteredArray.push(filteArr);
          }
        });
      });
      this.restaurants = Object.assign([], filteredArray);
    }


    // Search Clear
    this.searchData = '';
    // Filter Clear
    this.filters = {
      fastDelivery: false,
      rating: {
        threestar: false,
        fourstar: false,
        fivestar: false,
      },
      deliveryFee: {
        one: false,
        two: false,
        five: false,
        ten: false,
      },
      sortBy: null,
      offer: false,
      dietary: null,
    };

    if (this.selected_array.length <= 0) {
      this.restaurants = this.originalRestaurants
    }
  }

  checkFav(id) {
    return this.favRestaurants.includes(id);
  }

  favourite(favourite: boolean, restaurant: Restaurant) {
    this.restaurant.restaurant_id = restaurant.id;
    this.restaurant.user_id = this.loginUser.id;
    this.restaurant.is_favourite = favourite;
    this.restaurantService.favourite(this.restaurant)
      .pipe(finalize(() => { }))
      .subscribe(
        (res) => {
          this.fetchRestaurants();
        },
        (err) => { }
      );
  }

  clearSearch() {
    this.searchData = null;
    this.restaurantService.searchResturant(this.searchData);
  }

  search(search: string) {
    this.selected_array = [];
    if (!search) {
      this.searchData = '';
      this.restaurants = Object.assign([], this.originalRestaurants);
    } else {
      search = search.trim();
      // Cuisines Clear
      this.cuisines.forEach(cuisi => {
        cuisi.selected = false;
      });
      this.selected_array = [];
      // Filter Clear
      this.filters = {
        fastDelivery: false,
        rating: {
          threestar: false,
          fourstar: false,
          fivestar: false,
        },
        deliveryFee: {
          one: false,
          two: false,
          five: false,
          ten: false,
        },
        sortBy: null,
        offer: false,
        dietary: null,
      };
      // search = search.replace(/\s/g, "");
      this.restaurants = Object.assign([], this.originalRestaurants);
      let filteredArray = []
      this.restaurants.forEach(restaurant => {
        if (restaurant?.restaurant_name?.toLocaleLowerCase().includes(search.toLocaleLowerCase())) {
          filteredArray.push(Object.assign({}, restaurant));
          try {
            if (window.innerWidth <= 1199) {
              this.document.getElementById("allStore").scrollIntoView({ behavior: 'smooth' });
            }
          } catch (e) { }
        }
      });
      this.restaurants = Object.assign([], filteredArray);
      return;
    }
  }

  redirectRestaurant(restaurant) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, restaurant.id);
    }
    this.router.navigateByUrl('/' + restaurant.city_name + '/' + restaurant.seo_url + '/menus');
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  showFilter() {
    this.renderer.addClass(this.document.body, 'filter-open');
  }

  closeFilter() {
    this.renderer.removeClass(this.document.body, 'filter-open');

    // Search Clear
    this.searchData = '';
    // Cuisines Clear
    this.cuisines.forEach(cuisi => {
      cuisi.selected = false;
    });
    this.selected_array = [];
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.shellComponent.clearSearch();
    this.subs.unsubscribe();
  }
}
