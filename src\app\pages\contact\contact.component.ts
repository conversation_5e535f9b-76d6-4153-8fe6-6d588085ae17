import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { environment } from '../../../environments/environment';
import { Contact } from '../../core/models/contact';
import { ContactService } from '../../core/services/contact.service';
import { NotificationService } from '../../core/services/notification.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-contact',
  host: { ngSkipHydration: 'true' },
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss']
})

export class ContactComponent implements OnInit {
  @ViewChild('otpModal') otpModal: ElementRef;

  private subs = new Subscription();

  contact: Contact = new Contact();
  contactForm: NgForm;

  isLoading = false; error = null;
  isOtpLoading = false; Otperror = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private contactService: ContactService,
    public modalService: NgbModal,
    private notificationService: NotificationService,
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() { }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.createContact(form);
  }

  createContact(form: NgForm) {
    this.isLoading = true; this.error = null;

    this.contact.name = this.contact.first_name + ' ' + this.contact.last_name;

    this.subs.add(this.contactService.create(this.contact)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.contact = res;
        this.contactForm = form;
        this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', backdrop: 'static' });
        this.notificationService.showSuccess("Otp send successfully!", "Gogrubz")
      }, err => this.error = err)
    );
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isOtpLoading = true; this.Otperror = null;
    this.subs.add(
      this.contactService.verify(this.contact).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.contactForm.reset();
            this.modalService.dismissAll();
            this.notificationService.showSuccess("Thank you for contacting us.", "Gogrubz")
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.isOtpLoading = false; this.Otperror = false;

    this.subs.add(
      this.contactService.resend(this.contact).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("otp sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    // || event.keyCode == 32 
    if (event.keyCode == 189 || event.keyCode == 9 || event.keyCode == 8 || event.keyCode == 48 || (event.keyCode >= 97 && event.keyCode <= 122) || (event.keyCode >= 65 && event.keyCode <= 90)) {
    } else {
      event.preventDefault();
    }
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); }
}
