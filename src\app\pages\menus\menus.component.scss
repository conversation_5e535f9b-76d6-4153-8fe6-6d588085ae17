$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.delivery-pick-up-list {
  display: flex;
  padding: 5px;
  background-color: #F4F3F3;
  border-radius: 50px;
}

.delivery-pick-up-list li {
  width: 100%;
  padding: 14px 15px 9px 15px;
  text-align: center;
  border-radius: 50px;
}

.delivery-pick-up-list li p {
  font-family: 'Fredoka';
  font-size: 20px;
  color: #000000;
  font-weight: 500;
  line-height: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.delivery-pick-up-list li span {
  font-family: 'Fredoka';
  font-size: 12px;
  font-weight: 500;
  line-height: normal;
  color: #000000;
}

.delivery-pick-up-list li.active {
  background-color: #EA3323;
}

.delivery-pick-up-list li.active p {
  color: #fff;
}

.delivery-pick-up-list li.active span {
  color: #fff;
}

.delivery-pick-up-list li.disabled {
  background-color: #C0C0C0;
}

.delivery-pick-up-list li.disabled span,
.delivery-pick-up-list li.disabled p {
  color: #fff;
}

.order-free-delivery-list {
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.order-free-delivery-list li {
  font-family: 'Visby CF';
  font-size: 12px;
  color: #000000;
  font-weight: 700;
  margin: 0 16px;
}

.order-free-delivery-list li img {
  margin-right: 7px;
}

.delivery-pick-up-list li {
  cursor: pointer;
}

.menu-category-list {
  margin-top: 45px;
  padding-bottom: 30px;
  min-height: 165px;
}

.menu-category-list .menu-section {
  padding: 15px 0 8px 0;
  background-color: #fff;
  border-top: 2px solid #f4f3f3;
}

.menu-section .search-for-restaurant input.form-control {
  font-size: 16px;
  width: 355px;
  height: 36px;
  padding-right: 40px;
}

.menu-section .search-for-restaurant svg.search-close {
  position: absolute;
  right: 15px;
  top: 18px;
  left: auto;
  font-size: 14px !important;
}

.menu-top-line {
  padding-top: 20px;
  border-top: 2px solid #F4F3F3;
}

.menu-section h5.menu-title {
  font-size: 24px;
  font-family: 'Visby CF';
  font-weight: 800;
}

.menu-category-list.fixed .menu-section {
  position: fixed;
  top: 0px;
  left: 0;
  right: 0;
  margin: 0;
  width: 100%;
  padding: 15px 20px 8px 20px;
  z-index: 123;
  background-color: #fff;
  box-shadow: 0 2px 3px 0 #e1e1e1;
}

.menu-category-list.fixed .category-inner-section {
  max-width: 1718px;
  margin: auto;
}

.menu-category-list.fixed .menu-section .row,
.menu-category-list.fixed .menu-section .menu-inner-sec {
  max-width: 1302px;
}

.menu-category-list.fixed .menu-section .menu-top-line {
  display: none;
}

.menu-category-list.fixed .menu-inner-sec .menu-list {
  padding-right: 84px;
}

.menu-inner-sec {
  display: flex;
  justify-content: space-between;
}

.menu-list-arrow {
  padding-top: 3px;
  position: relative;
}

.menu-list-arrow button img {
  width: 26px;
}

.view-menu-btn {
  cursor: pointer;
  position: relative;
  z-index: 99;
  padding:0;
  border: 0;
  background-color:transparent;
}

.view-menu-dropdown-bg {
  position: absolute;
  z-index: 99;
  left: 55px;
  top: -10px;
}

.view-menu-dropdown-bg.show {
  display: block;
}

.view-menu-dropdown-bg::after {
  position: absolute;
  top: 10px;
  left: -24px;
  content: '';
  width: 34px;
  height: 32px;
  background: url(/assets/images/left-shape.svg) no-repeat top center;
  z-index: 1;
}

.view-menu-dropdown {
  width: 205px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 0px #00000040;
  border-radius: 15px;
  position: relative;
  z-index: 2;
}

.view-menu-title {
  padding: 10px 20px 20px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.view-menu-title p {
  font-family: 'Visby CF';
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 0;
}

.view-menu-title button.close {
  font-family: 'Fredoka';
  font-size: 12px;
  color: #000000;
  font-weight: 500;
  line-height: 22px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  text-align: center;
  background-color: #D9D9D9;
  border: 0;
  padding: 0;
}

.view-menu-dropdown .view-menu-list {
  max-height: 420px;
  overflow: hidden;
  overflow-y: auto;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar {
  width: 5px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-track {
  background: #ffe6e7;
  border-radius: 10px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-thumb {
  background: $primary;
  border-radius: 10px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-thumb:hover {
  background: $primary;
}

.view-menu-list ul {
  margin-bottom: 0;
}

.view-menu-list ul li {
  padding-bottom: 16px;
}

.view-menu-list ul li button{
  font-family: 'Visby CF';
  font-size: 15px;
  color: #000000;
  cursor: pointer;
  text-decoration: none;
  font-weight: 700;
  padding: 7px 20px 7px 25px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: relative;
  background-color: transparent;
  border:0;
}

.view-menu-list ul li button::before {
  content: '';
  position: absolute;
  width: 7px;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: #fc353a;
  border-radius: 0 10px 10px 0;
  display: none;
}

.view-menu-list ul li button:hover::before,
.view-menu-list ul li.active button::before {
  display: block;
}

.view-menu-list ul li.active button{
  color: #fc353a;
}

.menu-inner-sec .menu-list {
  width: 100%;
  padding: 0 50px;
  margin-bottom: 0;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow {
  top: 18px;
  line-height: 20px;
  background-color: transparent;
  box-shadow: none;
  margin: 0;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-prev {
  left: -30px;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
  right: -40px;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-disabled {
  display: none !important;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow::before {
  opacity: 1;
  color: #000;
}

.menu-inner-sec .menu-list li {
  text-align: center;
  padding: 0 30px;
}

.menu-inner-sec .menu-list li button {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  cursor: pointer;
  white-space: nowrap;
  line-height: 25px;
  text-align: center;
  display: inline-block;
  text-decoration: none;
  padding-bottom: 20px;
  position: relative;
  background-color: transparent;
  border:0;
}

.menu-inner-sec .menu-list li button::before {
  width: 100%;
  display: inline-block;
  position: absolute;
  content: '';
  height: 7px;
  bottom: 0;
  border-radius: 8px 8px 0 0;
  background-color: #fc353a;
  display: none;
}

.menu-inner-sec .menu-list li.active button {
  color: #fc353a;
}

.menu-inner-sec .menu-list li button:hover::before,
.menu-inner-sec .menu-list li.active button::before {
  display: block;
}

.menu-inner-sec {
  padding-top: 10px;
  border-bottom: 2px solid #F4F3F3;
}

.menu-inner-sec .left-arrow {
  padding-top: 3px;
  padding-left: 35px;
  cursor: pointer;
  display: none;
}

.menu-inner-sec .left-arrow.slick-disabled {
  display: none;
}

.menu-inner-sec .left-arrow button {
  font-size: 18px;
  color: #000000;
  padding:0;
  background-color: transparent;
  border:0;
}

.menu-inner-sec .right-arrow {
  padding-top: 3px;
  padding-left: 15px;
  cursor: pointer;
  display: none;
}

.menu-inner-sec .right-arrow.slick-disabled {
  display: none;
}

.menu-inner-sec .right-arrow button {
  font-size: 18px;
  color: #000000;
  padding:0;
  background-color: transparent;
  border:0;
}

.customer-favourites-section .main-heading {
  margin-bottom: 32px;
}

.customer-favourites-section .main-heading h6 {
  font-size: 30px;
  font-family: 'Visby CF';
  font-weight: 800;
}

.favourite-box {
  margin-bottom: 30px;
}

.favourite-box .favourite-image {
  position: relative;
}

.favourite-box .favourite-image img {
  width: 100%;
  height: 193px;
  object-fit: cover;
  border-radius: 10px;
}

.horizontal-favourite-box .horizontal-image-box button.add-option,
.favourite-box .favourite-image button.add-option {
  color: #000;
  font-size: 15px;
  font-weight: 800;
  display: inline-block;
  text-decoration: none;
  line-height: 28px;
  cursor: pointer;
  width: 60px;
  height: 30px;
  line-height: 28px;
  background-color: #fff;
  border-radius: 25px;
  text-align: center;
  position: absolute;
  right: 12px;
  bottom: 10px;
  box-shadow: 0px 0px 7px 2px #00000026;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border:0;
}

.favourite-box .favourite-content {
  padding-top: 8px;
}

.horizontal-favourite-box .horizontal-content-box h6,
.favourite-box .favourite-content h6 {
  font-family: 'Visby CF';
  font-size: 25px;
  font-weight: 800;
  line-height: 30px;
  margin-bottom: 10px;
}

.favourite-box .favourite-content h6 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.horizontal-favourite-box .horizontal-content-box span.price,
.favourite-box .favourite-content span.price {
  font-family: 'Visby CF';
  font-size: 20px;
  color: #000;
  font-weight: 600;
  padding-bottom: 5px;
  display: flex;
  align-items: center;
}

.horizontal-favourite-box .horizontal-content-box p,
.favourite-box .favourite-content p {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #8B8F8F;
  line-height: 24px;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.horizontal-favourite-box {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  box-shadow: 0px 0px 4px 4px #0000000D;
  margin-bottom: 35px;
}

.horizontal-favourite-box .horizontal-content-box {
  width:calc(100% - 150px);
  padding: 24px 30px;
}

.horizontal-favourite-box .horizontal-content-box h6 {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 6px;
}

.horizontal-favourite-box .horizontal-image-box {
  width: 150px;
  min-width: 150px;
  position: relative;
}

.horizontal-favourite-box .horizontal-image-box img {
  width: 100%;
  height: 100%;
  border-radius: 0 10px 10px 0;
  object-fit: cover;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

.horizontal-favourite-box .horizontal-image-box a.add-option {
  right: 28px;
  bottom: 17px;
}

/*---------------------------------------------
         Product-Multiple-Options-Popup
---------------------------------------------*/
.product-header {
  padding: 15px 25px;
}

.product-close {
  position: initial !important;
}

.product-body {
  padding: 20px 25px;
}

.product-body h6 {
  text-align: center;
  margin-bottom: 12px;
}

.product-body p {
  font-size: 16px;
  text-align: center;
}

.popup-product-box {
  padding-bottom: 15px;
}

.product-body .product-image {
  text-align: center;
}

.product-body .product-image img {
  width: 90px;
  border-radius: 5px;
}

.product-body .choose-options h6 {
  font-size: 22px;
  margin-bottom: 0;
  text-align: left;
}

.required-select {
  display: flex;
  align-items: center;
}

.required-select .required {
  font-size: 14px;
  color: #ff0000;
  font-weight: 600;
}

.required-select .required svg {
  margin-right: 5px;
}

.required-select svg.fa-circle {
  color: #8F8F8A;
  font-size: 3px;
  margin: 0 6px;
}

.required-select span {
  color: #8F8F8A;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Visby CF';
}

.choose-options .choose-list {
  margin-bottom: 0;
}

.choose-options .choose-list li .form-check {
  cursor: pointer;
  position: relative;
  padding: 0px;
  margin-bottom: 0;
  display: flex;
  border-bottom: 2px solid #F8F7F7;
}

.choose-options .choose-list li .form-check label {
  width: 100%;
  padding: 0 45px;
}

.choose-options .choose-list li .form-check .burger-name {
  font-size: 16px;
  color: #202020;
  font-weight: bold;
}

.choose-options .choose-list li .form-check input.form-check-input {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 0;
  margin: 0;
}

.choose-options .choose-list li .form-check .form-check-label {
  padding: 10px 0;
}

.extra-addon-list li {
  font-size: 13px;
  color: #202020;
  display: inline-block;
  font-weight: 500;
  font-family: 'Visby CF';
}

.extra-addon-list li svg.fa-circle {
  font-size: 5px;
  margin: 0 5px 0 3px;
}

.choose-options .choose-list li .form-check .price {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #8B8F8F;
  display: block;
  padding-bottom: 5px;
}

.choose-options .choose-list li .form-check .edit-section span {
  font-size: 14px;
  color: #000;
  font-weight: 600;
  padding: 4px 15px 5px 15px;
  background-color: #f9f9f9;
  border: 1px solid #f4f3f3;
  border-radius: 20px;
}

.choose-options .choose-list li .form-check .right-arrow {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
}

.choose-options .choose-list li .form-check .right-arrow svg {
  font-size: 15px;
  color: #202020;
}

.product-footer {
  padding: 0 25px 25px 25px;
}

.product-footer button.btn.btn-primary {
  width: 100%;
}



/*----------Addon-Popup----------*/
#addonpopup .modal-header {
  padding: 15px 75px;
}

#addonpopup .modal-header button.btn-close {
  top: 24px;
  left: 22px;
}

#addonpopup .modal-header h6 {
  font-size: 24px;
  line-height: normal;
  margin-bottom: 0;
}

#addonpopup .modal-body {
  padding: 20px 25px 25px 25px;
}

#addonpopup .addon-list h6 {
  font-size: 24px;
  margin-bottom: 0;
}

#addonpopup .addon-list ul {
  padding: 10px 0 30px 0;
  margin-bottom: 0;
}

#addonpopup .addon-list ul li {
  border-bottom: 2px solid #F8F7F7;
}

#addonpopup .addon-list ul li .form-check {
  position: relative;
  display: flex;
  padding-left: 0px;
  margin-bottom: 0;
}

#addonpopup .form-check input.form-check-input {
  margin-top: 14px;
}

#addonpopup .addon-list ul li .form-check .form-check-label {
  color: #202020;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  padding: 11px 0;
}

#addonpopup button.btn {
  width: 100%;
  font-size: 18px;
  padding: 9px 15px;
}

.form-check input.form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #202020;
  margin-left: 0;
  margin-right: 10px;
}

.form-check input.form-check-input.radio-button {
  border-radius: 50%;
}

.favourite-icon {
  position: absolute;
  top: 32px;
  right: 45px;
  z-index: 2;
}

.favourite-icon button {
  padding: 0;
  border: 0;
  border-radius: 0;
  background-color: transparent;
}

.favourite-icon button img {
  width: 60px;
}

.make-reservation-btn {
  width: 220px;
  height: 50px;
  line-height: 30px;
  color: #000 !important;
  font-family: 'Visby CF';
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  position: absolute;
  right: 55px;
  bottom: 32px;
  border-color: #fff !important;
  background-color: #ffffff !important;
  box-shadow: 0px 4px 8px 3px #00000040;
}

.make-reservation-btn:hover {
  color: #ea3323 !important;
  border-color: #ea3323 !important;
}

.see-all-review {
  text-align: right;
}

.see-all-review a {
  font-family: 'Visby CF';
  font-size: 14px;
  font-weight: 700;
  color: #000000;
  cursor: pointer;
  width: 170px;
  text-align: center;
  background-color: #F4F3F3;
  padding: 7px 15px 7px 15px;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
}

.see-all-review a svg {
  font-size: 16px;
  margin-left: 20px;
}

.restaurant-status-title-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.restaurant-status-title-bg h5 {
  color: #fff;
  text-align: center;
  font-family: 'Visby CF';
  font-size: 40px;
  font-weight: 700;
  line-height: 20px;
  margin-bottom: 0;
}

.allergy-info {
  text-align: right;
}

.allergy-info button {
  padding:0;
  font-family: 'Visby CF';
  font-weight: 700;
  line-height: 22px;
  font-size: 16px;
  color: #000000;
  text-decoration: none;
  background-color: transparent;
  border:0;  
}

.allergy-info button img {
  width:18px;
  position: relative;  
  margin-right: 8px;
}

#allergy-popup button.modal-black-btn {
  width: 180px;
  margin: auto;
  display: table;
  height: 46px;
  padding: 5px 10px;
}

#allergy-popup .modal-body p {
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 20px;
}

.product-counter {
  font-size: 13px;
  color: #fff;
  font-weight: 600;
  width: 26px;
  height: 26px;
  line-height: 24px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  background-color: #000;
  position: absolute;
  top: -7px;
  right: -7px;
  z-index: 1;
  box-shadow: 0 0 2px 1px #fbfbfb;
}

#items-already-in-cart-popup .modal-body p {
  color: #000000;
}

#items-already-in-cart-popup .modal-body button.btn {
  padding: 10px 20px;
  font-size: 18px;
  margin: 0 15px;
}

.add-items {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  display: table;
  bottom: 10px;
  z-index: 100;
}

.add-items button.btn {
  font-size: 16px;
  line-height: 24px;
  padding: 8px 15px;
  border-radius: 30px;
  box-shadow: 0 0 10px 2px #b9b9b9;
  display: flex;
  justify-content: space-between;
  width: 650px;
}

.add-items button.btn svg {
  margin-left: 2px;
}

.add-items button.btn:hover {
  background-color: #fff !important;
}

.addon-items ul {
  margin-bottom: 0;
}

.addon-items ul li {
  font-family: 'Visby CF';
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  line-height: 22px;
  display: inline-block;
  margin-right: 5px;
}

.addon-items ul li img {
  width: 17px;
  margin-top: -3px;
}

#opening-times {
  padding-top: 0;
}

#opening-times .nav.nav-tabs {
  margin: 10px 0;
  border: 0;
  flex-wrap: nowrap;
  padding: 5px;
  border-radius: 50px;
  background-color: #f4f3f3;
}

#opening-times .nav.nav-tabs li {
  width: 100%;
  margin: 0;
}

#opening-times .nav.nav-tabs li button {
  font-size: 16px;
  padding: 9px 15px;
  font-weight: 800;
  border: 0;
  border-radius: 25px;
  width: 100%;
  text-align: center;
}

#opening-times .nav.nav-tabs li.active button{
  background-color: #ea3323;
  color: #fff;
}

.edit-addon-btn {
  padding: 0;
  height: 20px;
  width: 28px;
  font-size: 10px;
  line-height: 18px;
  background-color: #fc353a !important;
  border: 1px solid #fc353a !important;
  margin-left: 15px;
  color: #fff;
  border-radius: 5px;
  position: relative;
  top: 3px;
  box-shadow: none !important;
  font-weight: 700;
}

.veg-nonveg {
  width: 16px;
  height: 16px;
  position: relative;
  border: 2px solid #3ab54a;
  margin-right: 8px;
}

.veg-nonveg::before {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3ab54a;
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  margin: auto;
}

.veg-nonveg.noneveg-bg {
  border-color: #fe0000;
}

.veg-nonveg.noneveg-bg::before {
  background-color: #fe0000;
}

.subaddon-circle {
  margin: 0 2px 0 5px;
  font-size: 5px !important;
  color: #8f8f8a;
  vertical-align: middle;
}

.price-circle {
  font-size: 4px !important;
  color: #fff;
  vertical-align: middle;
}

.price-circle svg {
  margin: 0px !important;
  transition: all .5s;
}

#addonpopup button.btn:hover .price-circle {
  color: #000;
  opacity: 0.7;
}
.spicy-list-images{
  margin:10px 0 0 0;
 }
 .spicy-list-images li{
  display: inline-block;
  margin-right:10px;
 }
 .spicy-list-images li img{
  width: 20px;
 } 
 .reviews-rating-detail .reviews-show a{
  color: #111;
 }

@media screen and (max-width:1800px) {
  .menu-category-list.fixed .menu-section {
    padding: 15px 42px 8px;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 416px);
  }
  .menu-inner-sec .menu-list li button{
    font-size: 15px;
  }

}

@media screen and (max-width:1750px) {
  .delivery-pick-up-list li {
    padding: 12px 10px 5px 10px;
  }

  .delivery-pick-up-list li p {
    font-size: 18px;
  }

  .order-free-delivery-list li {
    font-size: 11px;
    margin: 0 4px;
  }

  .order-free-delivery-list li img {
    margin-right: 5px;
    width: 22px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 30px 8px;
  }

  .menu-category-list.fixed .category-inner-section {
    max-width: 100%;
    width: 100%;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 406px);
  }

  .menu-top-line {
    padding-top: 15px;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 16px;
    width: 305px;
  }

  .favourite-box .favourite-image img {
    height: 163px;
  }

  .favourite-box .favourite-content h6 {
    font-size: 19px;
    margin-bottom: 2px;
  }

  .favourite-box .favourite-content span.price {
    font-size: 16px;
    padding-bottom: 2px;
  }

  .favourite-box .favourite-content p {
    font-size: 14px;
    line-height: 22px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 20px 25px;
  }

  .horizontal-favourite-box .horizontal-content-box h6 {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .horizontal-favourite-box .horizontal-content-box span.price {
    font-size: 16px;
    padding-bottom: 4px;
  }

  .horizontal-favourite-box .horizontal-content-box p {
    font-size: 14px;
    line-height: 20px;
  }

}

@media screen and (max-width:1500px) {
  .favourite-icon {
    top: 20px;
    right: 32px;
  }

  .favourite-icon button img {
    width: 50px;
  }

  .see-all-review a {
    font-size: 10px;
    padding: 6px 10px;
    width: 127px;
  }

  .see-all-review a svg {
    font-size: 10px;
    position: relative;
    top: 1px;
  }

  .make-reservation-btn {
    font-size: 13px;
    width: 165px;
    height: 37px;
    line-height: 24px;
    right: 40px;
    bottom: 24px;
  }

  .delivery-pick-up-list {
    padding: 3px 4px;
    margin-bottom: 10px;
  }

  .delivery-pick-up-list li {
    padding: 8px 5px 5px;
    line-height: normal;
  }

  .delivery-pick-up-list li p {
    font-size: 15px;
  }

  .order-free-delivery-list li img {
    width: 20px;
    position: relative;
    top: -2px;
  }

  .menu-category-list {
    margin-top: 35px;
    padding-bottom: 25px;
    min-height: 145px;
  }

  .menu-top-line {
    padding-top: 20px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 10px 120px 8px;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 306px);
    padding-top: 5px;
  }

  .menu-section h5.menu-title {
    font-size: 22px;
    margin: 0;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 13px;
    width: 270px;
    height: 30px;
    padding: 0 35px 0 35px;
  }

  .menu-section .search-for-restaurant svg {
    font-size: 13px;
    left: 12px;
  }

  .menu-list-arrow button img {
    width: 20px;
  }

  .menu-inner-sec .menu-list {
    padding: 0 48px 0 50px;
  }

  .menu-inner-sec .menu-list li button {
    font-size: 14px;
    padding-bottom: 12px;
  }

  .menu-inner-sec .menu-list li button:before {
    height: 6px;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec .menu-list li a:before {
    height: 5px;
  }

  ::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -30px;
  }

  .customer-favourites-section .main-heading {
    margin-bottom: 20px;
  }

  .customer-favourites-section .main-heading h6 {
    font-size: 22px;
  }

  .allergy-info button {
    font-size: 14px;
  }

  .allergy-info button img {    
    margin-right: 8px;
  }

  .favourite-box .favourite-image img {
    height: 135px;
  }

  .favourite-box .favourite-content h6 {
    font-size: 18px;
    line-height: 22px;
  }

  .favourite-box .favourite-content span.price {
    font-size: 15px;
    padding-bottom: 0;
  }

  .favourite-box .favourite-content p {
    font-size: 12px;
  }

  .horizontal-favourite-box {
    margin-bottom: 25px;
  }

  .horizontal-favourite-box .horizontal-image-box {
    width: 112px;
    min-width: 112px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 16px 20px;
    max-width: calc(100% - 112px);
    width: 100%;
  }

  .horizontal-favourite-box .horizontal-content-box h6 {
    font-size: 18px;
    margin-bottom: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box span.price {
    font-size: 15px;
    padding-bottom: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul {
    line-height: 20px;
  }

  .favourite-box .favourite-image button.add-option,
  .horizontal-favourite-box .horizontal-image-box button.add-option {
    font-size: 12px;
    width: 45px;
    height: 22px;
    line-height: 20px;
  }

  .favourite-box .favourite-image button.add-option {
    right: 8px;
    bottom: 8px;
  }

  .horizontal-favourite-box .horizontal-image-box button.add-option {
    right: 20px;
    bottom: 10px;
  }

  .addon-items ul li {
    font-size: 12px;
  }

  .addon-items ul li img {
    width: 14px;
    margin-top: 0px;
  }

  .view-menu-dropdown-bg {
    left: 38px;
    top: 1px;
  }

  .view-menu-dropdown-bg:after {
    top: 7px;
    left: -13px;
    width: 16px;
    height: 19px;
    background-size: 100%;
  }

  .view-menu-dropdown {
    width: 165px;
    box-shadow: 0px 0px 3px 0px #00000040;
    border-radius: 8px;
  }

  .view-menu-title {
    padding: 9px 14px 18px 18px;
  }

  .view-menu-title p {
    font-size: 14px;
    line-height: 22px;
  }

  .view-menu-title button.close {
    font-size: 10px;
    line-height: 15px;
    width: 15px;
    height: 15px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .view-menu-list ul li {
    padding-bottom: 12px;
  }

  .view-menu-list ul li {
    padding-bottom: 15px;
  }

  .view-menu-list ul li button {
    font-size: 12px;
    padding: 5px 20px;
  }

  .view-menu-list ul li button:before {
    width: 5px;
  }

  #addonpopup .modal-header {
    padding: 10px 65px;
  }

  #addonpopup .modal-header button.btn-close {
    left: 20px;
  }

  #addonpopup .modal-header h6 {
    font-size: 20px;
  }

  #addonpopup .modal-header span {
    font-size: 14px;
  }

  #addonpopup .modal-body {
    padding: 20px 20px 20px;
  }

  #addonpopup .addon-list h6 {
    font-size: 20px;
  }

  #addonpopup .addon-list h6 span {
    font-size: 13px;
  }

  #addonpopup .addon-list ul {
    padding: 0px 0 20px;
  }

  #addonpopup .addon-list ul li .form-check .form-check-label {
    font-size: 14px;
    padding: 10px 0;
  }

  #addonpopup .form-check input.form-check-input {
    width: 16px;
    height: 16px;
    margin-top: 14px;
  }

  #addonpopup button.btn {
    font-size: 14px;
    padding: 5px 15px;
  }

  .product-header {
    padding: 10px 20px;
  }

  .product-body {
    padding: 20px 20px;
  }

  .product-body h6 {
    font-size: 22px;
    margin-bottom: 5px;
  }

  .product-body .choose-options h6 {
    margin-bottom: 10px;
  }

  .choose-options .choose-list li .form-check .form-check-label {
    padding: 0;
  }

  .choose-options .choose-list li .form-check label {
    padding: 0 30px;
  }

  .choose-options .choose-list li .form-check input.form-check-input {
    width: 16px;
    height: 16px;
  }

  .choose-options .choose-list li .form-check .right-arrow svg {
    font-size: 12px;
  }

  #allergy-popup .modal-body p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 30px;
  }

  #allergy-popup button.modal-black-btn {
    width: 160px;
    height: 36px;
    font-size: 14px;
  }

  #allergy-popup button.modal-black-btn svg {
    font-size: 14px;
  }

  #items-already-in-cart-popup .modal-body p {
    margin-bottom: 15px;
  }

  #items-already-in-cart-popup .modal-body button.btn {
    font-size: 14px;
    margin: 0 5px;
    padding: 5px 20px;
  }

  #opening-times .nav.nav-tabs li button {
    font-size: 14px;
    padding: 7px 10px;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 345px;
  }

  .veg-nonveg {
    width: 12px;
    height: 12px;
    border: 1px solid #3ab54a;
    margin-right: 5px;
  }

  .veg-nonveg:before {
    width: 6px;
    height: 6px;
  }

  .menu-section .search-for-restaurant svg.search-close {
    top: 15px;
    font-size: 12px !important;
  }
  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 74px;
  }

}

@media screen and (max-width:1330px) {
  .favourite-box .favourite-content h6 {
    font-size: 16px;
  }

}

@media screen and (max-width:1300px) {
  .order-free-delivery-list li {
    font-size: 10px;
    margin: 0 2px;
  }

  .order-free-delivery-list li img {
    width: 18px;
    top: 1px;
    margin-right: 2px;
  }

  .favourite-box .favourite-image img {
    height: 120px;
  }

}

@media screen and (max-width:1199px) {

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: 100%;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 35px 8px 60px;
  }

  .order-free-delivery-list li {
    margin: 0 5px;
  }

  .favourite-icon {
    top: 15px;
    right: 20px;
  }

}

@media screen and (max-width:991px) {
  .menu-top-line {
    padding-top: 15px;
  }

  .delivery-pick-up-box {
    margin-bottom: 15px;
  }

  .menu-category-list {
    margin-top: 20px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 25px 8px 50px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 10px 15px;
  }

  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 73px;
  }

  .addon-items ul li {
    font-size: 11px;
    margin-right: 3px;
  }

  .addon-items ul li img {
    width: 11px;
    margin-top: -1px;
  }

  .addon-items ul li:last-child {
    margin-right: 0;
  }

}

@media screen and (max-width:767px) {
  .menu-category-list.fixed .menu-section {
    padding: 15px 7px 8px 30px;
  }

  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 70px;
  }

  .add-items {
    width: 100%;
    padding: 0 30px;
  }

  .add-items button.btn {
    width: 100%;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 300px;
  }

  .delivery-pick-up-list li {
    padding: 9px 5px 7px;
    line-height: 20px;
  }

}

@media screen and (max-width:575px) {
  .favourite-icon button img {
    width: 40px;
  }

  .make-reservation-btn {
    right: 20px;
    bottom: 20px;
  }

  .menu-inner-sec .menu-list {
    padding: 0 30px 0 40px;
    width: calc(100% - 10px);
  }
  .menu-inner-sec .menu-list li{
    padding:0 20px;
  }
  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 53px;
  }

  .menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -20px;
  }

  .restaurant-status-title-bg h5 {
    font-size: 36px;
  }

  .menu-section .search-for-restaurant input.form-control {
    width: 200px;
  }  

}

@media screen and (max-width:480px) {
  .horizontal-favourite-box .horizontal-content-box {
    padding: 12px 15px;
  }

  ::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -25px;
  }

  .menu-inner-sec {
    padding-top: 5px;
  }
  .menu-inner-sec .menu-list li{
    padding:0 15px;
  }
  .menu-category-list.fixed .menu-section .menu-inner-sec {
    padding-top: 5px;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 12px;
    width: 180px;
  }

  .menu-category-list.fixed .menu-section .search-for-restaurant input.form-control {
    font-size: 12px;
    width: 180px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 0px 8px 22px;
  }

  .restaurant-status-title-bg {
    padding: 0 15px;
    border-radius: 15px;
  }

  .restaurant-status-title-bg h5 {
    font-size: 30px;
    line-height: 36px;
  }

  .make-reservation-btn {
    width: 155px;
    height: 35px;
    right: 15px;
    bottom: 15px;
    line-height: 20px;
  }

  .add-items {
    padding: 0 22px;
  }

  .add-items button.btn {
    font-size: 15px;
    padding: 6px 15px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul {
    line-height: 16px;
    margin-top: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul li {
    line-height: 18px;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 260px;
  }

}

@media screen and (max-width:400px) {
  .horizontal-favourite-box .horizontal-content-box h6 {
    line-height: 24px;
  }

  .favourite-box {
    margin-bottom: 20px;
  }

  .customer-favourites-section .main-heading {
    margin-bottom: 15px;
  }

  .customer-favourites-section .main-heading h6 {
    font-size: 20px;
  }
  .spicy-list-images{
    margin-top:5px;
  }
  .spicy-list-images li {
    margin-right: 5px;
  }
  .spicy-list-images li img {
    width: 16px;
  }
  
}
