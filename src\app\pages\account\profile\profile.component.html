<div class="profile-box">
  <form nz-form #profileForm="ngForm" (ngSubmit)="onSubmit(profileForm)" nzLayout="vertical" *ngIf="user">
    <div class="row">

      <div class="col-lg-4 text-center">
        <div class="earn-box">
          <svg class="fa-solid fa-trophy"></svg>
          <h6>Earn Points</h6>
          <p>{{dashboardDetails?.stats.total_earn_point}}</p>
        </div>
      </div>

      <div class="col-lg-4 text-center">
        <div class="form-group">
          <app-image-upload [imageUrl]="user.thumb_url" (select)="user.imageFile = $event;"
            (remove)="user.imageFile = null;"></app-image-upload>
        </div>
      </div>

      <div class="col-lg-4 text-center">
        <div class="earn-box">
          <svg class="fa-solid fa-wallet"></svg>
          <h6>
            Wallet
          </h6>
          <p>
            {{convertNumber(user.wallet_amount)}}
          </p>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-group pe-xl-5 pe-lg-4">
          <label>First Name</label>
          <nz-form-item>
            <nz-form-control nzHasFeedback nzErrorTip="Please enter first name!">
              <nz-input-group>
                <input class="form-control" type="text" nz-input (keydown)="onSpaceKeyFirstDown($event)"
                  name="first_name" id="first_name" [(ngModel)]="user.first_name" required
                  placeholder="Enter First Name">
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-group ps-xl-5 ps-lg-4">
          <label>Last Name</label>
          <nz-form-item>
            <nz-form-control nzHasFeedback nzErrorTip="Please enter last name!">
              <nz-input-group>
                <input class="form-control" type="text" nz-input (keydown)="onSpaceKeyFirstDown($event)"
                  name="last_name" id="last_name" [(ngModel)]="user.last_name" required placeholder="Enter Last Name">
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-group pe-xl-5 pe-lg-4">
          <div class="d-flex justify-content-between align-items-center">
            <label>Email Address</label>
            <button class="verify-text cursor" *ngIf="!user.email_verify" (click)="validateEmail()">Verify Email</button>
            <button class="verify-text cursor text-success" *ngIf="user.email_verify"><img src="assets/images/verify.png"
                alt="GoGrubz-verify-image">Verified</button>
          </div>
          <nz-form-item>
            <nz-form-control nzHasFeedback nzErrorTip="Please enter email!">
              <nz-input-group>
                <input class="form-control" type="email" nz-input (keydown.space)="onSpaceKeyDown($event)"
                  name="username" id="username" [(ngModel)]="user.username" required placeholder="Enter Email Address"
                  readonly>
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-group ps-xl-5 ps-lg-4">
          <div class="d-flex justify-content-between align-items-center">
            <label>Phone Number</label>
            <button class="verify-text cursor" *ngIf="!user.phone_verify" (click)="otpSend('phone')">Verify Phone Number</button>
            <button class="verify-text cursor text-success" *ngIf="user.phone_verify"><img src="assets/images/verify.png"
                alt="GoGrubz-verify-image">Verified</button>
          </div>
          <nz-form-item>
            <nz-form-control nzHasFeedback nzErrorTip="Please enter last name!">
              <nz-input-group>
                <input class="form-control" type="text" inputmode="numeric" nz-input
                  (keydown.space)="onSpaceKeyDown($event)" name="phone_number" id="phone_number"
                  (keypress)="validateMobile($event)" [(ngModel)]="user.phone_number" required
                  placeholder="Enter Phone Number">
              </nz-input-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-group pe-xl-5 pe-lg-4">
          <label>Newsletter</label>
          <div class="radio-btns d-flex">
            <div class="form-check pe-4">
              <input type="radio" class="form-check-input radio-button" id="Yes" name="radio"
                [(ngModel)]="user.newsletter" value="Y">
              <label class="form-check-label" for="Yes">Yes</label>
            </div>
            <div class="form-check">
              <input type="radio" class="form-check-input radio-button" id="No" name="radio"
                [(ngModel)]="user.newsletter" value="N">
              <label class="form-check-label" for="No">No</label>
            </div>
          </div>
        </div>
      </div>

    </div>

    <nz-form-item *ngIf="errorMessage">
      <span class="text-danger">{{ errorMessage }}</span>
    </nz-form-item>

    <div class="row">
      <div class="col-lg-6 text-center order-2 order-lg-1">
        <button class="btn change-password-btn" (click)="openModal(changePassword)">
          Change Password
        </button>
      </div>
      <div class="col-lg-6 text-center order-1 order-lg-2">
        <button class="btn save-changes-btn" nz-button>
          <i class="spinner-border" *ngIf="isProfileLoading"></i>
          Save Changes
        </button>
      </div>
    </div>

    <div class="row mt-4 mt-lg-5">
      <div class="col-lg-12 text-center">
        <button class="btn account-delete-btn" (click)="delete(user)">
          <i class="spinner-border" *ngIf="isDeleteUserLoading"></i>
          Delete Account
        </button>
      </div>
    </div>

  </form>

</div>

<!-- Change-Password-Popup -->
<ng-template #changePassword let-modal>
  <div id="change-password-popup">

    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>

    <div class="modal-body">
      <h5 class="login-title">Change Password</h5>

      <form nz-form #changePasswordForm="ngForm" (ngSubmit)="onChangePasswordSubmit(changePasswordForm)">
        <div class="row">

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback [nzErrorTip]="passwordChnageErrorTpl">
                  <nz-input-group>
                    <input type="password" id="current_password" name="current_password"
                      [(ngModel)]="user.current_password" required minlength="6"
                      placeholder="Enter your current password" class="form-control">
                  </nz-input-group>

                  <ng-template #passwordChnageErrorTpl let-control>
                    <ng-container *ngIf="control.hasError('required')">
                      Please enter your current password!
                    </ng-container>
                    <ng-container *ngIf="control.hasError('minlength')">
                      Password must be atleast 6 characters long!
                    </ng-container>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback [nzErrorTip]="passwordErrorTpl">
                  <nz-input-group>
                    <input type="password" id="password" name="password" [(ngModel)]="user.password" minlength="6"
                      required placeholder="Enter your new password" class="form-control">
                  </nz-input-group>

                  <ng-template #passwordErrorTpl let-control>
                    <ng-container *ngIf="control.hasError('required')">
                      Please enter your password!
                    </ng-container>
                    <ng-container *ngIf="control.hasError('minlength')">
                      Password must be atleast 6 characters long!
                    </ng-container>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                  <nz-input-group>
                    <input type="password" id="confirmPassword" name="confirmPassword"
                      [(ngModel)]="user.confirmPassword" required placeholder="Enter your confirm password"
                      class="form-control">
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <nz-form-item *ngIf="errorChangePassword">
            <span class="text-danger">{{ errorChangePassword }}</span>
          </nz-form-item>

          <div class="col-md-12">
            <button nz-button class="btn modal-black-btn" [disabled]="isChangePasswordLoading">
              <i class="spinner-border" *ngIf="isChangePasswordLoading"></i>
              Change Password
            </button>
          </div>

        </div>

      </form>
    </div>
  </div>
</ng-template>

<!-- Modal -->
<ng-template #VerifyModal let-modal>
  <div id="VerifyModal">
    <div class="modal-content">
      <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
        <svg class="fa-solid fa-xmark"></svg>
      </button>
      <div class="modal-body">
        <h6>Enter your six digit one time code</h6>
        <p *ngIf="verfiType == 'email'">A six digit code will be sent to your email, please check<br> your spam folder
          if you haven’t received one.
        </p>
        <p *ngIf="verfiType == 'phone'">
          A six digit code will be sent to your phone, please check.
        </p>
        <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
          <!-- <div class="one-time-code">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 0)" name="otp-input-1" id="otp-input-1" autocomplete="off">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 1)" name="otp-input-2" id="otp-input-2" autocomplete="off">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 2)" name="otp-input-3" id="otp-input-3" autocomplete="off">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 3)" name="otp-input-4" id="otp-input-4" autocomplete="off">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 4)" name="otp-input-5" id="otp-input-5" autocomplete="off">
            <input class="form-control" type="text" inputmode="numeric" nz-input required maxlength="1"
              (input)="handleInput($event, 5)" name="otp-input-6" id="otp-input-6" autocomplete="off">
          </div>
          <input type="text" nz-input [value]="emailOtp" hidden readonly /> -->

          <div class="mb-3">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                <nz-input-group>
                  <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                    (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                    (keypress)="validateMobile($event)" [(ngModel)]="user.otp" required
                    placeholder="Enter 6-digit code">
                </nz-input-group>
                <ng-template #phoneVeriErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter phone verification code!
                  </ng-container>
                  <!-- <ng-container *ngIf="control.hasError('minlength')">
                    Phone verification code should be 6 digit!
                  </ng-container>
                  <ng-container *ngIf="control.hasError('maxlength')">
                    Phone verification code should be maximum 6 digits long!
                  </ng-container> -->
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>

          <nz-form-item *ngIf="Modelotperror">
            <span class="text-danger">{{ Modelotperror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isModelOtpLoading">
            <i class="spinner-border" *ngIf="isModelOtpLoading"></i>
            Verify
          </button>

          <div class="resend-code cursor" nz-button>
            <button class="cursor" (click)="resendOtp(verfiType)">Resend Code</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</ng-template>