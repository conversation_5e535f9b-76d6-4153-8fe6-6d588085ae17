import { Inject, Injectable } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class CanonicalService {

  constructor(
    private router: Router,
    @Inject(DOCUMENT) private dom
  ) { }

  setCanonicalURL(url?: string) {
    // const link: HTMLLinkElement = this.dom.createElement('link');
    // link.setAttribute('rel', 'canonical');
    // this.dom.head.appendChild(link);
    // link.setAttribute('href', canURL);
    const canURL = url == undefined ? 'https://www.gogrubz.com' : url;
    const link: HTMLLinkElement = this.dom.querySelector('link[rel="canonical"]');
    // this.dom.head.appendChild(link);
    link.setAttribute('rel', 'canonical');
    link.setAttribute('href', canURL);
  }
}
