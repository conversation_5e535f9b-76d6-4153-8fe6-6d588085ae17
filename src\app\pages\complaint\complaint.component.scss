$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

nz-form-item{
  width: 100%;
}

.inner-page{
  padding-left: 92px;
  padding-right: 92px;
}
.inner-page .container {
  max-width: 1742px;
}
.order-history-box {
  padding: 25px;
  box-shadow:0 0 20px 5px #79797933;
  margin: 10px 0 50px 0;
  display: flex;
  border-radius: 10px;
}

.order-image {
  max-width: 280px;
  width: 100%;
  min-height: 170px;
  max-height: 170px;
  position: relative;
}

.order-image img.order-main-image {
  width: 100%;
  height:100%;
  object-fit:cover;
  border-radius: 10px;  
}
.order-image .order-logo {
  width: 74px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: absolute;
  top: 16px;
  left: 16px;
  border-radius: 50%;
  box-shadow: 0px 4px 15px 0px #00000080;
  background-color: #FFFFFF;
}
.order-image .order-logo img {
  width:100%;
  box-shadow: none;
}

.order-details {
  width: 100%;
  padding-left: 45px;
}

.order-title {
  display: flex;
  margin-bottom: 15px;
}

.order-title h6 {
  color: #000000;
  font-size: 18px;
  font-family: Visby CF;
  font-weight: 800;
  margin-bottom: 0;
}

.order-title ul {
  padding-left: 30px;
  padding-top: 3px;
  margin-bottom: 0;
}

.order-title ul li {
  color: #8F8F8A;
  font-family: 'Visby CF';
  font-size: 15px;
  font-weight: 700;
  display: inline-block;
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}

.order-title ul li::after {
  width: 4px;
  height: 4px;
  content: '';
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: -2px;
  background-color: #8F8F8A;
  border-radius: 50%;
}

.order-title ul li:last-child:after {
  display: none;
}

.order-title ul li a {
  color: #000000;
  font-size: 15px;
  text-decoration: underline;
  font-weight: 700;
}

.reservation-id{
  margin-bottom: 20px;
}
.reservation-id p{
  font-size: 20px; 
  color:#8f8f8a;
  font-weight:bold; 
  margin-bottom: 0;
}
.reservation-id p strong{
  color:#000;
}
.address-details p{
  color:#8F8F8A;
  font-family: 'Visby CF';
  font-size: 25px;
  font-weight: 700;
  line-height: 29px;    
  margin-bottom: 10px;
}
.address-details ul{
  margin-bottom:0;
}
.address-details ul li {
  color: #8F8F8A;
  font-family: 'Visby CF';
  font-size: 20px;
  font-weight: 700;
  display: block;
  position: relative;    
}
.back-now-btn{
  float: right;
  display: flex;
  align-items: flex-end;
}
.back-now-btn a.btn{  
  white-space: nowrap;
}
.back-now-btn svg{  
  position: relative;
  top: 1px;
  margin-right: 2px;
}
.order-addon-list ul {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
}

.order-addon-list ul li {
  display: flex;
  width: 33.3%;
  padding-bottom: 20px;
  padding-right: 30px;
}
.order-count {
  font-family: 'Fredoka';
  font-size: 15px;
  font-weight: 500;
  line-height: 30px;
  width: 30px;
  min-width: 30px;
  height: 30px;
  min-height: 30px;
  margin-top: 3px;
  text-align: center;
  border-radius: 50%;
  background-color: #F4F3F3;
  box-shadow: 0px 0px 5px 0px #00000033;
}
span.multiple-symbol{    
  height: 25px;
  padding: 0;
  border: 0;
  line-height: 20px;
  margin-left: 10px;
  background-color: transparent;
  color: #000;
  font-weight: bold;
  font-size: 18px;    
}
.addon-order-detail {
  padding-left:10px;
}

.addon-order-detail p {
  font-family: 'Visby CF';
  font-size: 18px;
  color: #000000;
  line-height: normal;
  font-weight: 800;
  margin-bottom: 2px;
}

.order-addon-list ul li .addon-order-detail ul li {
  width: 100%;
  padding-bottom: 0;
}

.addon-order-detail ul li {
  color: #8F8F8A;
  font-family: 'Visby CF';
  font-size: 18px;
  font-weight: 600;
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}

.addon-order-detail ul li::before {
  width: 4px;
  height: 4px;
  content: '';
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: -2px;
  background-color: #8F8F8A;
  border-radius: 50%;
}

.addon-order-detail ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.addon-order-detail ul li:last-child:before {
  display: none;
}

.view-store ul {
  margin-bottom: 0;
}

.view-store ul li {
  margin-bottom: 10px;
}

.view-store ul li:last-child {
  margin-bottom: 0;
}

.view-store a.btn {
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  padding: 7px 15px;
  width: 170px;
  height: 40px;
  white-space: nowrap;
}

.view-store a.btn img {
  width: 16px;
  position: relative;
  top: -1px;
  margin-right: 2px;
}

.view-store a.btn img.red-reorder,
.view-store a.btn img.red-review {
  display: none;
}

.view-store a.btn:hover img.white-reorder,
.view-store a.btn:hover img.white-review {
  display: none;
}

.view-store a.btn:hover img.red-reorder,
.view-store a.btn:hover img.red-review {
  display: inline-block;
}
.view-store a.btn.raise-issue-btn{
  border-color:#F38F33 !important;
  background-color:#F38F33 !important;
}
.view-store a.btn.raise-issue-btn:hover{
  color:#F38F33 !important;
  background-color: transparent !important;
}
.dont-have-order {
  padding-top: 325px;
  padding-bottom: 50px;
}

.dont-have-order p {
  font-size: 30px;
  color: #000000;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2px;
}

.dont-have-order span {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  display: block;
  padding-bottom: 40px;
}

.dont-have-order a.btn {
  font-size: 18px;
  font-weight: 700;
  padding: 7px 15px;
  width: 150px;
  height: 40px;
}

/*chat-screen*/

.online {
  position: absolute;
  top: 30px;
  left: 35px;
  width: 13px;
  height: 13px;
  background-color: #73a6ff;
  border-radius: 13px;
  border: 3px solid #FAFAFA;
}
.chat {
  width: 100%;
  box-shadow: 0 0 20px 5px #79797933;
  border-radius: 10px;
}
.header-chat {  
  padding:20px 30px;
  border-bottom: 2px solid #f4f3f3;
  display: flex;
  align-items: center;
}
.header-chat img{
  width: 55px;
}
.chat .header-chat .name {
  font-size: 22px;
  color:#222;
  font-weight: 600;
  margin:0;
  padding-left:15px;
  text-transform: capitalize;  
}
.chat .messages-chat {
  padding: 25px;
  max-height: 360px;
  min-height: 360px;
  overflow: hidden;
  overflow-y: auto;
}
.chat .messages-chat::-webkit-scrollbar {
  width: 6px;
}
.chat .messages-chat::-webkit-scrollbar-track {
  background: #f5f5f5;
}
.chat .messages-chat::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 5px;
}
.chat .messages-chat::-webkit-scrollbar-thumb:hover {
  background: #c5c5c5;
  border-radius: 5px;
}
.chat .messages-chat .message {
  display:flex;
  align-items: flex-start;
  margin-bottom: 8px;
}
.chat .messages-chat .message .photo {
    display: block;
    width: 45px;
    min-width: 45px;
    height: 45px;
    min-height:45px;
    position: relative;
    background: #E6E7ED;
    border-radius: 50px;    
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}
.chat .messages-chat .message .photo img{
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.chat .messages-chat .text {
  font-size: 16px;
  margin: 0 20px 10px 20px;
  background-color: #f6f6f6;
  padding: 13px 15px;
  border-radius: 5px;
  word-break: break-word;
}

.time {
  font-size: 13px;
  color:#222;
  font-weight: bold;
  margin-bottom:10px;
  margin-left: 20px;
}
.user-response{
  float: left;
  margin-left: 0px;
  margin-right:auto;
}
.response-time {
  float: right;
  margin-right: 20px !important;
}
.response {
  float: right;
  margin-right: 0px !important;
  margin-left:auto;
}

.response .text {
  background-color: #e3effd !important;
}

.footer-chat {
  padding: 15px 30px;
  width:100%;
  display:flex;
  align-items: flex-start;    
  border-top: 2px solid #EEE;
  
}

.write-message {
  font-size: 15px;
  width:100%;
  height: 45px;
  padding: 10px 15px;
  box-shadow: none;
  outline: none;
  border-radius: 5px;
  border: 0;
  background: #f4f3f3 !important;    
}

.footer-chat *::-webkit-input-placeholder {
  color: #C0C0C0;  
}
.footer-chat input *:-moz-placeholder {
  color: #C0C0C0;
}
.footer-chat input *::-moz-placeholder {
  color: #C0C0C0;
}
.footer-chat input *:-ms-input-placeholder {
  color: #C0C0C0;
}
.footer-chat button.btn{
  font-size: 14px;
  padding: 5px 15px;
  margin-top:5px;
  margin-left: 10px;
  white-space: nowrap;
}

@media screen and (max-width:1800px) {
.order-title h6 {
  min-width: 220px;
}

.order-details {
  padding-left: 40px;
}

.order-title ul {
  padding-left: 20px;
}
.reservation-id p{
  font-size: 18px;
}
.address-details ul {
  margin-bottom: 10px;
}
.address-details ul li {
  font-size: 18px;
}

}

@media screen and (max-width:1500px) {     
.order-history-box{
  margin-bottom: 40px;
}
.order-image {
  max-width: 210px;
  min-height: 127px;
  max-height: 127px;
}
.order-image .order-logo {
  width: 55px;
  height: 55px;
  padding: 6px;
  top: 15px;
  left: 15px;
}
.order-title h6{
  font-size: 16px;
  font-weight: 800;
}
.order-title .order-date-time {
  padding-top: 0;
}
.order-title ul {
  padding-left: 10px;
}
.order-title ul li {
  font-size: 12px;
}
.order-title ul li a{
  font-size: 12px;
}
.order-details{
  padding-left: 35px;
}
.order-title{
  margin-bottom: 10px;
}
.reservation-id{
  margin-bottom: 15px;
}
.reservation-id p {
  font-size: 20px;
  font-family: 'Fredoka One';
  font-weight: 400;
}
.address-details p {
  font-size: 18px;
  margin-bottom: 5px;
}
.address-details ul li {
  font-size: 15px;
}
.order-count{
  font-size: 12px;
  line-height: 22px;
  width: 22px;
  min-width: 22px;
  height: 22px;
  min-height: 22px;
  box-shadow: 0px 0px 3.75px 0px #00000033;
}
span.multiple-symbol{
  font-size: 14px;
}
.addon-order-detail p {
  font-size: 14px;
}
.order-addon-list ul li {    
  padding-right: 15px;
  padding-bottom: 15px;
}
.order-addon-list ul li .addon-order-detail ul li {
  font-size: 14px;
  margin-right: 5px;
  padding-right: 5px;
}
.view-store a.btn {
  font-size: 14px;
  padding: 1px 10px;
  width: 132px;
  height: 30px;
  line-height: 24px;
}
.dont-have-order{
  padding-top: 250px;
}
.dont-have-order p {
  font-size: 22px;
}
.dont-have-order span{
  font-size: 15px;
  padding-bottom: 25px;
}
.dont-have-order a.btn {
  font-size: 14px;
  font-weight: 700;
  padding: 2px 10px;
  width: 112px;
  height: 30px;
}
.chat .messages-chat { 
  max-height: 310px;
  min-height: 310px;
}

}

@media screen and (max-width:1300px) {
.order-addon-list ul li{
    width:50%;
    padding-right: 15px;
}
  
}

@media screen and (max-width:1199px) {          
.inner-page{
  padding-left: 45px;
  padding-right: 45px;
}  
.order-history-box {
  margin-bottom: 55px;
}
.order-title {
  flex-wrap: wrap;
}
.order-title .order-date-time {
  padding: 0;
}
.order-details {
  padding-left: 25px;
}

}


@media screen and (max-width:991px) {    
.order-history-box {
  flex-wrap: wrap;
}
.order-image{
  margin-bottom: 25px;
}
.order-history-box .order-details {
  max-width: 100%;
  width: 100%;
  margin-bottom: 15px;
  padding-left: 0;
}

}

@media screen and (max-width:767px) {
.inner-page{
  padding-left: 10px;
  padding-right: 10px;
}
.order-history-box {
  justify-content: center;
}
.dont-have-order {
  padding-top: 125px;
  padding-bottom: 50px;
}

}

@media screen and (max-width:480px) {  
  .dont-have-order {
      padding-top: 80px;
      padding-bottom: 40px;
  }
  .dont-have-order p{
      margin-bottom: 10px;
  }
  .dont-have-order span {
      padding-bottom: 10px;
  }
  .order-history-box{
    padding: 20px;
  }
  .reservation-id p{
    font-size: 16px;
  }
  .order-history-box{
    margin-bottom: 35px;
  }
  .chat .messages-chat{
    padding: 20px;
  }
  .chat .messages-chat .message .photo{
    width: 35px;
    min-width: 35px;
    height: 35px;
    min-height: 35px;    
  }
  .online{
    top: 22px;
    left: 28px;
    width: 12px;
    height: 12px;
    border: 2px solid #FAFAFA;
  }
  .chat .messages-chat .text {
    font-size: 14px;
    margin: 0 10px 10px;
    padding: 12px 15px;
  }
  .time{
    margin-left: 10px;
  }
  .response-time{
    margin-right: 10px !important;
  }
  .footer-chat{
    padding: 12px 20px;
  }
  .write-message{
    font-size: 14px;
    height: 40px;
    padding: 10px 15px;
  }
  .footer-chat button.btn {
    font-size: 13px;
    padding: 4px 14px;
    margin-left: 7px;
  }

  }
  
  @media screen and (max-width:400px) {
  .order-history-box .order-details {
      margin-bottom: 0px;
  }    
  .order-addon-list ul li {
      width: 100%;
      padding-right: 0;
      padding-bottom: 20px;
  }
  
  }