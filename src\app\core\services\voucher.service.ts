import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from '../../shared/error-handler';
import { Voucher } from '../models/voucher';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class VoucherService {
  private url = environment.apiBaseUrl + 'vouchers/';
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Voucher> {
    return this.http.get<Voucher>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  find(voucher: Voucher): Observable<any> {
    return this.http.post<Voucher>(this.url + "find/" + voucher.voucher_code
      , Voucher.toFormData(voucher))
      .pipe(catchError(ErrorHandler.handleError));
  }

}
