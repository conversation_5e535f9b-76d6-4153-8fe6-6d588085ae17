export class SiteSetting {
  id: string;
  user_id: string;
  site_name: string;
  site_logo: string;
  site_fav: string;
  address_mode: string;
  site_mile: string;
  search_by: string;
  admin_name: string;
  admin_email: string;
  contact_us_email: string;
  order_email: string;
  invoice_email: string;
  contact_phone: string;
  admin_phone: string;
  site_address: string;
  site_country: string;
  site_state: string;
  site_city: string;
  site_zip: string;
  site_currency: string;
  site_timezone: string;
  site_currency_code: string;
  stripe_mode: string;
  stripe_publishkey: string;
  stripe_secretkey: string;
  stripe_secretkeyTest: string;
  stripe_publishkeyTest: string;
  stripe_description: string;
  stripe_descriptor: string;
  paypal_mode: string;
  test_clientid: string;
  live_clientid: string;
  test_secretkey: string;
  live_secretkey: string;
  getaddress_key: string;
  google_analytics: string;
  woopra_analytics: string;
  zoopim_code: string;
  mail_option: string;
  smtp_host: string;
  smtp_port: string;
  smtp_username: string;
  smtp_password: string;
  vat_no: string;
  vat_percent: number;
  card_fee: number;
  service_charge: number;
  invoice_duration: string;
  offline_status: string;
  offline_notes: string;
  sms_option: string;
  sms_token: string;
  sms_id: string;
  sms_source_number: string;
  sms_username: string;
  sms_password: string;
  meta_title: string;
  meta_keywords: string;
  meta_description: string;
  gogrubz_meta_title: string;
  gogrubz_meta_keywords: string;
  gogrubz_meta_description: string;
  multiple_language: string;
  default_language: string;
  other_language: string;
  facebook_api_id: string;
  facebook_secret_key: string;
  google_api_id: string;
  google_secret_key: string;
  twitter_key: string;
  twitter_secret_key: string;
  google_key1: string;
  google_key2: string;
  google_key3: string;
  google_key4: string;
  google_key5: string;
  mailchimp_key: string;
  mailchimp_list_id: string;
  pusher_key: string;
  pusher_secret: string;
  pusher_id: string;
  fcm_key: string;
  fcm_key1: string;
  assign_status: string;
  assign_miles: string;
  facebook_login: string;
  google_login: string;
  twitter_login: string;
  apple_login: string;
  banner_status: string;
  charity_message: string;
  charity_amount: number;
  android_version: string;
  ios_version: string;
  admin_complaint_all: string;
  admin_chat_all: string;
  admin_order_all: string;
  admin_pending_order: string;
  restaurant_order_all: string;
  restaurant_pending_order: string;
  complaint_all: string;
  chat_all: string;
  pending_order_time: string;
  created: string;
  updated: string;
  dine_in: string;
  show_offers: string;
  signup_verify: string;
  checkout_verify: string;
  order_verify: string;
  booking_verify: string;
  signup_verify_type: string;
  order_verify_type: string;
  booking_verify_type: string;
  wallet_available: string;
  minimum_wallet: number;

}