import { Component, HostListener, Inject, OnInit, PLATFORM_ID, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { formatDate, isPlatformBrowser, Location } from '@angular/common';
import { Meta, Title } from '@angular/platform-browser';
import { UserService } from '../../core/services/user.service';
import { RestaurantService } from '../../core/services/restaurant.service';
import { ReviewService } from '../../core/services/review.service';
import { User } from '../../core/models/user';
import { Review } from '../../core/models/review';
import { Restaurant } from '../../core/models/restaurant';
import { Promotions } from '../../core/models/promotions';
import { environment } from '../../../environments/environment';
import { CanonicalService } from '../../shared/canonical.service';

@Component({
  selector: 'app-reviews',
  host: { ngSkipHydration: 'true' },
  templateUrl: './reviews.component.html',
  styleUrls: ['./reviews.component.scss']
})

export class ReviewsComponent implements OnInit {
  @ViewChild('target') target: any;

  private subs = new Subscription();

  user: User;
  reviews: Review[] = [];
  reviewCounts: any;
  originalReviews: Review[] = [];
  restaurant: Restaurant = new Restaurant();
  promotions: Promotions = new Promotions();
  sortBy: string = null;

  weekday = new Array('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday');

  closeTime: string;

  isLoading = false; error = null;
  isFilterLoading = false; errorFilter = null;
  isReviewLoading = false; errorReview = null;
  success = false;
  isScrollLoading = false;
  scrolling: boolean = false;

  options = {
    query: null,
    page: 1,
    per_page: 10,
    restaurant_id: null,
    sort_by: null,
    by_rating: null,
  };

  filtersReview: string[] = [];

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    private reviewService: ReviewService,
    private metaTagService: Meta,
    private titleService: Title,
    private router: Router,
    public canonicalService: CanonicalService,
    private route: ActivatedRoute,
    private location: Location,
  ) { }

  ngOnInit() {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    this.options.restaurant_id = this.restaurant.id;
    if (this.route.snapshot.paramMap.get('name') && this.route.snapshot.paramMap.get('name') != null) {
      this.restaurant.seo_url = this.route.snapshot.paramMap.get('name');
      this.fetchRestaurantName();
    } else if (this.restaurant.id) {
      this.fetchRestaurant();
    } else {
      this.router.navigateByUrl('/location/' + this.restaurantService.zipcode)
    }
  }

  @HostListener("window:scroll", ["$event"])
  onScroll() {

    const height = this.target.nativeElement.offsetTop - 400;
    if (window.pageYOffset >= height && !this.scrolling) {
      this.options.page = this.options.page + 1;
      this.scrolling = true;
      this.isScrollLoading = true;

      this.subs.add(this.reviewService.get(this.options)
        .pipe(finalize(() => this.isScrollLoading = false))
        .subscribe(
          (res) => {
            this.reviews = this.reviews.concat(res.data);
            this.scrolling = false;
          }, (err) => { }
        )
      )
    }
  }

  fetchRestaurant() {
    this.isFilterLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = true))
      .subscribe(res => {
        this.restaurant = res;
        this.metaTagService.updateTag({ property: 'robots', content: 'index, follow' });
        if (this.restaurant.meta_tag.review_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.review_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.review_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.review_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.meta_tag.review_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.meta_tag.review_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.gogrubz_meta_description });
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }
        this.promotions = this.restaurant.promotions[0];
        var objToday = new Date();
        var wk = this.weekday[objToday.getDay()] + '_second_closetime';
        this.closeTime = res[wk];
        this.fetchReviews();
        this.fetchReviewCounts();
      }, err => this.error = err)
    );
  }

  fetchRestaurantName() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show_name(this.restaurant.seo_url)
      .pipe(finalize(() => this.isLoading = true))
      .subscribe(res => {
        this.restaurant = res;
        this.options.restaurant_id = this.restaurant.id;
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(environment.googleFirebase, this.restaurant.id);
          if (!localStorage.getItem(environment.zipcode)) {
            localStorage.setItem(environment.zipcode, res.zipcode);
          }
        }
        this.metaTagService.updateTag({ property: 'robots', content: 'index, follow' });
        if (this.restaurant.meta_tag.review_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.review_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.review_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.review_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.meta_tag.review_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.meta_tag.review_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.meta_description });
        }
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        // this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        this.promotions = this.restaurant.promotions[0];
        var objToday = new Date();
        var wk = this.weekday[objToday.getDay()] + '_second_closetime';
        this.closeTime = res[wk];
        this.fetchReviews();
        this.fetchReviewCounts();
      }, err =>
        this.router.navigateByUrl('/location/' + this.restaurantService.zipcode)
      )
    );
  }

  reviewFilter(sortBy: string) {
    this.sortBy = sortBy;
    this.options.sort_by = sortBy;
    this.fetchReviews();
  }

  fetchFilterReviews() {
    this.isFilterLoading = true; this.errorFilter = null;

    this.options.page = 1;
    this.scrolling = false;
    this.subs.add(this.reviewService.get(this.options)
      .pipe(finalize(() => this.isFilterLoading = false))
      .subscribe(
        (res) => {
          this.reviews = res.data;
        }, (err) => {
          this.reviews = [];
        }
      )
    )
  }

  fetchReviews() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.reviewService.get(this.options)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.reviews = res.data;
        }, (err) => {
          this.reviews = [];
        }
      )
    )
  }

  toggleReview(item: string): void {
    const index = this.filtersReview.indexOf(item);
    if (index > -1) {
      this.filtersReview.splice(index, 1);
    } else {
      this.filtersReview.push(item);
    }
    if (this.filtersReview.length > 0) {
      this.options.by_rating = this.filtersReview.join(',');
    } else {
      this.options.by_rating = null;
    }
    this.fetchFilterReviews();
  }

  fetchReviewCounts() {
    this.isReviewLoading = true; this.error = null;

    this.subs.add(this.reviewService.count({ restaurant_id: this.restaurant.id })
      .pipe(finalize(() => this.isReviewLoading = false))
      .subscribe(
        (res) => {
          this.reviewCounts = res;
        }, (err) => {
          this.errorReview = err
        }
      )
    )
  }

  calculateRating(targetRating: number): number {
    if (this.reviewCounts) {
      const filteredReviews = this.reviewCounts.filter(review => review.rating === targetRating);
      return filteredReviews[0]?.total_count ?? 0;
    } else {
      return 0;
    }
  }

  calculateAverageRating(targetRating: number): number {
    const filteredReviews = this.reviewCounts.filter(review => review.rating === targetRating);
    if (filteredReviews.length === 0 || filteredReviews[0].total_count <= 0) {
      return 0;
    }
    return filteredReviews[0].total_count / this.restaurant.total_reviews * 100;
  }

  calculateDaysAgo(date, format?) {
    date = new Date(formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US'));
    const currentDate = new Date();
    const timeDifference = currentDate.getTime() - date.getTime();
    const secondsAgo = Math.floor(timeDifference / 1000);
    const minutesAgo = Math.floor(secondsAgo / 60);
    const hoursAgo = Math.floor(minutesAgo / 60);
    const daysAgo = Math.floor(hoursAgo / 24);
    const monthsAgo = Math.floor(daysAgo / 30);
    const yearsAgo = Math.floor(daysAgo / 365);

    if (yearsAgo > 0) {
      return yearsAgo === 1 ? 'a year ago' : `${yearsAgo} years ago`;
    } else if (monthsAgo > 0) {
      return monthsAgo === 1 ? 'a month ago' : `${monthsAgo} months ago`;
    } else if (daysAgo > 0) {
      return daysAgo === 1 ? 'a day ago' : `${daysAgo} days ago`;
    } else if (hoursAgo > 0) {
      return hoursAgo === 1 ? 'an hour ago' : `${hoursAgo} hours ago`;
    } else if (minutesAgo > 0) {
      return minutesAgo === 1 ? 'a minute ago' : `${minutesAgo} minutes ago`;
    } else {
      return secondsAgo <= 10 ? 'just now' : `${secondsAgo} seconds ago`;
    }
  }

  redirectRestaurant(restaurant) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, restaurant.id);
    }
    this.router.navigateByUrl('/' + restaurant.city_name + '/' + restaurant.seo_url + '/menus');
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
