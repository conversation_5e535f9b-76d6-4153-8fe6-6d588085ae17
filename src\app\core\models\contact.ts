export class Contact {
  id: string;

  name: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  department: string = '';
  otp: string;
  status: boolean = true;
  verify: boolean = false;

  created: string;
  updated: string;

  static toFormData(contact: Contact) {
    const formData = new FormData();

    if (contact.id) formData.append('id', contact.id);
    if (contact.name) formData.append('name', contact.name);
    if (contact.email) formData.append('email', contact.email);
    if (contact.phone) formData.append('phone', contact.phone);
    if (contact.subject) formData.append('subject', contact.subject);
    if (contact.message) formData.append('message', contact.message);
    if (contact.department) formData.append('department', contact.department);
    if (contact.otp) formData.append('otp', contact.otp);
    formData.append('status', contact.status ? '1' : '0');

    return formData;
  }
}
