<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="row" *ngIf="!isLoading">
  <div class="col-md-12 text-end">
    <div class="save-your-payment text-center pt-5 pb-5">
      <p>Your Saved Addresses</p>
      <span *ngIf="addressBooks.length <= 0">You currently don’t have any addresses saved.</span>
      <button class="btn" (click)="openModal(addNewAddressPopup)">
        New Address<svg class="fa-solid fa-plus"></svg>
      </button>
    </div>
  </div>
</div>

<div class="row" *ngIf="!isLoading && addressBooks.length > 0">
  <div class="col-xl-4 col-sm-6" *ngFor=" let addressBook of addressBooks;let i = index;">
    <div class="card-address-box">
      <img class="map-icon" src="assets/images/map-icon.svg" alt="Go-Grubz-map-image" loading="lazy">
      <h6>{{addressBook.title}}</h6>
      <p>{{addressBook.flat_no}},{{addressBook.address}}</p>
    </div>
    <div class="edit-delete">
      <ul>
        <li>
          <button class="edit-btn" (click)="openModal(addNewAddressPopup,addressBook)">
            <svg class="fa-regular fa-pen-to-square"></svg>
          </button>
        </li>
        <li>
          <button class="delete-btn" (click)="deleteAddress(addressBook)">
            <svg class="fa-regular fa-trash-can"></svg>
          </button>
        </li>
      </ul>
    </div>
  </div>
</div>

<!-- Add-New-Address-Popup -->
<ng-template #addNewAddressPopup let-modal>
  <div id="add-new-address-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body">
      <h5 class="login-title">New Address</h5>
      <form nz-form #addressForm="ngForm" (ngSubmit)="onAddressSubmit(addressForm)" nzLayout="vertical">
        <div class="row">

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter title!">
                  <nz-input-group>
                    <input type="text" class="form-control" nz-input name="title" id="title"
                      [(ngModel)]="addressBookAdd.title" required placeholder="Address Title">
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-group postcode-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode!">
                  <nz-input-group>
                    <input type="text" class="form-control" nz-input (keydown.space)="onSpaceKeyDown($event)"
                      name="zipcode" id="zipcode" required [(ngModel)]="addressBookAdd.zipcode" placeholder="Postcode">
                    <div class="btn modal-black-btn" (click)="findzipcode(addressBookAdd.zipcode)">
                      <i class="spinner-border" *ngIf="isPostcodeLoading"></i>
                      Search
                    </div>
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item *ngIf="Postcodeerror">
                <span class="text-danger error-text">{{ Postcodeerror }}</span>
              </nz-form-item>
            </div>
          </div>


          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter house/door number!">
                  <nz-input-group>
                    <input type="text" class="form-control" nz-input name="flat_no" id="flat_no"
                      [(ngModel)]="addressBookAdd.flat_no" placeholder="House Number/Door Number" required="">
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter address!">
                  <nz-input-group>
                    <input type="text" class="form-control" nz-input name="address" id="address"
                      [(ngModel)]="addressBookAdd.address" placeholder="Address" required>
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
            [(ngModel)]="addressBookAdd.latitude" />
          <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
            [(ngModel)]="addressBookAdd.longitude" />

          <nz-form-item *ngIf="errorAddAddress">
            <span class="text-danger">{{ errorAddAddress }}</span>
          </nz-form-item>

          <nz-form-item *ngIf="Modelerror">
            <span class="text-danger">{{ Modelerror }}</span>
          </nz-form-item>

          <div class="col-md-12">
            <button nz-button class="btn btn-primary modal-black-btn text-white cursor" [disabled]="isModelLoading">
              <i class="spinner-border" *ngIf="isModelLoading"></i>
              Save Address
            </button>
          </div>

        </div>
      </form>
    </div>
  </div>
</ng-template>