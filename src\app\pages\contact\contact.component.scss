$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

#edit-number-popup .modal-body h6,
#update-number-popup .modal-body h6,
#number-verification-popup .modal-body h6 {
  font-size: 26px;
  font-weight: 800;
  font-family: 'Visby CF';
  text-align: center;
}

#number-verification-popup .modal-body p {
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  margin-bottom: 35px;
}

#number-verification-popup .modal-body .form-group,
#edit-number-popup .modal-body .form-group,
#update-number-popup .modal-body .form-group {
  margin-bottom: 15px !important;
}

#edit-number-popup .modal-body .form-group input.form-control,
#number-verification-popup .modal-body .form-group input.form-control,
#update-number-popup .modal-body .form-group input.form-control {
  padding: 4px 20px 7px;
  height: 50px;
  background-color: #f4f3f3!important;
}

#number-verification-popup .modal-body span {
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  display: inline-block;
}

#number-verification-popup .more-option {
  display: flex;
  justify-content: center;
  margin: 0;
  padding-top: 15px;
}

#number-verification-popup .more-option li {
  padding: 0 5px;
}

#number-verification-popup .more-option li svg {
  color: #d7d7d7;
  font-size: 4px;
  position: relative;
  top: -4px;
}

#number-verification-popup .more-option li a {
  font-size: 16px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

#edit-number-popup .modal-footer,
#update-number-popup .modal-footer,
#number-verification-popup .modal-footer {
  padding: 0 30px 30px 30px;
}

#edit-number-popup .modal-footer button.btn,
#update-number-popup .modal-footer button.btn,
#number-verification-popup .modal-footer button.btn {
  margin: 0;
  width: 100%;
}


/*----get-in-touch----*/

.get-in-touch-bg {
  background-color: #FDF6F2;
  border-radius: 50px;
  padding-top: 350px;
  padding: 230px 135px 120px 135px;
  overflow: hidden;
}

.get-in-touch-content h2 {
  font-size: 68px;
  color: #FC353A;
  margin-bottom: 55px;
}

.get-in-touch-content p {
  font-family: 'Visby CF';
  font-size: 22px;
  font-weight: 700;
  line-height: 26px;
  color: #000;
}

.get-in-touch-image {
  text-align: right;
}

.get-in-touch-image img {
  max-width: 478px;
  width: 100%;
}

/*----customer-support----*/
.customer-support-bg {
  padding: 100px 135px;
  overflow: hidden;
}

.customer-support-content {
  padding-top: 30px;
}

.customer-support-content h4 {
  color: #FC353A;
  margin-bottom: 55px;
}

.customer-support-content p {
  color: #000000;
  font-size: 22px;
  font-weight: 600;
  line-height: 26px;
  margin-bottom: 65px;
}

/*----merchant-partner----*/
.merchant-partner-bg {
  overflow: hidden;
  padding: 50px 135px 100px 135px;
}

.merchant-partner-image img {
  max-width: 482px;
  width: 100%;
}

.merchant-partner-content {
  padding-top: 60px;
}

.merchant-partner-content h4 {
  color: #FC353A;
  margin-bottom: 50px;
}

.merchant-partner-content p {
  font-size: 22px;
  font-weight: 600;
  line-height: 26px;
  color: #000000;
  margin-bottom: 45px;
}

/*----contact-form----*/
.contact-form-bg {
  padding: 0 250px;
  margin-bottom: 250px;
}
.contact-form-bg .main-heading {
  margin-bottom: 100px;
}
.contact-form-bg .main-heading h4 {
  color: #FC353A;
}
.contact-form-box {
  padding: 55px 50px 42px 50px;
  border-radius: 40px;
  background-color: #F4F3F3;
}
.form-group {
  margin-bottom: 42px;
}
.form-group input.form-control {
  padding: 10px 30px;
  font-size: 24px;
  font-weight: 700;
  color: #000 !important;
  height: 78px;
  border-radius: 12px;
  background-color: #fff !important;
}
.form-group.select-box {
  position: relative;
  border-radius: 15px;
  background-color: #fff !important;
}
.form-group.select-box::before {
  color: #000;
  font-size: 24px;
  right: 32px;
  top: 50%;
  transform: translate(0, -50%);
}
.form-group.select-box select.form-control {
  padding: 10px 30px;
  font-size: 24px;
  font-weight: 700;
  color: #8F8F8A !important;
  height: 78px;
  border-radius: 12px;
}
.form-group.select-box select.form-control:focus{
  color: #000 !important;
}
.message-box textarea.form-control {
  color: #000;
  font-size: 24px;
  font-weight: 700;
  padding: 20px 30px;
  border-radius: 12px;
  width: 100%;
  height: 248px;
  background-color: #fff !important;
}
.message-box textarea.form-control::-webkit-input-placeholder,
.form-group input.form-control::-webkit-input-placeholder {
  color: #8F8F8A;
  opacity: 1;
}
.message-box textarea.form-control::-moz-placeholder,
.form-group input.form-control::-moz-placeholder {
  color: #8F8F8A;
  opacity: 1;
}
.form-group textarea.form-control:-ms-input-placeholder,
.form-group input.form-control:-ms-input-placeholder {
  color: #8F8F8A;
  opacity: 1;
}
.form-group textarea.form-control:-moz-placeholder,
.form-group input.form-control:-moz-placeholder {
  color: #8F8F8A;
  opacity: 1;
}
.contact-form-box button.btn {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  width: 228px;
  height: 62px;
  line-height: 58px;
  padding: 0 20px;
  border-radius: 50px;
}
.spinner-border {
  width: 20px;
  height: 20px;
  position: relative;
  top:-2px;
}
.contact-form-box button.btn svg {
  font-size: 34px;
  margin-left: 20px;
  position: relative;
  top: 3px;
}

@media screen and (max-width:1800px) {
  .get-in-touch-bg {
    background-color: #fdf6f2;
    border-radius: 50px;
    padding: 200px 100px 100px;
  }  
  .get-in-touch-content h2 {  
    line-height: 76px;
  }

  .customer-support-bg {
    padding: 100px 100px;
  }
  
  .merchant-partner-bg {
    padding: 50px 100px 140px;
  }
  .merchant-partner-content h4{
    margin-bottom: 35px;
  }  

  .contact-form-bg {
    padding: 0 200px;
  }  
  .contact-form-bg .main-heading h4{
    font-size: 50px;
  }  

}

@media screen and (max-width:1500px) {
.get-in-touch-bg{
    padding: 170px 92px 90px 92px;
    border-radius: 36px;
}
.get-in-touch-content h2 {
    font-size: 68px;
    line-height: 74px;
}
.get-in-touch-content p {
    font-size: 22px;
    line-height: 27px;
}
.get-in-touch-image img{
    max-width: 478px;
}
.customer-support-bg{
  padding: 65px 92px 90px 92px;
}
.customer-support-content h4 {
  font-size: 42px;
  margin-bottom: 60px;
}
.customer-support-content p {
  font-size: 22px;
  line-height: 26px;
  margin-bottom: 65px;
}
.merchant-partner-content a.btn,
.customer-support-content a.btn{
  font-size: 14px;
  width:136px;
  padding:4px 5px;
}

.merchant-partner-bg{
  padding: 60px 92px 108px;
}
.merchant-partner-content{
  padding-top: 45px;
}
.merchant-partner-content h4 {
  font-size: 42px;
  margin-bottom: 30px;
}
.merchant-partner-content p {
  font-size: 22px;
  line-height: 26px;
  margin-bottom: 30px;
}

.contact-form-bg{
  margin-bottom: 150px;
}
.contact-form-bg .main-heading {
  margin-bottom: 80px;
}
.contact-form-bg .main-heading h4 {
  font-size: 42px;
}
.contact-form-box{
  padding: 45px 50px 30px;
  border-radius: 25px;
}
.form-group{
  margin-bottom: 30px;
}
.form-group.select-box:before {
  font-size: 15px;
  right: 35px;
}
.form-group.select-box select.form-control, 
.form-group input.form-control {
  font-size: 14px;
  padding: 10px 25px;
  height: 50px;
  border-radius: 8px;
}
.form-group.select-box{
  border-radius: 8px;
}
.message-box textarea.form-control {
  font-size: 14px;
  padding: 18px 25px;
  border-radius: 8px;
  height:170px;
}
.contact-form-box button.btn {
  font-size: 16px;
  width: 130px;
  height: 38px;
  line-height: 32px;
  padding: 0 5px;
}
.contact-form-box button.btn svg{
  top:1px;
  font-size: 20px;
  margin-left: 10px;
}
.spinner-border {
  width: 14px;
  height: 14px;
  top:0;
}

  #number-verification-popup .modal-body h6,
  #edit-number-popup .modal-body h6,
  #update-number-popup .modal-body h6 {
    font-size: 18px;
  }

  #edit-number-popup .modal-body .add-btn a.btn,
  #update-number-popup .modal-body .add-btn a.btn,
  #number-verification-popup .modal-body .add-btn a.btn {
    font-size: 14px;
    padding: 4px 15px;
  }

  #edit-number-popup .modal-body .form-group input.form-control,
  #update-number-popup .modal-body .form-group input.form-control,
  #number-verification-popup .modal-body .form-group input.form-control {
    padding: 5px 15px 6px;
    height: 36px;
  }

  #number-verification-popup .modal-body span {
    font-size: 14px;
  }
  
}

@media screen and (max-width:1399px) {
  .get-in-touch-content p br{
    display: none;
  }

}

@media screen and (max-width:1300px) {
  .get-in-touch-bg {
    padding: 160px 70px 80px;
  }
  .get-in-touch-content h2 {
    margin-bottom: 50px;
  }

  .customer-support-bg {
    padding: 65px 70px;
  }
  .customer-support-content h4 {
    margin-bottom: 50px;
  }
  .customer-support-content p {
    margin-bottom: 50px;
  }
  .merchant-partner-bg {
    padding: 40px 70px 110px;
  }
  .contact-form-bg {
    padding: 0 140px;
  }      

}

@media screen and (max-width:1199px) {
  .get-in-touch-bg {
    padding: 150px 40px 70px;
  }   
  .get-in-touch-content h2{
    font-size: 62px;
    margin-bottom: 40px;
  }
  .get-in-touch-image img{
    max-width:100%;
  }
  .customer-support-bg {
    padding: 65px 40px;
  }
  .merchant-partner-bg {
    padding: 30px 30px 100px;
  }
  .merchant-partner-content {
    padding-top: 30px;
  }        

}

@media screen and (max-width:991px) {
  .get-in-touch-bg {
    border-radius: 30px;
  }

  .get-in-touch-content {
    text-align: center;
    margin-bottom: 50px;
  }

  .get-in-touch-content h2 {
    font-size: 55px;
    margin-bottom: 30px;
  }

  .get-in-touch-image {
    text-align: center;
  }

  .customer-support-content {
    text-align: center;
    padding-top:0;
    margin-bottom: 40px;
  }

  .customer-support-content h4 {
    margin-bottom: 30px;
  }

  .customer-support-content p {
    margin-bottom: 35px;
  }

  .merchant-partner-image {
    text-align: center;
  }

  .merchant-partner-content {
    text-align: center;
    padding-top:0;
  }

  .merchant-partner-content h4 {
    margin-bottom: 30px;
  }

  .merchant-partner-content p {
    margin-bottom: 35px;
  }    

}

@media screen and (max-width:767px) {
  .get-in-touch-bg {
    padding: 120px 30px 50px;
  }
  .get-in-touch-content {
    margin-bottom: 40px;
  }
  .customer-support-bg {
    padding: 60px 20px;
  }
  .customer-support-content {
    padding-top: 0;
  }  
  .merchant-partner-bg {
    padding: 20px 20px 80px;
  }
  .contact-form-bg {
    padding: 0 20px;
  }
  .contact-form-box {
    padding: 40px;
  }

}

@media screen and (max-width:575px) {
  .get-in-touch-content {
    padding-top: 10px;
    margin-bottom: 30px;
  }

  .get-in-touch-content h2 {
    font-size: 45px;
    margin-bottom: 10px;
  }

  .get-in-touch-content p {
    font-size: 20px;
  }

  .customer-support-content h4 {
    font-size: 40px;
    line-height: normal;
    margin-bottom: 20px;
  }

  .customer-support-content p {
    font-size: 20px;
    margin-bottom: 30px;
  }
  .merchant-partner-bg {
    padding-bottom: 60px;
  }
  .merchant-partner-content{
    padding-top:0;
  }
  .merchant-partner-content h4 {
    font-size: 40px;
    line-height: normal;
    margin-bottom: 20px;
  }
  .merchant-partner-content p {
    font-size: 20px;
    margin-bottom: 30px;
  }

  .contact-form-bg {
    margin-bottom: 120px;
  }
  .contact-form-bg .main-heading {
    margin-bottom: 60px;
  }
  .contact-form-bg .main-heading h4 {
    font-size: 40px;
  }
  .contact-form-box {
    padding: 35px;
    border-radius: 20px;
  }
  .form-group {
    margin-bottom: 25px;
  }
  .form-group.select-box:before {
    right: 25px;
  }
  .message-box textarea.form-control {
    padding: 15px 25px;
  }

}

@media screen and (max-width:480px) {
  .get-in-touch-bg {
    padding: 120px 20px 30px;
    border-radius: 20px;
  }
  .get-in-touch-content {
    padding-top: 0;
    margin-bottom: 25px;
  }
  .get-in-touch-content h2 {
    font-size: 40px;
    line-height: 62px;
  }
  .get-in-touch-content p {
    font-size: 18px;
  }
  .customer-support-content h4 {
    font-size: 28px;
    line-height: normal;
    margin-bottom: 15px;
  }

  .customer-support-content p {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .merchant-partner-content {
    padding-top: 0px;
  }

  .merchant-partner-image {
    padding-top: 20px;
  }

  .customer-support-bg {
    padding: 50px 10px;
  }

  .merchant-partner-bg {
    padding: 0 10px 40px;
  }

  .merchant-partner-content h4 {
    font-size: 28px;
    line-height: normal;
    margin-bottom: 10px;
  }

  .merchant-partner-content p {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .contact-form-bg {
    padding: 0 10px;
    margin-bottom: 80px;
  }

  .contact-form-bg .main-heading {
    margin-bottom: 30px;
  }

  .contact-form-bg .main-heading h4 {
    font-size: 28px;
  }

  .contact-form-box {
    padding: 25px;
    border-radius: 20px;
  }

  .form-group {
    margin-bottom: 20px;
  }
  .form-group.select-box:before {
    right: 20px;
  }
  .form-group.select-box select.form-control,
  .form-group input.form-control {
    padding: 5px 15px;
    height: 42px;
  }
  .message-box textarea.form-control {
    padding: 10px 15px;
    height: 150px;
  }
  .contact-form-box button.btn{
    height: 34px;
    line-height: 30px;
    padding: 0 10px;
  }
  .contact-form-box button.btn svg{
    top:2px;
  }

}
