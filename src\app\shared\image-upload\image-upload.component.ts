import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { NzUploadFile } from 'ng-zorro-antd/upload';

@Component({
  selector: 'app-image-upload',
  host: { ngSkipHydration: 'true' },
  templateUrl: './image-upload.component.html',
  styleUrls: ['./image-upload.component.scss']
})
export class ImageUploadComponent implements OnInit {
  @Input('icon') icon: string = 'user';
  @Input('imageUrl') imageUrl: string = null;
  imageFile: any = null; image: string = null;

  @Output('select') fileSelected = new EventEmitter<any>();
  @Output('remove') fileRemoved = new EventEmitter();

  @Input('placeholder') placeholder: string = null
  @Input('height') height: string = null
  @Input('width') width: string = null

  constructor() { }

  ngOnInit() { }

  beforeUpload = (file: NzUploadFile): boolean => {
    this.imageFile = file;

    let reader = new FileReader();
    reader.onload = (e: any) => this.image = e.target.result;
    reader.readAsDataURL(this.imageFile);

    this.fileSelected.emit(this.imageFile);

    return false;
  };

  deleteImage() {
    this.imageFile = null;
    this.image = null;

    this.fileRemoved.emit();
  }

  getHeight() {
    if (this.height == "100%") return this.height;
    return (this.height == null) ? "128px" : this.height + "px"
  }

  getWidth() {
    if (this.width == "100%") return this.width;
    return (this.width == null) ? "128px" : this.width + "px"
  }
}
