<section class="product-main-box-section inner-page">

    <div class="container loader-height" *ngIf="isLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container" *ngIf="!isLoading">
        <div class="row">
            <div class="col-md-12">
                <div class="product-main-box">
                    <img class="product-main-image" [src]="promotions?.image_url" [alt]="restaurant.restaurant_name"
                        onerror="this.src='./assets/images/product-main-image.png';">
                    <button class="view-menu-btn cursor" (click)="redirectRestaurant(restaurant)">View Menu</button>
                    <div class="product-logo">
                        <img [src]="restaurant?.image_url" [alt]="restaurant.restaurant_name"
                            onerror="this.src='./assets/favicon.png';">
                    </div>
                </div>
            </div>
        </div>
        <div class="product-rating-section pb-0">
            <div class="row">
                <div class="col-xl-12 col-lg-12">
                    <div class="rating-main-box mb-0">
                        <div class="product-name">
                            <h5>{{ restaurant.restaurant_name }}</h5>
                        </div>
                        <div class="product-rating">
                            <ul>
                                <li>
                                    <span>
                                        {{restaurant.average_rating > 0 ? (restaurant.average_rating | number :'1.2-2')
                                        : 0.00}}
                                    </span>
                                    <svg class="fa-solid fa-star"></svg> ({{restaurant.total_reviews > 0 ?
                                    restaurant.total_reviews:'0'}} Reviews)
                                </li>
                                <li>
                                    <div *ngFor="let cuisines of restaurant.cuisine_names; let i=index">
                                        <svg class="fa-solid fa-circle" *ngIf="i != 0"></svg>
                                        {{ cuisines.cuisine_name }}
                                    </div>
                                </li>
                                <li>
                                    <svg class="fa-solid fa-location-dot"></svg>
                                    1.3 miles
                                </li>
                            </ul>
                        </div>
                        <div class="open-close">
                            <ul>
                                <li class="green-text" *ngIf="restaurant.currentStatus != 'Close'">
                                    <svg class=" fa-regular fa-clock"></svg>
                                    {{ restaurant.currentStatus }} Now
                                </li>
                                <li class="red-text" *ngIf="restaurant.currentStatus == 'Close'">
                                    <svg class="fa-regular fa-clock"></svg>
                                    {{ restaurant.currentStatus }} Now
                                </li>
                                <li>
                                    <svg class="fa-solid fa-circle"></svg>
                                </li>
                                <li *ngIf="restaurant.currentStatus != 'Close'">
                                    Closes at {{ closeTime }}
                                </li>
                            </ul>
                        </div>
                        <div class="reviews-rating-main">
                            <div class="d-flex justify-content-between">
                                <h6>Reviews & Ratings</h6>

                                <ul class="filter-list ms-2">
                                    <li ngbDropdown class="dietary-dropdown">
                                        <button ngbDropdownToggle>
                                            Sort By
                                            <svg class="fa-solid fa-chevron-down"></svg>
                                        </button>
                                        <div ngbDropdownMenu class="dropdown-menu">
                                            <ul>
                                                <li ngbDropdownItem [ngClass]="{'active' : sortBy == 'most'}"
                                                    (click)="reviewFilter('most');">
                                                    Most Relevant
                                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                        *ngIf="sortBy == 'most'" loading="lazy">
                                                </li>
                                                <li ngbDropdownItem [ngClass]="{'active' : sortBy == 'high'}"
                                                    (click)="reviewFilter('high');">
                                                    Highest
                                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                        *ngIf="sortBy == 'high'" loading="lazy">
                                                </li>
                                                <li ngbDropdownItem [ngClass]="{'active' : sortBy == 'low'}"
                                                    (click)="reviewFilter('low');">
                                                    Lowest
                                                    <img src="assets/images/check.svg" alt="Go-Grubz-Check-Image"
                                                        *ngIf="sortBy == 'low'" loading="lazy">
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="reviews-rating-box">
                                <div class="reviews-rating-detail">
                                    <div class="reviews-show">
                                        <h4>{{restaurant.average_rating > 0 ? (restaurant.average_rating | number
                                            :'1.2-2') : 0.00 }}</h4>
                                        <ul class="reviews-list">
                                            <li *ngFor="let item of [1, 2, 3, 4, 5];">
                                                <button *ngIf="restaurant.average_rating > item"><svg
                                                        class="fa-solid fa-star"></svg></button>
                                                <button *ngIf="restaurant.average_rating <= item"><svg
                                                        class="fa-regular fa-star"></svg></button>
                                            </li>
                                        </ul>
                                        <span>{{restaurant.total_reviews > 0 ? restaurant.total_reviews:'0'}}
                                            Reviews</span>
                                    </div>
                                    <div class="rating-show" *ngIf="!isReviewLoading">
                                        <div class="progress-box">
                                            <span>5</span>
                                            <div class="progress" role="progressbar" aria-valuenow="85"
                                                aria-valuemin="0" aria-valuemax="100">
                                                <div class="progress-bar"
                                                    [ngStyle]="{ 'width.%': calculateAverageRating(5) }"></div>
                                            </div>
                                        </div>
                                        <div class="progress-box">
                                            <span>4</span>
                                            <div class="progress" role="progressbar" aria-valuenow="75"
                                                aria-valuemin="0" aria-valuemax="100">
                                                <div class="progress-bar"
                                                    [ngStyle]="{ 'width.%': calculateAverageRating(4) }"></div>
                                            </div>
                                        </div>
                                        <div class="progress-box">
                                            <span>3</span>
                                            <div class="progress" role="progressbar" aria-valuenow="15"
                                                aria-valuemin="0" aria-valuemax="100">
                                                <div class="progress-bar"
                                                    [ngStyle]="{ 'width.%': calculateAverageRating(3) }"></div>
                                            </div>
                                        </div>
                                        <div class="progress-box">
                                            <span>2</span>
                                            <div class="progress" role="progressbar" aria-valuenow="20"
                                                aria-valuemin="0" aria-valuemax="100">
                                                <div class="progress-bar"
                                                    [ngStyle]="{ 'width.%': calculateAverageRating(2) }"></div>
                                            </div>
                                        </div>
                                        <div class="progress-box">
                                            <span>1</span>
                                            <div class="progress" role="progressbar" aria-valuenow="5" aria-valuemin="0"
                                                aria-valuemax="100">
                                                <div class="progress-bar"
                                                    [ngStyle]="{ 'width.%': calculateAverageRating(1) }"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <ul class="filter-list">
                                    <li class="filter-rating-list"
                                        [ngClass]="{'active' : filtersReview.indexOf(1) !== -1}"
                                        (click)="toggleReview(1)">
                                        <ul class="star-icons">
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                        </ul> ({{ calculateRating(1) }})
                                    </li>
                                    <li class="filter-rating-list"
                                        [ngClass]="{'active' : filtersReview.indexOf(2) !== -1}"
                                        (click)="toggleReview(2)">
                                        <ul class="star-icons">
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                        </ul> ({{ calculateRating(2) }})
                                    </li>
                                    <li class="filter-rating-list"
                                        [ngClass]="{'active' : filtersReview.indexOf(3) !== -1}"
                                        (click)="toggleReview(3)">
                                        <ul class="star-icons">
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                        </ul> ({{ calculateRating(3) }})
                                    </li>
                                    <li class="filter-rating-list"
                                        [ngClass]="{'active' : filtersReview.indexOf(4) !== -1}"
                                        (click)="toggleReview(4)">
                                        <ul class="star-icons">
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                        </ul> ({{ calculateRating(4) }})
                                    </li>
                                    <li class="filter-rating-list"
                                        [ngClass]="{'active' : filtersReview.indexOf(5) !== -1}"
                                        (click)="toggleReview(5)">
                                        <ul class="star-icons">
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                            <li>
                                                <svg class="fa-solid fa-star"></svg>
                                            </li>
                                        </ul> ({{ calculateRating(5) }})
                                    </li>
                                </ul>

                                <div class="grubz-loader" *ngIf="isFilterLoading">
                                    <div class="set-one">
                                        <div class="circle"></div>
                                        <div class="circle"></div>
                                    </div>
                                    <div class="set-two">
                                        <div class="circle"></div>
                                        <div class="circle"></div>
                                    </div>
                                </div>

                                <div class="rating-list" *ngIf="reviews?.length > 0 && !isFilterLoading">
                                    <div class="rating-box" *ngFor="let review of reviews;let i = index;">
                                        <p>“{{review.message}}”</p>
                                        <div class="rating-details">
                                            <ul class="rating-star">
                                                <li *ngFor="let item of [1, 2, 3, 4, 5]; let i=index">
                                                    <button *ngIf="review.rating > i">
                                                        <svg class="fa-solid fa-star"></svg>
                                                    </button>
                                                    <button *ngIf="review.rating <= i">
                                                        <svg class="fa-regular fa-star"></svg>
                                                    </button>
                                                </li>
                                            </ul>
                                            <span>{{ review.customer_name }}</span>
                                            <svg class="fa-solid fa-circle"></svg>
                                            <span>{{convertToDate(review.created)}}</span>
                                        </div>
                                        <div class="review-response" *ngIf="review.responce">
                                            <h6>Response from the Restaurant
                                                <span>{{calculateDaysAgo(review.created)}}</span>
                                            </h6>
                                            <!-- <p>Dear Guest,</p> -->
                                            <p> {{review.responce}}</p>
                                            <!-- <p>Thank you,</p> -->
                                            <!-- <p>Warm Regards,<br>
                                                GoGrubz Manager</p> -->
                                        </div>
                                        <div #target *ngIf="i+1 == reviews.length - 1"></div>
                                    </div>

                                    <div class="grubz-loader" *ngIf="isScrollLoading">
                                        <div class="set-one">
                                            <div class="circle"></div>
                                            <div class="circle"></div>
                                        </div>
                                        <div class="set-two">
                                            <div class="circle"></div>
                                            <div class="circle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="rating-list pt-4" *ngIf="reviews?.length <= 0">
                                <div class="dont-have-order text-center">
                                    <p>Don't have any reviews for this restaurant.</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="clearfix"></div>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>