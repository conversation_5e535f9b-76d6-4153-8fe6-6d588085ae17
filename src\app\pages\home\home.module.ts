import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { HomeComponent } from './home.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { SlickCarouselModule } from 'ngx-slick-carousel';

const routes: Routes = [
  { path: '', component: HomeComponent },
];
@NgModule({
  imports: [
    GoogleMapsModule,
    SharedModule,
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    SlickCarouselModule,
    CommonModule
  ],
  declarations: [HomeComponent],
  exports: [HomeComponent]
})
export class HomeModule { }
