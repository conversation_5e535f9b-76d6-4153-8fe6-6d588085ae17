$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.welcome-about-bg {
  position: relative;
  overflow: hidden;
  background-color: #fdf6f2;
  border-radius: 50px;
  margin-bottom: 45px;
  overflow-x: hidden;
}

.welcome-about-bg .b-shape-one {
  width: 163px;
  height: 163px;
  border-radius: 50%;
  background-color: #F3EBE7;
  position: absolute;
  top: 200px;
  left: 105px;
}

.welcome-about-bg .b-shape-two {
  width: 476px;
  height: 476px;
  border-radius: 50%;
  background-color: #F3EBE7;
  position: absolute;
  top: 80px;
  right: -345px
}

.welcome-about-bg .b-shape-three {
  position: absolute;
  left: -110px;
  bottom: -110px;
  width: 467px;
  height: 467px;
  border-radius: 50%;
  background-color: #F3EBE7;
}

.welcome-about-bg .b-shape-four {
  width: 263px;
  height: 263px;
  border-radius: 50%;
  background-color: #F3EBE7;
  position: absolute;
  right: 360px;
  bottom: -115px;
}

.welcome-about-content {
  text-align: center;
  position: relative;
  z-index: 2;
  padding-top: 335px;
  padding-bottom: 275px;
}

.welcome-about-content h2 {
  padding-bottom: 60px;
}

.welcome-about-content h2 img {
  position: relative;
  top: -10px;
  margin-right: 2px;
  width: 50px;
}

.welcome-about-content p {
  font-family: 'Visby CF';
  font-size: 26px;
  font-weight: 700;
  line-height: 32px;
  margin-bottom: 0;
}

/* -----Our-Mission-CSS------*/
.our-mission-bg {
  padding-top: 70px;
  padding-bottom: 155px;
  position: relative;
  overflow-x: hidden;
}

.our-mission-bg .shape-one {
  position: absolute;
  top: -60px;
  left: -70px;
  width: 200px;
  height: 204px;
  border-radius: 50%;
  background-color: #f9f5f3;
}

.our-mission-content {  
  position: relative;
  z-index: 1;
}

.our-mission-content h2 {
  margin-bottom: 45px;
  position: relative;
  z-index:1;
}

.our-mission-content p {
  font-size: 24px;
  font-family: 'Visby CF';
  font-weight: 700;
  line-height: 30px;
  position: relative;
  z-index:1;
  margin-bottom: 55px;
}
.our-mission-content .shape-three{
  width: 115px;
  height: 115px;
  border-radius: 50%;
  background-color: #f9f5f3;
  position: absolute;
  bottom: 0;
  right: 75px;  
}
.our-mission-image {
  position: relative;
  text-align: right;
}

.our-mission-image .shape-two {
  position: absolute;
  top: -15px;
  right: -80px;
  width: 213px;
  height: 213px;
  border-radius: 50%;
  background-color: #f9f5f3;
}

.our-mission-image img {
  position: relative;
  z-index: 1;
  max-width: 100%;
  width: 100%;
}

/* -----Our-Purpose-CSS------*/
.our-purpose-bg {
  background-color: #FDF6F2;
  border-radius: 50px;
  overflow: hidden;
}

.our-purpose-image img {
  width: 100%;
  clip-path: circle(98.2% at 0 36%);
}

.our-purpose-content {
  padding-left: 8px;
  padding-right: 100px;
}

.our-purpose-content h2 {
  margin-bottom: 50px;
}

.our-purpose-content p {
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
  color: #202020;
  font-family: 'Visby CF';
}

/* -----Empowering-Restaurants-CSS------*/
.empowering-restaurants-bg {
  padding-top: 75px;
  padding-bottom: 100px;
  overflow-x: hidden;
}

.empowering-content {
  padding-top: 100px;  
  position: relative;
}

.empowering-content h2 {
  position: relative;
  z-index: 1;
  margin-bottom: 50px;
}

.empowering-content .shape-one {
  position: absolute;
  top: 60px;
  left: -60px;
  width: 182px;
  height: 182px;
  border-radius: 50%;
  background-color: #f9f5f3;
}

.empowering-content p {
  font-family: 'Visby CF';
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 50px;
}

.empowering-image {
  padding-top: 75px;  
  padding-bottom: 55px;
  text-align: right;
  position: relative;
}

.empowering-image img {
  max-width: 100%;
  width: 100%;
  position: relative;
  z-index: 1;
}

.empowering-image .shape-one {
  position: absolute;
  top: 0;
  right:0px;
  width: 258px;
  height: 258px;
  background-color: #f9f5f3;
  border-radius: 50%;
}

.empowering-image .shape-two {
  position: absolute;
  left: -40px;
  bottom: 0px;
  background-color: #f9f5f3;
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

/* -----Download-Our-App-CSS------*/
.download-our-app-bg {
  padding-top: 80px;
  background-color: #FDF6F2;
  border-radius: 50px;
  overflow-x: hidden;
  margin-bottom: 155px;
}

.download-our-app-image {
  padding-left: 105px;
}

.download-our-app-image img {
  max-width: 100%;
}

.download-our-app-content {
  padding-top: 22px;
  padding-left: 107px;
}

.download-our-app-content .main-heading h2 {
  color: #000000;
  line-height: 86px;
  margin-bottom: 75px;
}

.download-our-app-content .main-heading p {
  color: #000000;
  font-weight: 700;
  margin-bottom: 70px;
}

.download-our-app-content ul {
  padding-top: 13px;
  margin-bottom: 0;
}

.download-our-app-content ul li {
  display: inline-block;
  margin-right: 17px;
}

.download-our-app-content ul li img {
  width: 140px;
}

@media screen and (max-width:1800px) {
  .welcome-about-content h2 img {
    top: -8px;
    width: 52px;
  }

  .welcome-about-content h2 {
    padding-bottom: 60px;
  }

  .welcome-about-content p {
    font-size: 32px;
  }

  .download-our-app-image {
    padding-left: 90px;
  }

  .download-our-app-content {
    padding-top: 0;
    padding-left: 70px;
    padding-right: 50px;
  }

  .our-purpose-content h2 {
    margin-bottom: 35px;
  }

  .our-purpose-content p {
    font-size: 28px;
  }

  .empowering-content h2 {
    margin-bottom: 35px;
  }

  .empowering-content p {
    font-size: 28px;
  }

}

@media screen and (max-width:1500px) {
  .welcome-about-bg{
    border-radius: 36px;
    margin-bottom: 40px;
  }
  .welcome-about-content{
    padding-top: 250px;
    padding-bottom: 208px;
  }  
  .welcome-about-content h2 {
    padding-bottom: 70px;
  }
  .welcome-about-content h2 img {
    width: 48px;
  }
  .welcome-about-content p {
    font-size: 26px;
    line-height: 32px;
  }
  .welcome-about-bg .b-shape-one {
    width: 123px;
    height: 123px;
    top: 152px;
    left: 82px;
  }
  .welcome-about-bg .b-shape-two {
    width: 356px;
    height: 356px;
    top: 100px;
    right: -165px;
  } 
  .welcome-about-bg .b-shape-three {
    left: -85px;
    bottom: -85px;
    width: 350px;
    height: 350px;
  }
  .welcome-about-bg .b-shape-four {
    width: 198px;
    height: 198px;
    right: 270px;
    bottom: -85px;
  }

  .our-mission-bg{
    padding: 60px 92px 115px 92px;
  }
  .our-mission-bg .shape-one {
    width: 168px;
    height: 168px;    
    left:-60px;
    top: -80px;
  }      
  .our-mission-content h2 {
    margin-bottom: 65px;
  }  
  .our-mission-content p {
    font-size: 22px;
    line-height: 27px;
    margin-bottom: 35px;
  }
  .our-mission-content a.btn{
    padding: 5px 10px;
    width:144px;
  }
  .our-mission-content .shape-three {
    right: 50px;
  }  
  .our-mission-image img{
    max-width: 505px;
  }
  .our-mission-image .shape-two {
    width: 160px;
    height: 160px;
  }  
  .our-purpose-bg{
    border-radius: 36px;
  }
  .our-purpose-content{
    padding-right: 80px;
  }
  .our-purpose-content h2 {
    margin-bottom: 66px;
  }
  .our-purpose-content p {
    font-size: 22px;
    line-height: 27px;
  }  

  .empowering-restaurants-bg{    
    padding: 60px 92px 65px 92px;
  }  
  .empowering-content{
    padding-top:0;
  }
  .empowering-content h2 {
    margin-bottom: 45px;
  }
  .empowering-content p {
    font-size: 22px;
    line-height: 27px;
    margin-bottom: 45px;
  }
  .empowering-content .shape-one {
    top: -30px;
    left:25px;
    width: 136px;
    height: 136px;
  }
  .empowering-image{
    padding-top: 58px;
  }
  .empowering-image img {
    max-width: 505px;
  }
  .empowering-image .shape-one {
    width: 194px;
    height: 194px;
  }
  .empowering-image .shape-two {
    left: 10px;
    bottom: 15px;
    width: 114px;
    height: 114px;
}
  .empowering-content a.btn{
    padding:5px 11px;
  }
  .download-our-app-bg{
    border-radius: 36px;
    padding-top: 55px;
    margin-bottom: 115px;    
  }
  .download-our-app-image img{
    width: 305px;
  }
  .download-our-app-content{
    padding-left: 15px;
    padding-right: 50px;
  }
  .download-our-app-content .main-heading h2 {
    line-height: 65px;
    margin-bottom: 58px;
  }
  .download-our-app-content .main-heading p{
    line-height: 24px;
    margin-bottom: 50px;
  }
  .download-our-app-content ul li img {
    width: 102px;
  }

}

@media screen and (max-width:1399px) {  
  .welcome-about-content {
    padding: 300px 20px 250px 20px;
  }
  .our-mission-bg {
    padding-top: 45px;
    padding-bottom: 100px;
  }  
  .our-mission-bg .shape-one {
    width: 164px;
    height: 164px;
    top: -60px;
  }  
  .our-mission-image .shape-two {
    top: -45px;
    right: -80px;
  }
  .our-mission-content h2 {
    margin-bottom: 35px;
  }    
  .our-purpose-content h2 {
    margin-bottom: 35px;
  }  
  .our-purpose-content {
    padding-left: 0;
  }    
  .empowering-image .shape-two {
    left: -20px;
  }
  .download-our-app-content {
    padding-right: 40px;
  }
  .download-our-app-content ul {
    padding-top: 0px;
  }
  .download-our-app-image {
    padding-left: 80px;
  }
  .download-our-app-content .main-heading h2 {
    margin-bottom: 50px;
  }
  .download-our-app-content .main-heading p {
    margin-bottom: 50px;
  }  

}

@media screen and (max-width:1300px) {
  .empowering-restaurants-bg,
  .our-mission-bg{
    padding-left: 72px;
    padding-right: 72px;
  }
  .download-our-app-image {
    padding-left: 70px;
  }
  .our-mission-image .shape-two {
    right: -60px;
  }  

}

@media screen and (max-width:1199px) {
  .welcome-about-content {
    padding: 250px 20px 200px;
  }
  .welcome-about-content h2 {
    padding-bottom: 30px;
  }
  .welcome-about-content h2 img {
    top: -5px;
    width: 36px;
  }
  .welcome-about-content p {
    font-size: 22px;
  }  
  .welcome-about-bg .b-shape-two {
    width: 300px;
    height: 300px;
    right: -150px;
  }
  .welcome-about-bg .b-shape-three {
    width: 300px;
    height: 300px;
  }
  .welcome-about-bg .b-shape-four {
    width: 180px;
    height: 180px;
    right: 150px;
    bottom: -100px;
  }  
  .our-mission-image{
    padding-right: 35px;
  }
  .our-purpose-content h2 {
    margin-bottom: 25px;
  } 
  .our-purpose-content p {
    font-size: 20px;
  }
  .our-purpose-content{
    padding-right: 35px;
  }    
  .our-mission-bg .shape-one {
    width: 144px;
    height: 144px;
    top: -40px;
  }
  .our-mission-image .shape-two {
    width: 140px;
    height: 140px;
  }
  .empowering-image{
    padding-right: 35px;
  }
  .download-our-app-bg {
    padding-top: 40px;
  }    
  .download-our-app-image {
    padding-left: 40px;
  }

}

@media screen and (max-width:991px) {  
  .welcome-about-bg .b-shape-one {
    width: 100px;
    height: 100px;
    top: 132px;
    left: 62px;
  }
  .welcome-about-bg .b-shape-two {
    width: 250px;
    height: 250px;
    right: -100px;
  }
  .welcome-about-bg .b-shape-three {
    width: 250px;
    height: 250px;
    left: -50px;
    bottom: -50px;
  }
  .welcome-about-bg .b-shape-four {
    width: 150px;
    height: 150px;
    right: 100px;
    bottom: -60px;
  }
  .empowering-restaurants-bg,
  .our-mission-bg{
    padding-left: 30px;
    padding-right: 30px;
  }
  .our-mission-bg .shape-one {
    top: 0px;
    left:0;
  }
  .our-mission-image {
    margin: auto;
    display: table;
  }
  .our-mission-content {
    text-align: center;    
    margin-bottom: 50px;
  }
  .our-purpose-bg { 
    border-radius: 30px;
  }
  .our-purpose-image img {
    clip-path: initial;
  }
  .our-purpose-content {
    padding: 30px 50px;
    text-align: center;
  }
  .our-purpose-content p {
    font-size: 22px;
  }
  .empowering-content {    
    text-align: center;
  }
  .empowering-image {
    padding-top: 48px;
    padding-right: 0px;
    padding-bottom:0;
    text-align: center;
  }
  .empowering-image .shape-two{
    left:0;
  }
  .download-our-app-bg {
    padding-top: 50px;
    margin-bottom: 100px;
    border-radius: 30px;
  }
  .download-our-app-image {
    padding: 0 30px;
  }
  .download-our-app-content {
    padding: 40px 30px;
    text-align: center;
  }
  
}

@media screen and (max-width:767px) {
  .welcome-about-bg{
    border-radius: 30px;
  }
  .welcome-about-content {
    padding: 200px 20px 150px;
  }
  .welcome-about-bg .b-shape-one {
    width: 80px;
    height: 80px;
    left: 30px;
  }
  .welcome-about-bg .b-shape-two {
    width: 200px;
    height: 200px;
  }
  .welcome-about-bg .b-shape-three {
    width: 200px;
    height: 200px;
    left: -50px;
    bottom: -50px;
  }
  .welcome-about-bg .b-shape-four {
    width: 100px;
    height: 100px;
    right: 80px;
    bottom: -40px;
  }
  .welcome-about-content h2 {
    margin-bottom: 0;
  }
  .our-mission-bg {
    padding-top: 25px;
    padding-bottom: 80px;
  }
  .our-mission-bg .shape-one {
    width: 100px;
    height: 100px;
  }
  .our-mission-image {
    padding-right: 0px;
  }
  .our-mission-image .shape-two {
    top: -30px;
    width: 100px;
    height: 100px;
  }
  .our-mission-content .shape-three{
    width: 80px;
    height: 80px;
    right: 30px;
  }
  .our-purpose-content {
    padding: 30px 30px;
  }
  .empowering-restaurants-bg {
    padding-top: 45px;
    padding-bottom: 45px;
  }
  .empowering-content h2 {
    margin-bottom: 35px;
  }
  .empowering-content .shape-one {
    top:-20px;
    width: 100px;
    height: 100px;
  }
  .empowering-image .shape-one {
    right: 30px;
    width: 100px;
    height: 100px;
  }
  .empowering-image .shape-two {
    left: 0;
    bottom: 0;
    width: 100px;
    height: 100px;
  }
  .empowering-image img{
    max-width: 100%;
  }

  .download-our-app-bg {
    margin-bottom: 80px;
  }

}

@media screen and (max-width:480px) {
  h2 {
    font-size: 42px;
  }
  .welcome-about-bg {
    border-radius: 20px;
  }
  .welcome-about-content {
    padding: 150px 15px 60px;
  }
  .welcome-about-content h2 {
    padding-bottom: 15px;
  }
  .welcome-about-content h2 img {
    width: 28px;
  }
  .welcome-about-content p {
    font-size: 18px;
  }
  .welcome-about-bg .b-shape-one {
    top: 100px;
    width: 50px;
    height: 50px;
    left: 20px;
  }
  .welcome-about-bg .b-shape-two {
    width: 150px;
    height: 150px;
    right: -80px;
  }
  .welcome-about-bg .b-shape-three {
    width: 100px;
    height: 100px;
    left: -20px;
    bottom: -20px;
  }
  .welcome-about-bg .b-shape-four {
    width: 80px;
    height: 80px;
    right: 30px;
    bottom: -30px;
  }
  .our-mission-bg {
    padding: 0 12px 60px 12px;    
  }  
  .empowering-restaurants-bg{
    padding-left: 12px;
    padding-right: 12px;
  }
  .our-mission-bg .shape-one {
    width: 80px;
    height: 80px;
  }
  .our-mission-content h2 {
    margin-bottom: 25px;
  }
  .our-mission-content p {
    font-size: 18px;
    margin-bottom: 25px;
  }
  .our-mission-content {
    margin-bottom: 35px;
  }
  .our-mission-image .shape-two {
    top: -25px;
    width: 80px;
    height: 80px;
  }
  .our-mission-content .shape-three{
    width: 50px;
    height: 50px;
    right:0px;
  }
  .our-purpose-bg{
    border-radius: 20px;
  }
  .our-purpose-content {  
    padding: 20px;
  }
  .our-purpose-content p {
    font-size: 18px;
  }

  .empowering-content .shape-one {
    width: 80px;
    height: 80px;
  }
  .empowering-content h2 {
    margin-bottom: 25px;
  }
  .empowering-content p {
    font-size: 18px;
    margin-bottom: 25px;
  }
  .empowering-image {
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .empowering-image .shape-one {
    top: 10px;
    right: 20px;
    width: 80px;
    height: 80px;
  }
  .empowering-image .shape-two {
    width: 80px;
    height: 80px;
  }
  .download-our-app-bg {
    padding-top: 30px;
    margin-bottom: 50px;
    border-radius: 20px;
  }
  .download-our-app-content {
    padding: 30px 30px;
  }
  .download-our-app-content .main-heading h2 {
    line-height: 48px;
  }
  .download-our-app-content ul li {
    width: 100%;
    margin: 0;
    margin-bottom: 10px;
  }
  .download-our-app-content ul li:last-child {
    margin-bottom: 0;
  }
  .download-our-app-content .main-heading p,
  .download-our-app-content .main-heading h2 {
    margin-bottom: 30px;
  }  

}
