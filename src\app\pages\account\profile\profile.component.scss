
.profile-box {
    padding: 50px 100px 30px 80px;
    border-radius: 15px;
    background: #FFFFFF;
    box-shadow: 0px 0px 30px 1px #0000001A;
}
.form-group {
    margin-bottom: 35px;
}
.form-group label {
    font-size: 22px;
    color: #8F8F8A;
    font-family: 'Visby CF';
    font-weight: 700;
    padding-bottom: 10px;
}
.form-group input.form-control {
    padding: 5px 20px;
    color: #000000;
    font-size: 22px;
    font-family: 'Visby CF';
    font-weight: 700;
    height: 60px;
}
.change-password-btn {
    font-size: 25px;
    font-family: 'Visby CF';
    font-weight: 700;
    max-width: 305px;
    width: 100%;
    min-height: 62px;
    line-height: 40px;
    background-color: #FD811F !important;
    border-color: #FD811F !important;
}
.change-password-btn:hover {
    background-color: transparent !important;
    color: #FD811F !important;
}
button.save-changes-btn {
    font-size: 25px;
    font-family: 'Visby CF';
    font-weight: 700;
    max-width: 305px;
    width: 100%;
    min-height: 62px;
    background-color: #EA3323 !important;
    border-color: #EA3323 !important;
}
button.save-changes-btn i{
    position: relative;
    top: -4px;
}
button.save-changes-btn:hover {
    background-color: transparent !important;
    color: #EA3323 !important;
}
.form-group .radio-btns .form-check input.form-check-input.radio-button {
    cursor: pointer;
    width: 18px;
    height: 18px;
    margin-top: 9px;
}
.form-group .radio-btns .form-check label.form-check-label {
    cursor: pointer;
    padding-bottom: 0;
}
.modal-body {
    padding: 20px 30px 30px 30px;
}
.modal-body .login-title {
    margin-bottom: 30px;
}
.modal-body .form-group {
    margin-bottom: 20px;
}
.modal-body .form-group input.form-control {
    font-size: 18px;
    height: 50px;
}
.modal-body .form-group.postcode-group input.form-control {
    padding-right: 105px;
}
.modal-body .form-group.postcode-group div.btn {
    width: 75px;
}
.modal-body button.btn {
    width: 100%;
}
.earn-box{
    padding: 20px;
    text-align: center;
    box-shadow: 0 0 5px 5px #f5f5f5;
    max-width: 220px;
    margin: auto;
    margin-bottom: 30px;
    border-radius: 10px;
}
.earn-box svg{
    font-size: 32px;
    color: #ff4d03;
    margin-bottom: 15px;
}
.earn-box h6{
    font-family: 'Visby CF';
    font-size: 20px;
    font-weight: 600;
    color: #000;
    margin-bottom:5px;
}
.earn-box p{
    color:#8f8f8a;
    font-size: 18px;
    line-height: 24px;
    margin-bottom:0;
    font-family: 'Visby CF';
}
.btn.account-delete-btn{
    font-size: 18px;
    color: #FFFFFF !important;
    background-color: #000!important;
    border-color: #000!important;
    padding: 8px 20px;
}
.btn.account-delete-btn:hover{
    color:#000!important;
    background-color:transparent !important;
}
.verify-text{
    font-family:'Visby CF';
    font-size: 16px;
    color:#2184DF;
    font-weight: 600;
    line-height: 22px;
    text-decoration:none;
    margin-top:-5px;
    background-color: transparent;
    border:0;
    padding:0;
}
#VerifyModal .modal-body{
    padding-top:70px;
    text-align: center;
}
#VerifyModal .modal-body h6{
    font-family: 'Visby CF';
    font-size: 22px;
    font-weight: 600;
    margin-bottom:50px;
}
#VerifyModal .modal-body p{
    font-family: 'Visby CF';
    font-size: 17px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 50px;
}
.one-time-code{
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    margin-bottom: 50px;
}
.one-time-code input.form-control{
    font-size: 30px;
    width: 100%;
    height:85px;
    border-radius: 10px;
    padding: 5px 10px;
    margin: 0 8px;
    text-align: center;
}
.resend-code button{    
    font-size: 20px;
    font-weight: 600;
    color:#2184DF;
    font-family:'Visby CF';
    text-decoration:none;    
    padding:0;
    border:0;
    background-color: transparent;
}
.resend-code{
    padding-top:20px;
}
.text-success {
    color: #26d233 !important;
}
.verify-text img{
    width: 16px;
    margin-top: -2px;
    margin-right: 4px;    
}

@media screen and (max-width:1500px) {  
.profile-box {
    padding: 35px 85px 30px 65px;
    border-radius: 12px;
    box-shadow: 0px 0px 22.5px 0.75px #0000001A;
}
.form-group {
    margin-bottom: 22px;
}
.form-group label {
    font-size: 16px;
    padding-bottom: 8px;
}
.form-group input.form-control {
    font-size:16px;
    border-radius: 7px;
    padding:5px 15px;
    height: 45px;
}
.form-group .radio-btns .form-check input.form-check-input.radio-button {
    width: 14px;
    height: 14px;
    margin-top: 6px;
}
button.save-changes-btn,
.change-password-btn {
    font-size: 18px;
    line-height: 32px;
    max-width:228px;
    min-height: 46px;        
}
button.save-changes-btn i{    
    top: 0px;
}
.modal-body{
    padding: 20px 20px 20px;
}
.modal-body .login-title {
    margin-bottom: 20px;
}
.modal-body .form-group {
    margin-bottom: 15px;
}
.modal-body .form-group input.form-control {
    font-size: 13px;
    height: 36px;
}
.modal-body button.btn{
    padding:5px 15px;
}
.btn.account-delete-btn{
    font-size: 16px;
    padding: 6px 15px;
}
.verify-text{
    font-size: 12px;
}
#VerifyModal .modal-body{
    padding-top: 35px;
}
#VerifyModal .modal-body h6{
    font-size: 18px;
    margin-bottom: 30px;
}
#VerifyModal .modal-body p{
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 35px;
}
.one-time-code{
    margin-bottom: 40px;
}
.one-time-code input.form-control{
    font-size: 24px;
    height: 65px;
    border-radius: 8px;
    padding: 5px 8px;
    margin: 0 6px;
}
.resend-code{
    padding-top:15px;
}
.resend-code button {
    font-size: 16px;
}
.verify-text img {
    width: 12px;
}

}

@media screen and (max-width:1199px) {  
    .earn-box{
        max-width: 160px;
        padding: 15px;
    }
    .earn-box svg{
        font-size: 24px;
        margin-bottom: 10px;
    }
    .earn-box h6 {
        font-size: 18px;
        margin-bottom: 2px;
    }
    .earn-box p{
        font-size: 16px;
    }

}

@media screen and (max-width:991px) {  
.profile-box {
    padding: 30px 50px;
}
.save-changes-btn {
    margin-bottom: 20px;
}    

}

@media screen and (max-width:575px) {  
.profile-box {
    padding:30px;
}

}

@media screen and (max-width:480px) {
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    padding-bottom: 4px;
}
.profile-box{
    padding:25px;
}
#VerifyModal .modal-body{
    padding-top: 45px;
}
#VerifyModal .modal-body h6{
    font-size: 16px;
    margin-bottom: 20px;
}
#VerifyModal .modal-body p {
    font-size: 13px;
    margin-bottom: 25px;
}
.one-time-code{    
    padding: 0;
    margin-bottom: 25px;
}
.one-time-code input.form-control {
    font-size: 20px;
    height: 55px;
    border-radius: 6px;
    padding: 5px 5px;
    margin: 0 3px;
}
.resend-code button{
    font-size: 16px;
}

}