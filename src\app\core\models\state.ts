import { Countries } from "./countries";

export class State {
  id: string;
  country_id: string;
  state_name: string;
  state_code: string;

  status: boolean;

  country: Countries;

  created_at: string;
  updated_at: string;

  static toFormData(state: State) {
    const formData = new FormData();

    if (state.id) formData.append('id', state.id);
    if (state.country_id) formData.append('country_id', state.country_id);
    if (state.state_name) formData.append('state_name', state.state_name);
    if (state.state_code) formData.append('state_code', state.state_code);
    formData.append('status', state.status ? '1' : '0');

    return formData;
  }
}
