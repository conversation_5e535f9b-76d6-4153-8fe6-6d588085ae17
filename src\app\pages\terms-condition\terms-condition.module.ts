import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TermsConditionComponent } from './terms-condition.component';

const routes: Routes = [
  { path: '', component: TermsConditionComponent },
];
@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule
  ],
  declarations: [TermsConditionComponent],
  exports: [TermsConditionComponent]
})
export class TermsConditionModule { }
