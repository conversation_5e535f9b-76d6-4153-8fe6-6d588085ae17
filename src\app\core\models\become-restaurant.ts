export class BecomeRestaurant {
  id: string;
  restaurant_name: string;
  restaurant_address: string;
  email: string;
  phone: string;
  otp: string;
  restaurant_postcode: string;

  created: string;
  updated: string;

  static toFormData(become_restaurant: BecomeRestaurant) {
    const formData = new FormData();

    if (become_restaurant.id) formData.append('id', become_restaurant.id);
    if (become_restaurant.restaurant_name) formData.append('restaurant_name', become_restaurant.restaurant_name);
    if (become_restaurant.restaurant_address) formData.append('restaurant_address', become_restaurant.restaurant_address);
    if (become_restaurant.email) formData.append('email', become_restaurant.email);
    if (become_restaurant.phone) formData.append('phone', become_restaurant.phone);
    if (become_restaurant.otp) formData.append('otp', become_restaurant.otp);
    if (become_restaurant.restaurant_postcode) formData.append('restaurant_postcode', become_restaurant.restaurant_postcode);

    return formData;
  }
}
