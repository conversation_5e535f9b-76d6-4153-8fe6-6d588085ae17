// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  apiBaseUrl: 'http://************:9600/tiffintom/',
  rmsUrl: 'http://************:9600/api/',
  accessTokenKey: 'TiffMlgEgnr232J-token',
  refreshTokenKey: 'TiffMEh90R82TTS-refresh-token',
  userKey: 'HSDEh90R82TTS-d44',
  googleFirebase: 'HSDEh90R82TTS-d43',
  order_type: 'HSDEh90R82TTS-d45',
  order: 'HSDEh90R82TTS-dddddddd',
  cart: 'HSDEh90R82TTS-dddddddd3',
  address: 'HSDEh90R82TTS-dddddddd35',
  zipcode: 'HSDEh90R82TTS-dddddddd33',
  soc1: '406032794188643',
  gap: '91431041616-arll4gn2l718vrrfr4tpl11o5b2cmjto.apps.googleusercontent.com',
  garble: '7061737323313233',
  jumble: '7061737323313233',
  // googleMapsApiKey: 'AIzaSyDIg0quiiSztRFN1GxJOFa4nVUuzXLXkbE'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
