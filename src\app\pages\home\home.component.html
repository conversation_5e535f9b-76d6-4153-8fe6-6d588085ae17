<!--<div class="main-loader-bg" *ngIf="isLoading">
    <div class="loading">
        <div class="dot">
            <img class="grubz-icon" src="assets/images/grubz-icon.svg" alt="grubz-icon">
            G
        </div>
        <div class="dot">O</div>
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot">G</div>
        <div class="dot">r</div>
        <div class="dot">u</div>
        <div class="dot">b</div>
        <div class="dot">z</div>
        <span class="wait-text">Please Wait...</span>
    </div>
</div>-->
<!-- html code -->
<section class="banner-bg">
    <div class="banner-top-shape"></div>
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-xl-7">
                <div class="banner-content">
                    <div class="banner-shapes">
                        <div class="shape-one"></div>
                        <div class="shape-two"></div>
                        <div class="shape-three"></div>
                    </div>
                    <div class="banner-content-shape">
                        <h2>
                            Why <img src="assets/images/g-icon.svg" alt="GoGrubz-G-Image" loading="lazy">o out when,<br>
                            you
                            can Grubz in
                        </h2>
                    </div>
                    <div class="enter-postcode-address">
                        <form nz-form #fetchForm="ngForm" (ngSubmit)="restaurantFetch(fetchForm)">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode or delivery address!">
                                    <nz-input-group>
                                        <img class="send-icon" src="assets/images/send-icon.svg" alt="GoGrubz-Send-Icon"
                                            loading="lazy">
                                        <input class="form-control" type="text" nz-input name="zipcode" id="zipcode"
                                            [(ngModel)]="restaurant.zipcode" required (input)="formatPostcode($event)"
                                            placeholder="Enter postcode or delivery address">
                                        <a class="close-icon" *ngIf="restaurant.zipcode" (click)="clearPostcode()">
                                            <img src="assets/images/close.svg" alt="close">
                                        </a>
                                        <!-- (keydown.space)="onSpaceKeyDown($event)" -->
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>

                            <nz-form-item *ngIf="error">
                                <span class="text-danger">{{ error }}</span>
                            </nz-form-item>

                            <button class="btn" nz-button>
                                <i class="spinner-border" *ngIf="isLoading"></i>
                                <span>Search</span>
                            </button>
                        </form>
                    </div>
                    <div class="got-account-sign-in">
                        <p *ngIf="!userService.user?.id">
                            Got an account? <a class="cursor" data-bs-toggle="modal" href="#login-modal">Sign in</a>
                        </p>
                    </div>
                    <div class="users-restaurants-main-box">
                        <div class="users-restaurants-box">
                            <h3>{{ dashboardDetails?.total_restaurants }}</h3>
                            <p>Restaurants</p>
                        </div>
                        <div class="users-restaurants-box">
                            <h3>{{ nFormatter(dashboardDetails?.total_users) }}+</h3>
                            <p>Users</p>
                        </div>
                    </div>
                </div>
            </div>
            <!--<div class="col-lg-5">
                <div class="banner-image">
                    <img src="assets/images/banner-fastfood.png" alt="GoGrubz-Banner-Fastfood-Image" loading="lazy">
                </div>
            </div>-->
            <div class="banner-image">
                <img src="assets/images/banner-fastfood.png" alt="GoGrubz-Banner-Fastfood-Image" loading="lazy">
            </div>
            <div class="col-lg-12">
                <div class="favourite-dishes">
                    <h5>Let’s find your favourite dishes</h5>
                    <button class="cursor" (click)="scrollBannerToTop()">
                        <img class="cursor" src="assets/images/double-down.svg" alt="double-down">
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="banner-bottom-shape"></div>
</section>

<div class="middle-sections">

    <!-- <section class="offers-section">
        <div class="container">
            <div class="main-heading">
                <h5>This week’s offers</h5>
            </div>
            <ngx-slick-carousel class="carousel" #slickModal="slick-carousel" [config]="slideConfig">
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content green-text">
                            <h5>20% Off<br> Pearls Peri Peri</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-one.png" alt="offer-image-one">
                        </div>
                    </div>
                </div>
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content red-text">
                            <h5>20% Off<br> Pearls Peri Peri</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-two.png" alt="offer-image-two">
                        </div>
                    </div>
                </div>
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content orange-text">
                            <h5>New to Go Grubz<br> Mega Much</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-three.png" alt="offer-image-three">
                        </div>
                    </div>
                </div>
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content green-text">
                            <h5>20% Off<br> Pearls Peri Peri</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-one.png" alt="offer-image-one">
                        </div>
                    </div>
                </div>
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content red-text">
                            <h5>20% Off<br> Pearls Peri Peri</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-two.png" alt="offer-image-two">
                        </div>
                    </div>
                </div>
                <div ngxSlickItem class="offers-slider-item slide">
                    <div class="offers-box">
                        <div class="offers-box-content orange-text">
                            <h5>New to Go Grubz<br> Mega Much</h5>
                            <p>For this week only</p>
                            <a class="btn" href="#">Order Now</a>
                        </div>
                        <div class="offers-box-image">
                            <img src="assets/images/offer-image-three.png" alt="offer-image-three">
                        </div>
                    </div>
                </div>
            </ngx-slick-carousel>
        </div>
    </section> -->

    <section class="Join-the-movement-bg">
        <img class="Join-bg-image" src="assets/images/join-movement-bg.png" alt="GoGrubz-Partnering-Image"
            loading="lazy">
        <div class="container">
            <div class="row h-100 align-items-center">
                <div class="col-lg-6">
                    <div class="Join-movement-content">
                        <div class="main-heading">
                            <h2>Join the<br> movement</h2>
                            <p>
                                Grow your business and reach new<br>
                                customers by partnering with us.
                            </p>
                        </div>
                        <button class="btn" routerLink="/add-restaurant">
                            Sign up your store
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="everything-you-crave-bg">
        <div class="row h-100 align-items-center">
            <div class="col-lg-5">
                <div class="everything-you-crave-image">
                    <img src="assets/images/crave-image.png" alt="GoGrubz-Delivered-Image" loading="lazy">
                </div>
            </div>
            <div class="col-lg-7">
                <div class="everything-you-crave-content">
                    <div class="main-heading">
                        <h2>
                            Everything you<br> crave, delivered.
                        </h2>
                        <p>
                            Get a pizza or a curry delivered, or pick up some chicken<br>
                            chow mein from the Chinese takeout spot you've been meaning to try.
                        </p>
                    </div>
                    <!-- routerLink="/locations" -->
                    <button (click)="findLocation()" class="btn">
                        Find restaurant
                    </button>
                </div>
            </div>
        </div>
    </section>

    <section class="find-perfect-cuisine-bg">
        <div class="row h-100 align-items-center">
            <div class="col-lg-7 order-2 order-lg-1">
                <div class="perfect-cuisine-content">
                    <div class="main-heading">
                        <h2>
                            Find the perfect<br> cuisine for you.
                        </h2>
                        <p>
                            Start swiping right for your taste buds – you'll soon find the cuisine<br> that's a perfect
                            match for your cravings.
                        </p>
                    </div>
                    <button class="btn" routerLink="/">
                        Discover Cuisines
                    </button>
                </div>
            </div>
            <div class="col-lg-5 order-1 order-lg-2">
                <div class="perfect-cuisine-image text-end">
                    <img src="assets/images/perfect-cuisine-image.png" alt="GoGrubz-Discover-Cuisines" loading="lazy">
                </div>
            </div>
        </div>
    </section>

    <section class="download-our-app-bg">
        <div class="shape-column-one">
            <div class="shape-one"></div>
            <div class="shape-two"></div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <div class="download-our-app-image">
                    <img src="assets/images/download-image.png" alt="download-image">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="download-our-app-content">
                    <div class="main-heading">
                        <h2>
                            Download<br> our App
                        </h2>
                        <p>
                            Our app gives you access to your favourite restaurants.<br>
                            Get your food delivered right to you
                        </p>
                    </div>
                    <ul>
                        <li>
                            <a href="https://apps.apple.com/in/app/go-grubz/id6484270752" class="cursor"
                                target="_blank">
                                <img src="assets/images/app-store.svg" alt="app-store">
                            </a>
                        </li>
                        <li>
                            <a href="https://play.google.com/store/apps/details?id=com.gogrubzuk" class="cursor"
                                target="_blank">
                                <img src="assets/images/googleplay.svg" alt="googleplay">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="shape-column-two"></div>
        <div class="shape-column-three"></div>
        <div class="download-bottom-shape"></div>
    </section>

    <section class="restaurants-bg">
        <div class="restaurants-near-me">
            <h3>
                Restaurants<br> near me
            </h3>
        </div>

        <google-map [center]="center" [zoom]="zoom" [options]="{ streetViewControl: false, fullscreenControl: true }">
            <map-marker *ngFor="let marker of markers" [position]="marker.position" [label]="marker.label"
                [title]="marker.title">
            </map-marker>
        </google-map>

        <!--<img src="assets/images/map-bg.png" alt="map-bg">-->
        <!-- <iframe class="border-0"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2425.0280255222856!2d-1.9842219729866506!3d52.569104432592454!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x4870a277546101e9%3A0x8a375d5902f9fe3c!2sTame%20Cl%2C%20Walsall%20WS1%204BA%2C%20UK!5e0!3m2!1sen!2sin!4v1706016793944!5m2!1sen!2sin"
            allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> -->
    </section>

    <section class="view-all-cities-bg">
        <div class="main-heading text-center">
            <h4>View all cities</h4>
        </div>

        <div class="row">
            <div *ngFor="let item of cities | keyvalue">
                <div class="col-lg-12">
                    <div class="city-character-count">
                        <span>{{ item.key }}</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-3 col-sm-6" *ngFor="let city of item.value;">
                        <div class="cities-box">
                            <ul>
                                <li (click)="onCity(city.city_name)"><span>{{ city.city_name | titlecase }}</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>