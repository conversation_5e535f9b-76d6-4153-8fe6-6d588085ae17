import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription, interval } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { User } from '../../core/models/user';
import { Restaurant } from '../../core/models/restaurant';
import { Order } from '../../core/models/order';
import { ModalDismissReasons, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../core/services/user.service';
import { RestaurantService } from '../../core/services/restaurant.service';
import { OrderService } from '../../core/services/order.service';
import { CurrencyPipe, formatDate } from '@angular/common';
import { environment } from '../../../environments/environment';
import { AnimationOptions } from 'ngx-lottie';
import { SocketService } from '../../core/services/socket.service';

@Component({
  selector: 'app-order-details',
  host: { ngSkipHydration: 'true' },
  templateUrl: './order-details.component.html',
  styleUrls: ['./order-details.component.scss']
})

export class OrderDetailsComponent implements OnInit, OnDestroy {
  private subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  modalOptions: NgbModalOptions;

  milliSecondsInASecond = 1000;
  hoursInADay = 24;
  minutesInAnHour = 60;
  SecondsInAMinute = 60;

  public timeDifference;
  public secondsToDday;
  public minutesToDday;
  public hoursToDday;
  public daysToDday;

  orderStatuses = [
    {
      "title": "Order Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Order Accepted",
      "checked": false,
      "status": 'Accepted',
      "order_type": "all"
    },
    {
      "title": "Preparing",
      "checked": false,
      "status": 'Preparing',
      "order_type": "delivery"
    },
    {
      "title": "On the way",
      "checked": false,
      "status": 'Wait',
      "order_type": "delivery"
    },
    {
      "title": "Delivered",
      "checked": false,
      "status": 'Delivered',
      "order_type": "all"
    }
  ]

  orderRejectStatuses = [
    {
      "title": "Order Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Order has been Rejected",
      "checked": true,
      "status": 'Failed',
      "order_type": "all"
    },
  ]

  optionPending: AnimationOptions = {
    path: './assets/Animations/Waitingtoaccept.json',
  };
  optionAccepted: AnimationOptions = {
    path: './assets/Animations/OrderPlaced.json',
  };
  optionDelivered: AnimationOptions = {
    path: './assets/Animations/Fooddelivered.json',
  };
  optionCollected: AnimationOptions = {
    path: './assets/Animations/Foodontheway.json',
  };
  optionWaiting: AnimationOptions = {
    path: './assets/Animations/Placed.json',
  };
  optionFailed: AnimationOptions = {
    path: './assets/Animations/Orderrejected.json',
  };

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;

  options = { query: null, page: 1, per_page: 20, };
  socketConnected = false;

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    private orderService: OrderService,
    // private currencyPipe: CurrencyPipe,
    private route: ActivatedRoute,
    private router: Router,
    private socketService: SocketService,
  ) { }

  ngOnInit() {
    this.user = JSON.parse(this.userService.getUser());
    const id = atob(this.route.snapshot.paramMap.get('id'));
    this.order.id = id.toString().replace(/\%3D/g, '=');
    if (!this.user?.id) {
      this.router.navigateByUrl('/menu');
    }
    this.fetchUser();
    this.fetchOrder();

    // setTimeout(() => { this.ngOnInit() }, 10000);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    if (this.socketConnected) {
      this.socketService.disconnectSocket();
      this.socketConnected = false;
    }
  }


  fetchRestaurant() {
    this.error = null;
    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        const businessId = this.restaurant.business.id;
        const soketUrl = this.restaurant.business?.socket_url;
        if (businessId && !this.socketConnected && soketUrl) {
          this.socketService.connectSocket(businessId, this.user.id, soketUrl);
          this.socketService.listen<any>('tiffintom_order_change_status').subscribe(data => {
            Object.keys(data).forEach(key => {
              if (key in this.order) {
                this.order[key] = data[key];
              }
            });
            if (this.user.id != this.order.customer_id) {
              this.router.navigateByUrl('/menu');
            }
            if (this.order.status != 'Failed') {
              this.orderStatuses.forEach(statuses => {
                if (this.order.status == 'Delivered') {
                  statuses.checked = true;
                }
                if ((this.order.status == 'Collected') && statuses.status != 'Delivered') {
                  statuses.checked = true;
                }
                if ((this.order.status == 'Waiting' || this.order.status == 'Driver Accepted') && (statuses.status != 'Delivered' && statuses.status != 'Wait')) {
                  statuses.checked = true;
                }
                if (this.order.status == 'Accepted' && (statuses.status != 'Delivered' && statuses.status != 'Wait' && statuses.status != 'Preparing')) {
                  statuses.checked = true;
                }
                if (this.order.status == 'Pending' && statuses.status == 'Pending') {
                  statuses.checked = true;
                }
              });
            }
            if (this.order.status == 'Accepted') {
              this.startTimer()
            }
          })
          this.socketConnected = true;
        }

      }, err => this.error = err)
    );
  }

  fetchOrder() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.orderService.show(this.order.id)
      .pipe(finalize(() => this.isLoading = true))
      .subscribe(
        (res) => {
          this.order = res;
          this.restaurant.id = this.order.restaurant_id;
          this.fetchRestaurant();
          if (this.user.id != this.order.customer_id) {
            this.router.navigateByUrl('/menu');
          }
          if (this.order.status != 'Failed') {
            this.orderStatuses.forEach(statuses => {
              if (this.order.status == 'Delivered') {
                statuses.checked = true;
              }
              if ((this.order.status == 'Collected') && statuses.status != 'Delivered') {
                statuses.checked = true;
              }
              if ((this.order.status == 'Waiting' || this.order.status == 'Driver Accepted') && (statuses.status != 'Delivered' && statuses.status != 'Wait')) {
                statuses.checked = true;
              }
              if (this.order.status == 'Accepted' && (statuses.status != 'Delivered' && statuses.status != 'Wait' && statuses.status != 'Preparing')) {
                statuses.checked = true;
              }
              if (this.order.status == 'Pending' && statuses.status == 'Pending') {
                statuses.checked = true;
              }
            });
          }
          if (this.order.status == 'Accepted') {
            this.startTimer()
          }
        }, (err) => {
          this.router.navigateByUrl('/menu');
        }
      )
    )
  }

  fetchUser() {
    this.error = null;

    this.subs.add(this.userService.show(this.user?.id)
      .pipe(finalize(() => { }))
      .subscribe(
        (res) => {
          this.user = res;
        }, (err) => {
        }
      )
    )
  }

  orderStore(restaurant) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, restaurant.id);
    }
    this.router.navigateByUrl('/' + restaurant?.city_name + '/' + restaurant?.seo_url + '/menus');
  }

  startTimer() {
    this.subs.add(interval(1000).subscribe(x => { this.getTimeDifference(); }));
  }

  private getTimeDifference() {
    this.timeDifference = new Date(this.order.delivery_date + " " + this.order.preparation).getTime() - new Date().getTime();
    this.allocateTimeUnits(this.timeDifference);
  }

  private allocateTimeUnits(timeDifference) {
    this.secondsToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond) % this.SecondsInAMinute);
    this.minutesToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour) % this.SecondsInAMinute);
    this.hoursToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour * this.SecondsInAMinute) % this.hoursInADay);
    this.daysToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour * this.SecondsInAMinute * this.hoursInADay));
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    if (date) {
      return formatDate(date, format ? format : 'yyyy-MM-dd hh:mm a', 'en_US')
    } else {
      return null;
    }
  }


  applyFilters() { this.router.navigate([], { queryParams: this.options }); }


}
