<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="row" *ngIf="!isLoading">
  <div class="col-md-12">
    <div class="save-your-payment text-center">
      <p>Your Saved Payment Methods</p>
      <span *ngIf="stripeCustomers.length <= 0">You currently don’t have any payment methods saved.</span>
      <button class="btn" class="btn" (click)="addCard(addNewCardPopup)">
        New Payment Method <svg class="fa-solid fa-plus"></svg>
      </button>
    </div>
  </div>
</div>

<div class="row" *ngIf="!isLoading && stripeCustomers.length > 0">

  <div class="col-xl-4 col-sm-6" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
    <div class="credit-card-box">
      <img
        *ngIf="stripeCustomer.card_brand.toLowerCase() == 'mastercard' || stripeCustomer.card_brand.toLowerCase() == 'master'"
        class="card-bg" src="assets/images/master-card-bg.png" [alt]="stripeCustomer.card_brand | lowercase">
      <img *ngIf="stripeCustomer.card_brand.toLowerCase() == 'visa' || stripeCustomer.card_brand == ''" class="card-bg"
        src="assets/images/visa-card-bg.png" [alt]="stripeCustomer.card_brand | lowercase">
      <img
        *ngIf="stripeCustomer.card_brand.toLowerCase() == 'american express' || stripeCustomer.card_brand.toLowerCase() == 'amex'"
        class="card-bg" src="assets/images/american-card-bg.png" [alt]="stripeCustomer.card_brand">

      <div class="card-detail-box">
        <div class="credit-card-header">
          <span>{{ stripeCustomer.card_type | titlecase}}</span>
          <!--<span>{{ stripeCustomer.card_brand | titlecase}}</span>-->
        </div>
        <div class="credit-card-footer">
          <div class="card-holder-name">
            <p>{{ stripeCustomer?.customer_name | titlecase}}</p>
            <p class="d-flex align-items-center">
              <svg class="fa-solid fa-circle"></svg>
              <svg class="fa-solid fa-circle"></svg>
              <svg class="fa-solid fa-circle"></svg>
              <svg class="fa-solid fa-circle"></svg>
              {{ stripeCustomer.card_number}}
            </p>
          </div>
          <div class="expiry-date">
            <p>{{stripeCustomer.exp_month}}/{{getLastDigit(stripeCustomer.exp_year)}}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="edit-delete">
      <ul>
        <li *ngIf="false">
          <button class="edit-btn">
            <svg class="fa-regular fa-pen-to-square"></svg>
          </button>
        </li>
        <li>
          <button class="delete-btn" (click)="deleteCard(stripeCustomer)">
            <svg class="fa-regular fa-trash-can"></svg>
          </button>
        </li>
      </ul>
    </div>
  </div>
</div>


<!-- Add-New-Card-Popup -->
<ng-template #addNewCardPopup let-modal>
  <div id="add-new-card-popup">

    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>

    <div class="modal-body">
      <h5 class="login-title">New Payment Method</h5>

      <form nz-form #cardInfo="ngForm" (ngSubmit)="handleForm($event)" nzLayout="vertical">
        <div class="row">

          <div class="col-md-8">
            <div class="form-group">
              <div class="input-group">
                <span id="card-number" class="form-control rounded"></span>
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <span id="card-exp" nz-input class="form-control rounded"></span>
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-group">
              <span id="card-cvc" class="form-control rounded"> </span>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="form-group">
              <span id="postalCode" nz-input class="form-control rounded"></span>
            </div>
          </div>

          <nz-form-item *ngIf="Modelerror">
            <span class="text-danger">{{ Modelerror }}</span>
          </nz-form-item>

          <div class="col-md-12 mt-2">
            <button class="btn modal-black-btn" nz-button [disabled]="isModelLoading">
              Save Card
            </button>
          </div>

        </div>
      </form>

    </div>
  </div>

</ng-template>