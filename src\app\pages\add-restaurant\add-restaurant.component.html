<section class="add-restaurant-banner-bg">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-7">
                <div class="add-restaurant-banner-content">
                    <h2>Let’s partner up!</h2>
                    <h4>
                        <span>
                            Only 5% commission,
                        </span><br>
                        that’s it.
                    </h4>
                </div>
            </div>
            <div class="col-lg-5">
                <div class="add-restaurant-form-card">
                    <form nz-form #addRestaurantForm="ngForm" (ngSubmit)="onRestaurantAdd(addRestaurantForm)">

                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter name!">
                                    <nz-input-group>
                                        <input class="form-control" type="text" nz-input
                                            (keydown)="onSpaceKeyDown($event)"
                                            [(ngModel)]="becomeRestaurant.restaurant_name" name="business-name"
                                            id="business-name" placeholder="Business Name" required autocomplete="nope">
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>

                        <div class="form-group postcode-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode!">
                                    <nz-input-group>
                                        <input class="form-control" type="text" nz-input
                                            (keydown)="onSpaceKeyDown($event)"
                                            [(ngModel)]="becomeRestaurant.restaurant_postcode" name="business-postcode"
                                            id="business-postcode" placeholder="Business Postcode" required
                                            autocomplete="nope">
                                        <div class="btn" (click)="findzipcode(becomeRestaurant.restaurant_postcode)">
                                            <i class="spinner-border" *ngIf="isPostcodeLoading"></i>
                                            Search
                                        </div>
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                            <nz-form-item *ngIf="Postcodeerror">
                                <span class="text-danger error-text">{{ Postcodeerror }}</span>
                            </nz-form-item>
                        </div>

                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter address!">
                                    <nz-input-group>
                                        <input class="form-control" type="text" nz-input
                                            (keydown)="onSpaceKeyDown($event)"
                                            [(ngModel)]="becomeRestaurant.restaurant_address" name="business-address"
                                            id="business-address" placeholder="Business Address" required
                                            autocomplete="nope">
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>

                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback [nzErrorTip]="emailErrorTpl">
                                    <nz-input-group>
                                        <input class="form-control" type="email" nz-input email="true"
                                            pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #restEmail="ngModel"
                                            [(ngModel)]="becomeRestaurant.email"
                                            (keydown.space)="onSpaceKeyDown($event)" name="email-address"
                                            id="email-address" placeholder="Email Address" required autocomplete="nope">
                                    </nz-input-group>
                                    <ng-template #emailErrorTpl let-control>
                                        <ng-container *ngIf="control.hasError('required')">
                                            Please enter your email!
                                        </ng-container>
                                        <ng-container
                                            *ngIf="control.hasError('email') || (!control.hasError('required') && restEmail.touched) || (!control.hasError('required') && !restEmail.valid)">
                                            Email must be a valid email address
                                        </ng-container>
                                    </ng-template>
                                </nz-form-control>
                            </nz-form-item>
                        </div>

                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter phone number!">
                                    <nz-input-group>
                                        <input class="form-control" type="text" nz-input inputmode="numeric"
                                            (keydown.space)="onSpaceKeyDown($event)"
                                            [(ngModel)]="becomeRestaurant.phone" (keypress)="validateMobile($event)"
                                            name="phone_number" id="phone_number" placeholder="Phone Number" required
                                            autocomplete="nope">
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>
                        <!--<div class="form-group select-box">
                        <select class="form-control" name="bussiness-type" id="bussiness-type">
                            <option>
                                Business Type
                            </option>
                            <option>
                                Business Type 1
                            </option>
                            <option>
                                Business Type 2
                            </option>
                            <option>
                                Business Type 3
                            </option>
                            <option>
                                Business Type 4
                            </option>
                            <option>
                                Business Type 5
                            </option>
                        </select>
                    </div>-->

                        <nz-form-item *ngIf="error">
                            <span class="text-danger">{{ error }}</span>
                        </nz-form-item>

                        <!-- <a class="btn cursor"> -->
                        <button class="btn cursor" nz-button [disabled]="isLoading">
                            <i class="spinner-border" *ngIf="isLoading"></i>
                            Get Started <img src="assets/images/black-arrow-right.svg" alt="GoGrubz-Right-Arrow"
                                loading="lazy">
                        </button>
                        <!-- </a> -->
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="shape-one">
        <img src="assets/images/shape-one.png" alt="Go-grubz-shape-one">
    </div>
    <div class="shape-two">
        <img src="assets/images/shape-two.png" alt="Go-grubz-shape-two">
    </div>
</section>

<section class="no-upfront-cost-bg">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="no-upfront-image">
                    <div class="shape-one">
                        <img src="assets/images/upfront-shape-one.png" alt="GoGrubz-Upfront-Shape">
                    </div>
                    <img src="assets/images/upfront-cost-image.png" alt="GoGrubz-Upfront-Cost-Image" loading="lazy">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="no-upfront-content">
                    <div class="upfront-shape-two">
                        <img src="assets/images/upfront-shape-two.png" alt="GoGrubz-Upfront-Shape">
                    </div>
                    <div class="no-upfront-content-inner">
                        <h2>No Upfront Cost</h2>
                        <p>Unlock the potential of your restaurant with Go Grubz.
                            No upfront costs mean you can seamlessly integrate
                            online ordering and boost your business without
                            breaking the bank! Enhance your online visibility but also
                            keep the majority of your hard-earned income.</p>
                        <a class="btn cursor" routerLink="/add-restaurant" href="/add-restaurant">
                            Sign up your store
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="no-hidden-fees-bg">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 order-2 order-lg-1">
                <div class="no-hidden-content">
                    <div class="no-hidden-content-inner">
                        <h2>No Hidden Fees</h2>
                        <p>Choose financial clarity for your restaurant – Go Grubz
                            stands apart with a commitment to transparency,
                            guaranteeing no hidden fees. Enjoy the peace of mind
                            that comes with maximising your earnings while
                            offering customers a straightforward and honest
                            online ordering experience.</p>
                        <a class="btn cursor" routerLink="/add-restaurant" href="/add-restaurant">
                            Sign up your store
                        </a>
                    </div>
                    <div class="shape-one">
                        <img src="assets/images/hidden-shape-one.png" alt="Go-Grubz-shape">
                    </div>
                </div>
            </div>
            <div class="col-lg-6 order-1 order-lg-2">
                <div class="no-hidden-image">
                    <div class="shape-two">
                        <img src="assets/images/hidden-shape-two.png" alt="Go-Grubz-shape">
                    </div>
                    <img src="assets/images/no-hidden-fees-image.png" alt="Go-Grubz-hidden-fees-image" loading="lazy">
                </div>
            </div>
        </div>
    </div>
</section>

<section class="no-contracts-bg">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="no-contracts-image">
                    <div class="shape-one">
                        <img src="assets/images/no-contracts-shape-one.png" alt="Go-Grubz-shape">
                    </div>
                    <img src="assets/images/no-contracts-image.png" alt="Go-Grubz-contracts-image" loading="lazy">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="no-contracts-content">
                    <div class="no-contracts-inner-content">
                        <h2>No Contracts</h2>
                        <p>Choose Go Grubz for your restaurant and enjoy the
                            freedom of a no-contract partnership. Amplify your
                            online visibility and streamline order management
                            without any long-term commitments, putting you in
                            control of your success journey. If you aren’t satisfied
                            with our service or decide that it isn’t for you, we can
                            end the partnership no questions asked.</p>
                        <a class="btn cursor" routerLink="/add-restaurant" href="/add-restaurant">
                            Sign up your store
                        </a>
                    </div>
                    <div class="shape-two">
                        <img src="assets/images/no-contracts-shape-two.png" alt="Go-Grubz-shape">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="faq-section-bg">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="main-heading">
                    <h4>Frequently Asked Questions</h4>
                </div>
            </div>
            <div class="accordion" id="faq-accordions">
                <div class="accordion-item">
                    <div class="accordion-header">
                        <button class="accordion-button" data-bs-toggle="collapse" data-bs-target="#faq-accordions-one">
                            Question 1
                        </button>
                    </div>
                    <div id="faq-accordions-one" class="accordion-collapse collapse show"
                        data-bs-parent="#faq-accordions">
                        <div class="accordion-body">
                            <p>
                                Go Grubz is a vibrant and innovative restaurant that offers a
                                diverse menu of delicious and freshly prepared meals.
                                We aim to provide a convenient and delightful dining experience for
                                our customers.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <div class="accordion-header">
                        <button class="accordion-button collapsed" data-bs-toggle="collapse"
                            data-bs-target="#faq-accordions-two">
                            Question 2
                        </button>
                    </div>
                    <div id="faq-accordions-two" class="accordion-collapse collapse" data-bs-parent="#faq-accordions">
                        <div class="accordion-body">
                            <p>
                                Ordering from Go Grubz is easy! You can place an order through our
                                website, mobile app, or by calling our hotline.
                                Simply browse our menu, select your favorite dishes, and follow the
                                prompts to complete your order.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <div class="accordion-header">
                        <button class="accordion-button collapsed" data-bs-toggle="collapse"
                            data-bs-target="#faq-accordions-three">
                            Question 3
                        </button>
                    </div>
                    <div id="faq-accordions-three" class="accordion-collapse collapse" data-bs-parent="#faq-accordions">
                        <div class="accordion-body">
                            <p>
                                Yes, we do! Go Grubz provides fast and reliable delivery services to
                                bring your favorite meals straight to your doorstep.
                                You can choose the delivery option during the checkout process when
                                placing your order.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <div class="accordion-header">
                        <button class="accordion-button collapsed" data-bs-toggle="collapse"
                            data-bs-target="#faq-accordions-four">
                            Question 4
                        </button>
                    </div>
                    <div id="faq-accordions-four" class="accordion-collapse collapse" data-bs-parent="#faq-accordions">
                        <div class="accordion-body">
                            <p>
                                Our delivery service covers a wide range of areas. To check if we
                                deliver to your location,
                                enter your address on our website or app during the ordering
                                process.
                                You can also contact our customer support for assistance.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <div class="accordion-header">
                        <button class="accordion-button collapsed" data-bs-toggle="collapse"
                            data-bs-target="#faq-accordions-five">
                            Question 5
                        </button>
                    </div>
                    <div id="faq-accordions-five" class="accordion-collapse collapse" data-bs-parent="#faq-accordions">
                        <div class="accordion-body">
                            <p>
                                Absolutely! We understand that everyone has unique preferences. You
                                can customize your order by
                                adding or removing ingredients, choosing spice levels, or making
                                special requests.
                                Use the customization options available on our platform or specify
                                your
                                preferences when placing your order.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Phone-Number-Verification-Popup -->
<ng-template #otpModal let-modal>
    <div id="number-verification-popup">
        <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
            <div class="modal-body">
                <div class="d-flex justify-content-center">
                    <h6 class="ps-2">
                        Phone Number Verification
                    </h6>
                </div>
                <div class="verify-text">Before add restaurant, we need to verify your Phone Number for security
                    purposes
                </div>
                <div class="form-group">
                    <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                            <nz-input-group>
                                <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                                    (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                                    (keypress)="validateMobile($event)" [(ngModel)]="becomeRestaurant.otp" required
                                    placeholder="Enter 6-digit phone verification code">
                            </nz-input-group>
                            <ng-template #phoneVeriErrorTpl let-control>
                                <ng-container *ngIf="control.hasError('required')">
                                    Please enter phone verification code!
                                </ng-container>
                            </ng-template>
                        </nz-form-control>
                    </nz-form-item>
                </div>

                <div class="text-center">
                    <span class="d-inline-block w-100 pb-1">We sent a code to {{ becomeRestaurant.phone }}.</span>
                </div>

                <ul class="more-option">
                    <li><a class="d-flex text-nowrap cursor" (click)="resendOtp()"> Resend Code</a></li>
                </ul>

            </div>
            <div class="modal-footer">
                <nz-form-item *ngIf="Otperror">
                    <span class="text-danger">{{ Otperror }}</span>
                </nz-form-item>

                <button class="btn modal-black-btn" nz-button [disabled]="isOtpLoading">
                    <i class="spinner-border" *ngIf="isOtpLoading"></i>
                    Verify
                </button>
            </div>
        </form>
    </div>
</ng-template>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>