import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Error<PERSON>and<PERSON> } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { Complaint } from '../models/complaint';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ComplaintService {
  private url = `${environment.apiBaseUrl}complaints/`

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.order_id) params = params.set('order_id', options.order_id);
    if (options.user_id) params = params.set('user_id', options.user_id);
    if (options.status) params = params.set('status', options.status);
    if (options.admin) params = params.set('admin', options.admin);
    if (options.last_id) params = params.set('last_id', options.last_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Complaint> {
    return this.http.get<Complaint>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(complaint: Complaint): Observable<Complaint> {
    return this.http.post<Complaint>(this.url, Complaint.toFormData(complaint))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(complaint: Complaint): Observable<Complaint> {
    return this.http.post<Complaint>(this.url + complaint.id, Complaint.toFormData(complaint))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<Complaint> {
    return this.http.delete<Complaint>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  open(id: string): Observable<Complaint> {
    return this.http.post<Complaint>(this.url + id + '/open', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

  close(id: string): Observable<Complaint> {
    return this.http.post<Complaint>(this.url + id + '/close', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
