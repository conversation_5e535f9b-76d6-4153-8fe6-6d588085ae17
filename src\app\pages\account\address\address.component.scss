// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.fees-table {
  background: #fff;
  border-radius: 6px;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.10);
  text-align: center;

  tr td {
    padding: 8px !important;
    font-size: 15px;
    border-bottom: 1px solid #e1e1e1;
  }

  th {
    font-weight: 600;
    padding: 8px !important;
    text-transform: uppercase;
    background: linear-gradient(180deg, #fafafa, #f2f2f2);
  }
}
.save-your-payment p{
  font-size: 30px;
  font-weight:700;
  margin-bottom: 35px;
}
.save-your-payment span{
  font-size: 18px;
  color:#202020;
  display: block;
  margin-bottom: 35px;
}
.save-your-payment button.btn{
  font-size: 19px;
  font-weight: 500;
  font-family:'Visby CF';
  width: 220px;  
  text-align: center;
}
.save-your-payment button.btn svg{
  margin-left: 20px;
}
.card-address-box {
  max-width: 365px;
  position: relative;
  padding: 15px;
  border-radius: 10px;
  background-color: #FFFFFF;
  box-shadow: 0px 0px 18.999958038330078px 4.7499895095825195px #0000001A;
  margin-bottom: 15px;
  text-align: center;
}
.card-address-box img.map-icon{
  margin-bottom: 10px;
}
.card-address-box h6 {
  font-family: 'Visby CF';
  font-size: 20px;  
  line-height: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.card-address-box p {
  font-size: 16px;
  color:#8F8F8A;
  font-weight:700;
  line-height: 28px;
  word-break:break-word;
  margin-bottom: 0;
}
.edit-delete{
  width:365px;
  margin-bottom: 25px;
}
.edit-delete ul {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
}
.edit-delete ul li {
  margin:0 15px;
}
.edit-delete ul li button {
  padding: 0;
  color: #fff;
  font-size: 15px;
  width: 35px;
  height: 35px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0px 0px 4px 3px #0000001A;
  border: 1px dashed #242323;
  background-color:#242323;
}
.edit-delete ul li button.delete-btn {
  border-color: #fc353a;
  background-color:#fc353a;
}
.modal-body {
  padding: 20px 30px 30px 30px;
}
.modal-body .login-title {
  margin-bottom: 30px;
}
.modal-body .form-group {
  margin-bottom: 20px;
}
.modal-body .form-group input.form-control {
  font-size: 18px;
  height: 50px;
}
.modal-body .form-group.postcode-group input.form-control {
  padding-right: 105px;
}
.modal-body .form-group.postcode-group div.btn {
  width: 75px;
  line-height: 26px;
}
.modal-body .form-group.postcode-group div.btn:hover{
  color: #000 !important;
  background-color: #f4f3f3 !important;
}
.modal-body button.btn {
  width: 100%;
}
.error-text{
  font-size: 16px;
  font-weight: 500;
  color: red;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

@media screen and (max-width:1600px) {
  .card-address-box {
    max-width: 275px;
  }
  .edit-delete{
    width: 275px;
  }
}

@media screen and (max-width:1500px) {  
.save-your-payment{
  padding-top:30px;
}
.save-your-payment p{
  font-size: 22px;
}
.save-your-payment span{
  font-size:15px;
  font-weight:700;
  padding-bottom: 20px;
}
.save-your-payment button.btn {
  font-size: 15px;
  width: 165px;
  height: 45px;
  line-height: 30px;
}
.save-your-payment button.btn svg{
  margin-left: 15px;
}
.card-address-box{
  padding: 15px;
  border-radius: 8px;
}
.card-address-box h6{
  font-size: 17px;
}
.card-address-box p{
  font-size: 15px;
}
.edit-delete ul li {
  margin: 0 11px;
}
.edit-delete ul li button {
  width: 34px;
  height: 34px;
  line-height: 29px;
}
.modal-body .login-title {
  margin-bottom: 20px;
}
.modal-body{
  padding: 20px;
}
.modal-body .form-group {
  margin-bottom: 15px;
}
.modal-body .form-group.postcode-group input.form-control {
  padding-right: 80px;
}
.modal-body .form-group input.form-control {
  font-size: 13px;
  height: 36px;
  padding: 5px 15px;
}
.modal-body .form-group.postcode-group div.btn {
  width: 65px;
  line-height: 20px;
  height: 26px;
  top: 5px;
  right: 8px;
}
.modal-body .form-group.postcode-group div.btn i.spinner-border{
  position: relative;
  top: -1px;
  width: 10px;
  height: 10px;  
  margin-right: 1px;  
}
.modal-body button.btn {
  padding: 5px 15px;
}
.error-text{
  font-size: 13px;
}

}

@media screen and (max-width:1199px) {  
  .edit-delete ul li button {
    font-size: 13px;
    width: 30px;
    height: 30px;
    line-height: 28px;
  }
  
}

@media screen and (max-width:991px) {
.card-address-box {
  max-width: 225px;
  margin-bottom: 20px;
}
.card-address-box p {
  font-size: 14px;
  line-height: 22px;
}
.edit-delete{
  width: 225px;
}
.edit-delete ul li {
  margin: 0 10px;
}
.edit-delete ul li button {
  font-size: 12px;
  width: 28px;
  height: 28px;
  line-height: 23px;
}

}

@media screen and (max-width:575px) {  
  .card-address-box {
    max-width: 100%;
  }
  .edit-delete{
    margin: auto;
    margin-bottom: 25px;
  }  

}

@media screen and (max-width:480px) {
.error-text{
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: block;
}

}