<section class="checkout-page">

    <div class="loader-bg" *ngIf="isLoading || isCheckoutLoading || isOfferLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="checkout-section">
            <div class="login-box">
                <div class="step-box" *ngIf="!this.userService.user?.id">
                    <span class="step-number">1</span>
                    <h5 class="title" *ngIf="!isSignin && !isForgot && !isNewPass">Sign Up</h5>
                    <h5 class="title" *ngIf="isSignin && !isForgot && !isNewPass">Sign In</h5>
                    <h5 class="title" *ngIf="isForgot">Forgot Password</h5>
                    <h5 class="title" *ngIf="isNewPass">New Password</h5>

                    <div class="step-body-box">
                        <p class="dont-have-account-text" *ngIf="!isSignin && !isForgot && !isNewPass">
                            Already have an account? <button (click)="isSignin = true">Sign in here.</button>
                        </p>
                        <p class="dont-have-account-text" *ngIf="isSignin && !isForgot && !isNewPass">
                            Don’t have an account? <button (click)="isSignin = false">Sign up here.</button>
                        </p>
                        <form nz-form #signupForm="ngForm" (ngSubmit)="onSubmit(signupForm)" nzLayout="vertical"
                            *ngIf="!isSignin">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter first name!">
                                                <nz-input-group>
                                                    <input class="form-control" type="text" nz-input name="first_name"
                                                        (keydown)="onSpaceKeyDown($event)" id="first_name"
                                                        [(ngModel)]="signupuser.first_name" required
                                                        placeholder="First Name">
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter last name!">
                                                <nz-input-group>
                                                    <input class="form-control" type="text" nz-input name="last_name"
                                                        (keydown)="onSpaceKeyDown($event)" id="last_name"
                                                        [(ngModel)]="signupuser.last_name" required
                                                        placeholder="Last Name">
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="signEmailErrorTpl">
                                                <nz-input-group>
                                                    <input class="form-control" type="email" email="true"
                                                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
                                                        #chesignEmail="ngModel" nz-input name="username"
                                                        (keydown.space)="onSpaceKeyDown($event)" id="username"
                                                        [(ngModel)]="signupuser.username" required
                                                        placeholder="Email Address">
                                                </nz-input-group>
                                                <ng-template #signEmailErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your email!
                                                    </ng-container>
                                                    <ng-container
                                                        *ngIf="control.hasError('email') || (!control.hasError('required') && chesignEmail.touched) || (!control.hasError('required') && !chesignEmail.valid)">
                                                        Email must be a valid email address
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter phone number!">
                                                <nz-input-group>
                                                    <input class="form-control" type="text" inputmode="numeric" nz-input
                                                        name="phone_number" id="phone_number"
                                                        (keypress)="validateMobile($event)"
                                                        [(ngModel)]="signupuser.phone_number" required
                                                        placeholder="Mobile Number">
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="passwordErrorTpl">
                                                <nz-input-group>
                                                    <input class="form-control" [type]="hide ? 'password' : 'text'"
                                                        (keydown.space)="onSpaceKeyDown($event)" nz-input
                                                        name="password" id="password" minlength="6"
                                                        [(ngModel)]="signupuser.password" required
                                                        placeholder="Password">
                                                    <span class="icon" (click)="hidePassword()">
                                                        <img src="assets/images/eye.svg" *ngIf="hide"
                                                            alt="Go-Grubz-Eye-Image" loading="lazy">
                                                        <img src="assets/images/eye-off.svg" *ngIf="!hide"
                                                            alt="Go-Grubz-Eye-Off-Image" loading="lazy">
                                                    </span>
                                                </nz-input-group>

                                                <ng-template #passwordErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your password!
                                                    </ng-container>
                                                    <ng-container *ngIf="control.hasError('minlength')">
                                                        Password must be atleast 6 characters long!
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                                                <nz-input-group>
                                                    <input class="form-control" [type]="chide ? 'password' : 'text'"
                                                        nz-input name="confirmPassword" id="confirmPassword"
                                                        (keydown.space)="onSpaceKeyDown($event)"
                                                        [(ngModel)]="signupuser.confirmPassword" required
                                                        placeholder="Confirm Password">
                                                    <span class="icon" (click)="hideCpassword()">
                                                        <img src="assets/images/eye.svg" *ngIf="chide"
                                                            alt="Go-Grubz-Eye-Image" loading="lazy">
                                                        <img src="assets/images/eye-off.svg" *ngIf="!chide"
                                                            alt="Go-Grubz-Eye-Off-Image" loading="lazy">
                                                    </span>
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter referred code!">
                                                <nz-input-group>
                                                    <input class="form-control" type="text" nz-input name="referred_by"
                                                        (keydown.space)="onSpaceKeyDown($event)" id="referred_by"
                                                        [(ngModel)]="signupuser.referred_by"
                                                        placeholder="Referral Code (optional)">
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                            </div>
                            <div class="terms-conditions">
                                <p>
                                    By signing up or using “Continue with Google, Facebook, or Apple,” you agree to Go
                                    Grubz’s <a href="/terms-condition" target="_blank">Terms and Conditions</a>
                                    and <a href="/privacy-policy" target="_blank">Privacy Policy.</a>
                                </p>
                            </div>

                            <nz-form-item *ngIf="errorSignup">
                                <span class="text-danger">{{ errorSignup }}</span>
                            </nz-form-item>

                            <button class="btn w-100" nz-button [disabled]="isSignupLoading">
                                <i class="spinner-border" *ngIf="isSignupLoading"></i>
                                Sign Up
                            </button>
                        </form>
                        <form nz-form #loginForm="ngForm" (ngSubmit)="loginSubmit(loginForm)" nzLayout="vertical"
                            *ngIf="isSignin && !isForgot && !isNewPass">
                            <div class="row">

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="loginEmailErrorTpl">
                                                <nz-input-group>
                                                    <input class="form-control" type="email" nz-input email="true"
                                                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
                                                        #checklogEmail="ngModel" name="username"
                                                        (keydown.space)="onSpaceKeyDown($event)" id="username"
                                                        [(ngModel)]="loginuser.username" required
                                                        placeholder="Email Address">
                                                </nz-input-group>
                                                <ng-template #loginEmailErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your email!
                                                    </ng-container>
                                                    <ng-container
                                                        *ngIf="control.hasError('email') || (!control.hasError('required') && checklogEmail.touched) || (!control.hasError('required') && !checklogEmail.valid)">
                                                        Email must be a valid email address
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="passwordLoginErrorTpl">
                                                <nz-input-group>
                                                    <input class="form-control" [type]="lhide ? 'password' : 'text'"
                                                        nz-input name="password" id="password"
                                                        (keydown.space)="onSpaceKeyDown($event)"
                                                        [(ngModel)]="loginuser.password" required
                                                        placeholder="Password">
                                                    <span class="icon" (click)="hideLpassword()">
                                                        <img src="assets/images/eye.svg" *ngIf="lhide"
                                                            alt="Go-Grubz-Eye-Image" loading="lazy">
                                                        <img src="assets/images/eye-off.svg" *ngIf="!lhide"
                                                            alt="Go-Grubz-Eye-Off-Image" loading="lazy">
                                                    </span>
                                                </nz-input-group>

                                                <ng-template #passwordLoginErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your password!
                                                    </ng-container>
                                                    <ng-container *ngIf="control.hasError('minlength')">
                                                        Password must be atleast 6 characters long!
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>

                            </div>

                            <nz-form-item *ngIf="errorLogin">
                                <span class="text-danger">{{ errorLogin }}</span>
                            </nz-form-item>
                            <div class="forgot-password text-end">
                                <span class="cursor" (click)="isForgot = true">Forgot Password?</span>
                            </div>
                            <button class="btn w-100" nz-button [disabled]="isLoginLoading">
                                <i class="spinner-border" *ngIf="isLoginLoading"></i>
                                Sign In
                            </button>
                        </form>
                        <!-- <div class="or-option"
                            *ngIf="!isForgot && !isNewPass && (siteSetting.google_login == 'Y' || siteSetting.facebook_login == 'Y' || siteSetting.apple_login == 'Y')">
                            <span>or</span>
                        </div>
                        <div class="socials-login-options" *ngIf="!isForgot && !isNewPass">
                            <ul>
                                <li *ngIf="siteSetting.google_login == 'Y'">
                                    <a class="google-btn" (click)="loginWithGoogle()">
                                        <img src="assets/images/google.svg" alt="google">
                                        Continue with Google
                                    </a>
                                </li>
                                <li *ngIf="siteSetting.facebook_login == 'Y'">
                                    <a class="facebook-btn" (click)="loginWithFacebook()">
                                        <img src="assets/images/facebook.svg" alt="google">
                                        Continue with Facebook
                                    </a>
                                </li>
                                <li *ngIf="siteSetting.apple_login == 'Y'">
                                    <a class="apple-btn">
                                        <img src="assets/images/white-apple.svg" alt="google">
                                        Continue with Apple
                                    </a>
                                </li>
                            </ul>
                        </div> -->
                    </div>
                    <div class="step-body-box" *ngIf="isForgot || isNewPass">
                        <!--<p class="dont-have-account-text">
                            Already have an account? <a class="cursor" (click)="openSignin()">Sign in here.</a>
                        </p>-->
                        <p *ngIf="!otpVisible" class="dont-have-account-text">Please enter the e-mail address used to
                            register. We will send your new OTP to that address.</p>
                        <p *ngIf="otpVisible" class="dont-have-account-text text-success"> Check your email for OTP.</p>
                        <form nz-form #forgotForm="ngForm" (ngSubmit)="onSubmitForgot(forgotForm)"
                            *ngIf="!otpVisible && !isNewPass">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="forgotEmailErrorTpl">
                                                <nz-input-group>
                                                    <input class="form-control" type="email" nz-input email="true"
                                                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
                                                        #checkforEmail="ngModel"
                                                        (keydown.space)="onSpaceKeyDown($event)"
                                                        [(ngModel)]="forgotUser.username" required
                                                        name="otp-verification-email" id="otp-verification-email"
                                                        placeholder="Enter your email">
                                                </nz-input-group>
                                                <ng-template #forgotEmailErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your email!
                                                    </ng-container>
                                                    <ng-container
                                                        *ngIf="control.hasError('email') || (!control.hasError('required') && checkforEmail.touched) || (!control.hasError('required') && !checkforEmail.valid)">
                                                        Please enter a valid email address!
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>
                            </div>

                            <nz-form-item *ngIf="ForgotModelerror">
                                <span class="text-danger">{{ ForgotModelerror }}</span>
                            </nz-form-item>

                            <button class="btn w-100" nz-button [disabled]="isForgotModelLoading">
                                <i class="spinner-border" *ngIf="isForgotModelLoading"></i>
                                Send
                            </button>
                            <div class="back-to-login">
                                <span>Back to</span>
                                <a class="cursor" (click)="isSignin = true;isForgot = false">Sign in</a>
                            </div>
                        </form>

                        <form nz-form #otpForm="ngForm" (ngSubmit)="onNewSubmitOtp(otpForm)" *ngIf="otpVisible">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Enter OTP
                                        </label>
                                        <!-- <div class="form-otp-list">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 0)"
                                                name="otp-input-1" id="otp-input-1" autocomplete="off">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 1)"
                                                name="otp-input-2" id="otp-input-2" autocomplete="off">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 2)"
                                                name="otp-input-3" id="otp-input-3" autocomplete="off">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 3)"
                                                name="otp-input-4" id="otp-input-4" autocomplete="off">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 4)"
                                                name="otp-input-5" id="otp-input-5" autocomplete="off">
                                            <input class="form-control" type="text" inputmode="numeric" nz-input
                                                required maxlength="1" (input)="handleInput($event, 5)"
                                                name="otp-input-6" id="otp-input-6" autocomplete="off">
                                        </div>
                                        <input type="text" nz-input [value]="emailOtp" hidden readonly /> -->
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                                                <nz-input-group>
                                                    <input type="text" inputmode="numeric" class="form-control" nz-input
                                                        minlength="6" (keydown.space)="onSpaceKeyDown($event)"
                                                        maxlength="6" id="email_otp" name="email_otp"
                                                        (keypress)="validateMobile($event)"
                                                        [(ngModel)]="forgotUser.email_otp" required
                                                        placeholder="Enter 6-digit code">
                                                </nz-input-group>
                                                <ng-template #phoneVeriErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter verification code!
                                                    </ng-container>
                                                    <ng-container *ngIf="control.hasError('minlength')">
                                                        statement verification code at least 6 digit!
                                                    </ng-container>
                                                    <ng-container *ngIf="control.hasError('maxlength')">
                                                        statement verification code maximum 6 digits long!
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>

                                    </div>
                                </div>
                            </div>

                            <nz-form-item *ngIf="Otperror">
                                <span class="text-danger">{{ Otperror }}</span>
                            </nz-form-item>

                            <button class="btn" nz-button [disabled]="isOtpLoading">
                                <i class="spinner-border" *ngIf="isOtpLoading"></i>
                                Verify
                            </button>
                            <div class="resend-code text-center">
                                <a class="cursor">
                                    Resend code
                                </a>
                            </div>
                        </form>
                    </div>

                    <div class="step-body-box" *ngIf="isNewPass">
                        <form nz-form #changePasswordForm="ngForm"
                            (ngSubmit)="onChangePasswordSubmit(changePasswordForm)">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter New Password"> -->
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback [nzErrorTip]="passwordChnageErrorTpl">
                                                <nz-input-group>
                                                    <input type="password" id="password" name="password"
                                                        [(ngModel)]="forgotUser.password" required minlength="6"
                                                        placeholder="Enter new password" class="form-control">
                                                </nz-input-group>

                                                <ng-template #passwordChnageErrorTpl let-control>
                                                    <ng-container *ngIf="control.hasError('required')">
                                                        Please enter your current password!
                                                    </ng-container>
                                                    <ng-container *ngIf="control.hasError('minlength')">
                                                        Password must be atleast 6 characters long!
                                                    </ng-container>
                                                </ng-template>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter Confirm Password"> -->
                                        <nz-form-item>
                                            <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                                                <nz-input-group>
                                                    <input type="password" id="confirmPassword" name="confirmPassword"
                                                        [(ngModel)]="forgotUser.confirmPassword" required
                                                        placeholder="Enter your confirm password" class="form-control">
                                                </nz-input-group>
                                            </nz-form-control>
                                        </nz-form-item>
                                    </div>
                                </div>
                            </div>

                            <nz-form-item *ngIf="errorChangePassword">
                                <span class="text-danger">{{ errorChangePassword }}</span>
                            </nz-form-item>

                            <button class="btn" nz-button [disabled]="isChangePasswordLoading">
                                <i class="spinner-border" *ngIf="isChangePasswordLoading"></i>
                                Submit
                            </button>
                        </form>
                    </div>
                </div>

                <div class="step-box" *ngIf="!this.userService.user?.id">
                    <span class="step-number">2</span>
                    <h5 class="title" *ngIf="order.order_type == 'delivery'">
                        Delivery Details
                    </h5>
                    <h5 class="title" *ngIf="order.order_type == 'pickup'">
                        Pickup Details
                    </h5>
                </div>

                <div class="step-box" *ngIf="!this.userService.user?.id">
                    <span class="step-number">3</span>
                    <h5 class="title">
                        Payment Details
                    </h5>
                </div>

                <div class="step-box" *ngIf="this.userService.user?.id">
                    <span class="step-number">1</span>
                    <h5 class="title">
                        Account Details
                    </h5>
                    <span class="success-icon">
                        <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image" loading="lazy">
                    </span>
                    <span class="account-email w-100 d-inline-block text-center">
                        {{ user?.username }}
                    </span>
                </div>

                <div class="step-box" *ngIf="this.userService.user?.id">
                    <span class="step-number">2</span>
                    <h5 class="title" *ngIf="order.order_type == 'delivery'">Delivery Details</h5>
                    <h5 class="title" *ngIf="order.order_type == 'pickup'">Pickup Details</h5>
                    <div class="step-body-box">
                        <div class="delivery-pickup-box">
                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <button class="nav-link"
                                        [style.pointer-events]="order.order_type == 'delivery' ?'none':'auto'"
                                        [ngClass]="{'active' : order.order_type == 'delivery','pe-none' : restaurant.restaurant_delivery != 'Yes'}"
                                        (click)="orderType('delivery')">Delivery
                                        <span class="unavailable-text" *ngIf="restaurant?.restaurant_delivery != 'Yes'">
                                            Unavailable </span>
                                    </button>
                                </li>
                                <li class="nav-item">
                                    <button class="nav-link"
                                        [style.pointer-events]="order.order_type == 'pickup' ?'none':'auto'"
                                        [ngClass]="{'active' : order.order_type == 'pickup','pe-none' : restaurant.restaurant_pickup != 'Yes'}"
                                        (click)="orderType('pickup')">Pickup
                                        <span class="unavailable-text" *ngIf="restaurant?.restaurant_pickup != 'Yes'">
                                            Unavailable </span>
                                    </button>
                                </li>
                            </ul>
                            <div class="delivery-time">
                                <span *ngIf="order.order_type == 'delivery'">
                                    <svg class="fa-regular fa-clock"></svg> Delivery Time
                                </span>
                                <span *ngIf="order.order_type == 'pickup'">
                                    <svg class="fa-regular fa-clock"></svg> Pickup Time
                                </span>
                                <span *ngIf="order.order_type == 'delivery' && order.assoonas != 'later'">
                                    ASAP
                                </span>
                                <span *ngIf="order.order_type == 'delivery' && order.assoonas == 'later'">
                                    {{ order.delivery_date }} & {{ order.delivery_time }}
                                </span>
                                <span *ngIf="order.order_type == 'pickup' && order.assoonas !='later'">
                                    ASAP
                                </span>
                                <span *ngIf=" order.order_type=='pickup' && order.assoonas == 'later'">
                                    {{ order.delivery_date }} & {{ order.delivery_time }}
                                </span>
                            </div>
                            <div class="delivery-pickup-list">
                                <div class="delivery-list-box" *ngIf="restaurant.currentStatus == 'Open'">
                                    <button class="cursor" (click)="deliveryNow('now')">
                                        <h6>ASAP</h6>
                                        <p *ngIf="order.order_type == 'delivery'">As soon as possible</p>
                                        <p *ngIf="order.order_type == 'pickup'">As soon as possible</p>
                                        <!-- <p *ngIf="order.order_type == 'pickup'">{{ restaurant.pickup_estimate_time }}
                                            min</p> -->
                                        <span *ngIf="order.assoonas == 'now'"><svg
                                                class=" fa-solid fa-circle-check"></svg></span>
                                        <span *ngIf="order.assoonas 
                                            != 'now'"><svg class=" fa-solid fa-circle"></svg></span>
                                    </button>
                                </div>
                                <div class="delivery-list-box">
                                    <button class="cursor" (click)="deliveryNow('later')">
                                        <h6>Schedule for later</h6>
                                        <p
                                            *ngIf="order.assoonas != 'later'  || !order.delivery_date || !order.delivery_time">
                                            Choose a time</p>
                                        <p
                                            *ngIf="order.assoonas == 'later' && order.delivery_date && order.delivery_time">
                                            {{ order.delivery_date }} & {{ order.delivery_time }}</p>
                                        <span *ngIf="order.assoonas == 'later'"><svg
                                                class=" fa-solid fa-circle-check"></svg></span>
                                        <span *ngIf="order.assoonas 
                                            != 'later'"><svg class=" fa-solid fa-circle"></svg></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="contact-details" *ngIf="order.order_type == 'delivery'">
                            <ul>
                                <li>
                                    <button class="cursor" (click)="openAddress()">
                                        <div class="icon">
                                            <svg class="fa-solid fa-house"></svg>
                                        </div>
                                        <div class="address">
                                            <p *ngIf="!order.address_id">Choose Address</p>
                                            <p *ngIf="order.address_id">Selected Address</p>
                                            <span *ngIf="order.address_id">{{ order.address }}</span>
                                        </div>
                                        <div class="right-arrow">
                                            <svg class="fa-solid fa-chevron-right"></svg>
                                        </div>
                                    </button>
                                </li>
                                <li>
                                    <button class="cursor" (click)="openAddress()">
                                        <div class="icon">
                                            <svg class="fa-solid fa-door-open"></svg>
                                        </div>
                                        <div class="address">
                                            <p>Leave it at my door</p>
                                            <span>Add more details</span>
                                        </div>
                                        <div class="right-arrow">
                                            <svg class="fa-solid fa-chevron-right"></svg>
                                        </div>
                                    </button>
                                </li>
                                <li>
                                    <button class="cursor" (click)="openPhoneEdit()">
                                        <div class="icon">
                                            <svg class="fa-solid fa-phone"></svg>
                                        </div>
                                        <div class="address">
                                            <p>{{ user?.phone_number }}</p>
                                        </div>
                                        <div class="right-arrow">
                                            <svg class="fa-solid fa-chevron-right"></svg>
                                        </div>
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <div class="contact-details" *ngIf="order.order_type == 'pickup'">
                            <ul>
                                <li>
                                    <button class="cursor">
                                        <div class="icon">
                                            <svg class="fa-solid fa-house"></svg>
                                        </div>
                                        <div class="address">
                                            <p>{{ restaurant.restaurant_name }}</p>
                                            <span>{{ restaurant.street_address }}</span>
                                        </div>
                                    </button>
                                </li>
                            </ul>
                        </div>

                        <div class="contact-details" *ngIf="order.order_type == 'pickup'">
                            <ul>
                                <li>
                                    <button class="cursor" (click)="openAddress()">
                                        <div class="icon">
                                            <!--<svg class="fa-solid fa-door-open"></svg>-->
                                            <img src="assets/images/take-away.png" alt="Go-Grubz-take-away-image">
                                        </div>
                                        <div class="address">
                                            <p>Take away Details</p>
                                            <span>Add more details</span>
                                        </div>
                                        <div class="right-arrow">
                                            <svg class="fa-solid fa-chevron-right"></svg>
                                        </div>
                                    </button>
                                </li>
                            </ul>
                        </div>

                    </div>
                </div>

                <div class="step-box" *ngIf="this.userService.user?.id">
                    <span class="step-number">3</span>
                    <h5 class="title">Payment Details</h5>
                    <div class="step-body-box">
                        <div class="my-wallet-box"
                            *ngIf="siteSetting.wallet_available == 'Yes' && restaurant.wallet_payment == 'Yes'">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="wallet-checkbox" name="checkbox1"
                                    (change)="walletAdd($event)" [(ngModel)]="order.payment_wallet"
                                    [value]="order.payment_wallet" [disabled]="user.wallet_amount <= 0">
                                <label class="form-check-label" for="wallet-checkbox">
                                    <svg class="fa-solid fa-wallet"></svg> My Wallet
                                </label>
                            </div>
                            <p>Current Balance : <span
                                    class="wallet-amount">{{convertNumber(user.wallet_amount)}}</span></p>
                            <p class="paid-amount" *ngIf="order.wallet_amount > 0">Paid from wallet : <span
                                    class="wallet-amount">{{convertNumber(order.wallet_amount)}}</span></p>
                        </div>

                        <div class="accordion" id="payment-details" *ngIf="stripePaymentMethod.payment_status == 'Y'"
                            [class.d-none]="(order.payment_wallet && user.wallet_amount >= order.order_grand_total + order.charity_amount)">
                            <div class="accordion-item" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
                                <div class="accordion-header">
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.card_id != stripeCustomer.id,'collapsed' : order.payment_method != 'Stripe'}"
                                        (click)="cardPaymentMethod(stripeCustomer.id)">
                                        <!-- data-bs-toggle="collapse" data-bs-target="#creditpayment{{ order.card_id }}" -->
                                        <span class="check"
                                            *ngIf="order.card_id == stripeCustomer.id && order.payment_method == 'Stripe'">
                                            <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                loading="lazy">
                                        </span>
                                        <span class="check"
                                            *ngIf="!order.card_id || order.card_id != stripeCustomer.id || order.payment_method != 'Stripe'">
                                            <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                        </span>
                                        {{ stripeCustomer.card_brand | uppercase }} xxxx{{stripeCustomer.card_number}}
                                        Valid till {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}} <i
                                            class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>

                                <!-- <div id="creditpayment{{ order.card_id }}" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.card_id == stripeCustomer.id}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <ul class="credit-card-list">
                                            <li>
                                                <img src="assets/images/american-card.png" alt="american-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/visa-card.png" alt="visa-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/master-card.png" alt="master-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/other-credit-card.png" alt="other-credit-card">
                                            </li>
                                        </ul>
                                    </div>
                                </div> -->

                            </div>
                        </div>

                        <div class="accordion" id="payment-details"
                            *ngFor=" let paymentMethod of restaurant?.payment_methods;"
                            [class.d-none]="(order.payment_wallet && user.wallet_amount >= order.order_grand_total + order.charity_amount)">
                            <div class="accordion-item"
                                *ngIf="paymentMethod.payment_method_name=='Stripe' && paymentMethod.payment_status == 'Y'">
                                <div class="accordion-header">
                                    <!-- <a class="cursor add-card-option" (click)="addCard(addNewCardPopup)"> -->
                                    <!-- <a class="cursor add-card-option">
                                        <svg class="fa-solid fa-plus"></svg>
                                        Pay By
                                    </a> -->
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.payment_method == 'Stripe' && !order.card_id}"
                                        (click)="checkPaymentMethod('Stripe')">
                                        <!-- data-bs-toggle="collapse" data-bs-target="#creditpayment{{ order.card_id }}" -->
                                        <span class="check" *ngIf="order.payment_method == 'Stripe' && !order.card_id">
                                            <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                loading="lazy">
                                        </span>
                                        <span class="check" *ngIf="order.payment_method != 'Stripe' ||  order.card_id">
                                            <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                        </span>
                                        Pay By Card <ul class="credit-card-list">
                                            <li>
                                                <img src="assets/images/american-card.png" alt="american-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/visa-card.png" alt="visa-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/master-card.png" alt="master-card">
                                            </li>
                                            <li>
                                                <img src="assets/images/other-credit-card.png" alt="other-credit-card">
                                            </li>
                                        </ul> <i class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>

                                <div id="creditpayment{{ order.payment_method }}" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.payment_method == 'Stripe' && !order.card_id}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <div class="pt-2">
                                            <form nz-form #cardInfo="ngForm" (ngSubmit)="handleForm($event)"
                                                nzLayout="vertical">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group">
                                                            <div class="input-group">
                                                                <span id="card-number"
                                                                    class="form-control rounded"></span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <span id="card-exp" nz-input
                                                                class="form-control rounded"></span>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <span id="card-cvc" class="form-control rounded"> </span>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <span id="postalCode" nz-input
                                                                class="form-control rounded"></span>
                                                        </div>
                                                    </div>

                                                    <nz-form-item *ngIf="Modelerror">
                                                        <span class="text-danger">{{ Modelerror }}</span>
                                                    </nz-form-item>
                                                    <div class="next-btn pl-2 pr-2">
                                                        <button
                                                            class="btn w-100 justify-content-between d-none d-xl-flex mt-2"
                                                            *ngIf="order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                                            [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
                                                            nz-button>
                                                            <span>
                                                                Place Order
                                                            </span>
                                                            <span>
                                                                Total: {{ convertNumber(getGrandTotal()) }}
                                                            </span>
                                                        </button>
                                                        <button
                                                            class="btn w-100 justify-content-between d-none d-xl-flex mt-2"
                                                            *ngIf="order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                                            [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
                                                            nz-button>
                                                            <span>
                                                                Place Order
                                                            </span>
                                                            <span>
                                                                Total: {{ convertNumber(getGrandTotal()) }}
                                                            </span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="accordion-item"
                                *ngIf="(paymentMethod.payment_method_name =='COD' || paymentMethod.payment_method_name =='cod') && paymentMethod.payment_status == 'Y'">
                                <div class="accordion-header">
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.payment_method != 'COD' || order.payment_method != 'cod'}"
                                        (click)="checkPaymentMethod(paymentMethod.payment_method_name)">
                                        <!-- data-bs-toggle="collapse" data-bs-target="#cashpayment" -->
                                        <span class="check"
                                            *ngIf="order.payment_method =='COD' || order.payment_method =='cod'">
                                            <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                loading="lazy">
                                        </span>
                                        <span class="check"
                                            *ngIf="order.payment_method != 'COD' || order.payment_method != 'cod'">
                                            <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                        </span>
                                        Pay By Cash <img src="assets/images/pound.png" alt="cash-payment"> <i
                                            class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>
                                <!-- <div id="cashpayment" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.payment_method == 'COD'}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <img src="assets/images/cash-payment.png" alt="cash-payment">
                                    </div>
                                </div> -->
                            </div>
                            <div class="accordion-item"
                                *ngIf="paymentMethod.payment_method_name=='Apple Pay' && paymentMethod.payment_status == 'Y'">
                                <div class="accordion-header">
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.payment_method == 'Apple Pay'}"
                                        (click)="checkPaymentMethod('Apple Pay')">
                                        <div class="d-flex align-items-center">
                                            <!-- data-bs-toggle="collapse" data-bs-target="#creditpayment{{ order.card_id }}" -->
                                            <span class="check" *ngIf="order.payment_method == 'Apple Pay'">
                                                <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                    loading="lazy">
                                            </span>
                                            <span class="check" *ngIf="order.payment_method != 'Apple Pay'">
                                                <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                            </span>
                                            Pay By
                                            <ul class="pay-icons-list">
                                                <li>
                                                    <img src="assets/images/apple-pay.png" alt="apple-pay">
                                                </li>
                                                <li>
                                                    <img src="assets/images/google-pay.png" alt="google-pay">
                                                </li>
                                                <li>
                                                    <img src="assets/images/link-pay.png" alt="link-pay">
                                                </li>
                                            </ul>
                                        </div>
                                        <!-- Pay By Link / Apple pay / Google Pay -->
                                        <i class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>

                                <div id="creditpayment{{ order.payment_method }}" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.payment_method == 'Apple Pay'}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <div class="pt-2">
                                            <div id="payment-request-button"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item"
                                *ngIf="paymentMethod.payment_method_name=='Paypal' && paymentMethod.payment_status == 'Y'">
                                <div class="accordion-header">
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.payment_method != 'Paypal'}"
                                        (click)="checkPaymentMethod('Paypal')">
                                        <div class="d-flex align-items-center">
                                            <!-- data-bs-toggle="collapse" data-bs-target="#creditpayment{{ order.card_id }}" -->
                                            <span class="check" *ngIf="order.payment_method == 'Paypal'">
                                                <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                    loading="lazy">
                                            </span>
                                            <span class="check" *ngIf="order.payment_method != 'Paypal'">
                                                <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                            </span>
                                            Pay By PayPal
                                            <ul class="pay-icons-list">
                                                <li>
                                                    <img src="assets/images/paypal.png" alt="paypal">
                                                </li>
                                            </ul>
                                        </div>
                                        <i class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>
                                <div id="paypalpayment" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.payment_method == 'Paypal'}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <div id="paypal-button-container" class="pt-2"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="accordion-item"
                                *ngIf="paymentMethod.payment_method_name=='Revolut Pay' && paymentMethod.payment_status == 'Y'">
                                <div class="accordion-header">
                                    <button class="cursor d-flex justify-content-between align-items-center"
                                        [ngClass]="{'collapsed' : order.payment_method != 'Revolut Pay'}"
                                        (click)="checkPaymentMethod('Revolut Pay')">
                                        <div class="d-flex align-items-center">
                                             <span class="check" *ngIf="order.payment_method == 'Revolut Pay'">
                                                <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                                                    loading="lazy">
                                            </span> 
                                            <span class="check" *ngIf="order.payment_method != 'Revolut Pay'">
                                                <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                                            </span>
                                            Pay By Revolut Pay
                                             <ul class="pay-icons-list">
                                                <li>
                                                    <img src="assets/images/revolut.png" alt="apple-pay">
                                                </li>
                                            </ul>
                                        </div>
                                        <i class="fa-solid fa-chevron-down"></i>
                                    </button>
                                </div>
                                <div id="revolutpayment" class="accordion-collapse collapse"
                                    [ngClass]="{'show' : order.payment_method == 'Revolut Pay'}"
                                    data-bs-parent="#payment-details">
                                    <div class="accordion-body">
                                        <div id="revolut-pay-button-container" class="pt-2"></div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="next-btn text-center">
                            <nz-form-item *ngIf="errorPaymentMethod">
                                <span class="text-danger fw-bold">{{ errorPaymentMethod }}</span>
                            </nz-form-item>
                            <nz-form-item *ngIf="errorCheckout">
                                <span class="text-danger fw-bold">{{ errorCheckout }}</span>
                            </nz-form-item>
                            <button class="btn w-100 justify-content-between d-none d-xl-flex mt-2"
                                *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
                                (click)="validateCheckout()" nz-button>
                                <span>
                                    Place Order
                                </span>
                                <span>
                                    Total: {{ convertNumber(getGrandTotal()) }}
                                </span>
                            </button>
                            <button class="btn w-100 justify-content-between d-none d-xl-flex mt-2"
                                *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
                                (click)="validateCheckout()" nz-button>
                                <span>
                                    Place Order
                                </span>
                                <span>
                                    Total: {{ convertNumber(getGrandTotal()) }}
                                </span>
                            </button>
                            <span class="blink-animation"
                                *ngIf="order.order_type == 'delivery' && restaurant.minimum_order >= order.order_sub_total">The
                                minimum order value is {{convertNumber(restaurant.minimum_order)}}.</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="add-to-cart-box-two">
                <div class="d-flex h-100">
                    <div class="add-cart-box">
                        <div class="cart-top border-end-0">
                            <div class="top-cart-name">
                                <div class="cart-logo">
                                    <img [src]="restaurant?.image_url" [alt]="restaurant.restaurant_name"
                                        onerror="this.src='./assets/favicon.png';">
                                </div>
                                <div class="cart-name">
                                    <h6>Your Basket<img src="assets/images/cart.svg" alt="Go-Grubz-Cart-Image"
                                            loading="lazy"></h6>
                                    <p>{{ restaurant.restaurant_name }}</p>
                                </div>
                            </div>
                            <div class="checkout-btn">
                                <nz-form-item *ngIf="errorPaymentMethod">
                                    <span class="text-danger fw-bold">{{ errorPaymentMethod }}</span>
                                </nz-form-item>
                                <nz-form-item *ngIf="Modelerror">
                                    <span class="text-danger fw-bold">{{ Modelerror }}</span>
                                </nz-form-item>
                                <nz-form-item *ngIf="errorCheckout">
                                    <span class="text-danger fw-bold">{{ errorCheckout }}</span>
                                </nz-form-item>

                                <button class="btn"
                                    *ngIf="(order.payment_method == 'Stripe' && !order.card_id) && order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                    [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
                                    (click)="cardInfo.ngSubmit.emit($event)" nz-button>
                                    <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
                                    <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
                                    <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
                                </button>

                                <button class="btn"
                                    *ngIf="(order.payment_method == 'Stripe' && !order.card_id) && order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                    [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
                                    (click)="cardInfo.ngSubmit.emit($event)" nz-button>
                                    <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
                                    <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
                                    <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
                                </button>

                                <button class="btn"
                                    *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                    [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
                                    (click)="validateCheckout()" nz-button>
                                    <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
                                    <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
                                    <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
                                </button>

                                <button class="btn"
                                    *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
                                    [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
                                    (click)="validateCheckout()" nz-button>
                                    <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
                                    <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
                                    <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
                                </button>

                                <span class="blink-animation"
                                    *ngIf="order.order_type == 'delivery' && restaurant.minimum_order >= order.order_sub_total">The
                                    minimum order value is {{convertNumber(restaurant.minimum_order)}}.</span>
                            </div>
                        </div>



                        <div class="cart-middle">

                            <div *ngIf="carts?.length == 0">
                                <div class="empty-cart-cls text-center py-5">
                                    <img src="assets/cartitem.png" alt="GoGrubz-cart-item" loading="lazy">
                                    <p><strong>No Item(s) Added</strong></p>
                                </div>
                            </div>

                            <div *ngIf="carts?.length > 0">
                                <div class="cart-item" *ngFor="let cart of carts; let i=index ; trackBy: trackByFn">
                                    <div class="cart-image-item"
                                        *ngIf="cart?.image_url && restaurant?.image_type == 'Yes'">
                                        <img [src]="cart?.image_url" [alt]="cart.menu_name">
                                    </div>
                                    <div class="cart-content">
                                        <h6>{{cart.menu_name}}</h6>
                                        <ul *ngIf="cart.subaddons_name">
                                            <li>{{cart.subaddons_name}}</li>
                                        </ul>
                                        <p>{{convertNumber(cart?.total_price)}}</p>
                                    </div>
                                    <div class="cart-add-item">
                                        <ul>
                                            <li>
                                                <button class="cursor"
                                                    [style.pointer-events]="isUpdateCart ?'none':'auto'"
                                                    (click)="updateToCart(cart,i,'remove')">
                                                    <svg class="fa-solid fa-minus"></svg>
                                                </button>
                                            </li>
                                            <li><span>{{ cart.quantity }}</span></li>
                                            <li><button class="cursor"
                                                    [style.pointer-events]="isUpdateCart ?'none':'auto'"
                                                    (click)="updateToCart(cart,i,'add')"><svg
                                                        class="fa-solid fa-plus"></svg></button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="offfer-heading" *ngIf="order?.applied_offers?.length > 0">
                                <h6>
                                    <img src="assets/images/offer-icon.png" alt="Go-Grubz-offer-image">
                                    Your Offer
                                </h6>
                            </div>
                            <div *ngIf="order?.applied_offers?.length > 0">
                                <div class="cart-item" *ngFor=" let appliedOffer of order.applied_offers; let i=index">
                                    <div class="cart-content">
                                        <h6>{{appliedOffer?.menu_name}}</h6>
                                        <ul *ngIf="appliedOffer?.subaddons_name">
                                            <li>{{appliedOffer?.subaddons_name}}</li>
                                        </ul>
                                        <p>{{convertNumber(appliedOffer?.total_price)}}</p>
                                    </div>
                                    <div class="cart-add-item">
                                        <ul class="offer-counter">
                                            <li><span>{{ appliedOffer?.quantity }}</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                    <div class="subtotal-price-box">
                        <div class="subtotal-box">
                            <ul class="add-promo-code">
                                <li>
                                    <span>Subtotal</span>
                                    <span>{{convertNumber(order.order_sub_total)}}</span>
                                </li>
                                <li
                                    *ngIf="restaurant?.site_setting?.charity_message != '' && restaurant?.site_setting?.charity_amount > 0">
                                    <span>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="covid-19"
                                                name="checkbox" (change)="charityAdd($event)">
                                            <label class="form-check-label" for="covid-19">
                                                {{restaurant?.site_setting?.charity_message}}
                                            </label>
                                        </div>
                                    </span>
                                    <span>
                                        {{convertNumber(restaurant.site_setting.charity_amount)}}
                                    </span>
                                </li>
                                <li *ngIf="order.rewardPoint || order.rewardPercentage > 0">
                                    <span>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="redeem-amount"
                                                name="checkbox" (change)="redeemAdd($event)">
                                            <label class="form-check-label" for="redeem-amount">
                                                Redeem ({{order.redeemPoint}} Points)
                                            </label>
                                        </div>
                                    </span>
                                    <span>
                                        (-) {{convertNumber(order.rewardPoint)}}
                                    </span>
                                </li>
                                <li *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
                                    <span>Delivery Charge</span>
                                    <span>{{convertNumber(order.delivery_charge)}}</span>
                                </li>
                                <li *ngIf="order.service_charge > 0">
                                    <span>Service charge</span>
                                    <span>{{convertNumber(order.service_charge)}}</span>
                                </li>
                                <li class="green-text" *ngIf="order.voucher_amount > 0">
                                    <span *ngIf="assignVoucher.offer_mode != 'free_delivery'">
                                        <span *ngIf="assignVoucher.offer_mode != 'percentage'">£</span>
                                        <span>{{assignVoucher.offer_value}}</span>
                                        <span *ngIf=" assignVoucher.offer_mode=='percentage'">%</span>
                                        promo code
                                    </span>
                                    <span *ngIf="assignVoucher.offer_mode == 'free_delivery'">
                                        Delivery Charge
                                    </span>
                                    <span *ngIf="assignVoucher.offer_mode != 'free_delivery'">
                                        <span (click)=" voucherRemove()">
                                            - {{convertNumber(order.voucher_amount)}}
                                            <i class="fa-regular fa-circle-xmark cursor"></i>
                                        </span>
                                    </span>
                                    <span *ngIf="assignVoucher.offer_mode == 'free_delivery'">
                                        <span (click)=" voucherRemove()">
                                            <span
                                                class="text-decoration-line-through">{{convertNumber(order.voucher_amount)}}</span>
                                            Free
                                            <i class="fa-regular fa-circle-xmark cursor"></i>
                                        </span>
                                    </span>
                                </li>
                                <li *ngIf="order.offer_amount > 0">
                                    <span>Offer {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}</span>
                                    <span>(-) {{convertNumber(order.offer_amount)}}</span>
                                </li>
                                <li *ngIf="order.wallet_amount > 0">
                                    <span>My Wallet</span>
                                    <span>(-) {{convertNumber(order.wallet_amount)}}</span>
                                </li>
                            </ul>
                            <ul class="add-promo-code" *ngIf="surcharges.length > 0">
                                <li></li>
                                <li *ngFor="let surcharge of surcharges">
                                    <span>{{surcharge.surcharge_name}}</span>
                                    <span>{{convertNumber(surcharge.surcharge_amount)}}</span>
                                </li>
                            </ul>
                            <ul class="add-promo-code">
                                <li class="total-amount">
                                    <span>Total : </span>
                                    <span>{{convertNumber(getGrandTotal())}}</span>
                                </li>
                            </ul>
                            <div class="earn-points" *ngIf="order.order_point && restaurant.reward_option == 'Yes'">
                                <p><svg class="fa-solid fa-trophy"></svg> You will earn {{order.order_point}} points</p>
                            </div>
                            <div class="driver-tip" *ngIf="order.order_type == 'delivery' && restaurant.driver_tip">
                                <h6>Driver Tip</h6>
                                <ul class="tip-list">
                                    <li>
                                        <button class="close" *ngIf="order.driver_tip == 1" (click)="driverTip(0)">
                                            <svg class="fa-solid fa-xmark"></svg>
                                        </button>
                                        <button (click)="driverTip(1)" [ngClass]="{'active' : order.driver_tip == 1}">
                                            £1
                                        </button>
                                    </li>
                                    <li>
                                        <button class="close" *ngIf="order.driver_tip == 2" (click)="driverTip(0)">
                                            <svg class="fa-solid fa-xmark"></svg>
                                        </button>
                                        <button (click)="driverTip(2)" [ngClass]="{'active' : order.driver_tip == 2}">
                                            £2
                                        </button>
                                    </li>
                                    <li>
                                        <button class="close" *ngIf="order.driver_tip == 5" (click)="driverTip(0)">
                                            <svg class="fa-solid fa-xmark"></svg>
                                        </button>
                                        <button (click)="driverTip(5)" [ngClass]="{'active' : order.driver_tip == 5}">
                                            £5
                                        </button>
                                    </li>
                                    <li>
                                        <button class="close" *ngIf="order.driver_tip == 10" (click)="driverTip(0)">
                                            <svg class="fa-solid fa-xmark"></svg>
                                        </button>
                                        <button (click)="driverTip(10)" [ngClass]="{'active' : order.driver_tip == 10}">
                                            £10
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="promo-code-box" *ngIf="!order.voucher_amount && userService.user?.id">
                            <h6>
                                Promo Code<img src="assets/images/key.svg" alt="Go-Grubz-key" loading="lazy">
                            </h6>
                            <input class="form-control" type="text" id="voucher_code" name="voucher_code"
                                [(ngModel)]="order.voucher_code" placeholder="Enter promo code">
                            <div class="text-danger error-text" *ngIf="errorVoucher">{{ errorVoucher }}</div>
                            <button class="btn" (click)="voucherCheck()">Apply</button>
                            <p class="promo-code not-valid-promo-code" *ngIf="order.voucher_amount">
                                Promo code successfully applied
                            </p>
                        </div>
                        <div class="suggested-items-box" *ngIf="menuSuggested.length > 0">
                            <h6>
                                Suggested Items <img src="assets/images/suggested-icon.svg"
                                    alt="Go-Grubz-suggested-icon" loading="lazy">
                            </h6>
                            <div class="cart-item" *ngFor="let menu of menuSuggested">
                                <div class="cart-image-item" *ngIf="menu?.image_url && restaurant?.image_type == 'Yes'">
                                    <img [src]="menu?.image_url" [alt]="menu?.menu_name"
                                        onerror="this.src='./assets/images/favourite-image-1.png';">
                                </div>
                                <div class="cart-content">
                                    <h6>{{ menu?.menu_name }}</h6>
                                    <ul>
                                        <li>{{ menu?.price_option | titlecase }}</li>
                                    </ul>
                                    <p>
                                        <span class="price" *ngIf="menu.product_percentage <= 0">
                                            <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                            <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                            {{ menu?.variants[0]?.orginal_price | currency: "GBP" }}
                                        </span>
                                        <span class="price" *ngIf="menu.product_percentage > 0">
                                            <div class="veg-nonveg" *ngIf="menu.menu_type == 'veg'"></div>
                                            <div class="veg-nonveg noneveg-bg" *ngIf="menu.menu_type == 'nonveg'"></div>
                                            <span class="text-decoration-line-through">
                                                {{ menu?.variants[0]?.orginal_price | currency: "GBP" }}</span>
                                            {{ (menu?.variants[0]?.orginal_price - (menu?.variants[0]?.orginal_price *
                                            menu.product_percentage / 100)) | currency: "GBP" }}
                                        </span>
                                        <button class="add-option cursor"
                                            [style.pointer-events]="isModelLoading || isAddonModelLoading ?'none':'auto'"
                                            (click)="addItemToCart(menuModal,menu,menu.id)">Add</button>
                                    </p>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="clearfix"></div>

<div class="add-items d-xl-none">
    <button class="btn"
        *ngIf="order.payment_method == 'Stripe' && !order.card_id && order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
        [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
        (click)="cardInfo.ngSubmit.emit($event)" nz-button>
        <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
        <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
        <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
    </button>

    <button class="btn"
        *ngIf="order.payment_method == 'Stripe' && !order.card_id && order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
        [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
        (click)="cardInfo.ngSubmit.emit($event)" nz-button>
        <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
        <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
        <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
    </button>
    <button class="btn"
        *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'delivery' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
        [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || !userService.user?.id"
        (click)="validateCheckout()" nz-button>
        <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
        <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
        <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
    </button>

    <button class="btn"
        *ngIf="(order.payment_method == 'COD' || order.payment_method == 'cod' || order.payment_method == 'Revolut Pay' || order.payment_method == 'Paypal' || order.card_id) && order.order_type == 'pickup' && restaurant.online_order == 'Yes' && order?.carts?.length != 0"
        [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes' || !userService.user?.id"
        (click)="validateCheckout()" nz-button>
        <i class="spinner-border" *ngIf="isCheckoutLoading"></i>
        <span>Place Order <svg class="fa-solid fa-arrow-right"></svg></span>
        <span>Total: {{ convertNumber(getGrandTotal()) }}</span>
    </button>
</div>

<!-- Select-Address-Popup -->
<ng-template #selectAddressModal let-modal>
    <div id="select-address-popup">
        <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
            <svg class="fa-solid fa-xmark"></svg>
        </button>

        <div class="modal-body" *ngIf="isSelectAddressLoading">
            <div class="grubz-loader">
                <div class="set-one">
                    <div class="circle"></div>
                    <div class="circle"></div>
                </div>
                <div class="set-two">
                    <div class="circle"></div>
                    <div class="circle"></div>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isSelectAddressLoading">
            <div class="row">
                <div class="col-7 pr-0" *ngIf="order.order_type == 'delivery'">
                    <div class="main-heading pt-1 mb-0">
                        <h6 class="text-start mb-0">Select Address</h6>
                    </div>
                </div>
                <div class="col-5 ps-0" *ngIf="order.order_type == 'delivery'">
                    <div class="add-btn text-end">
                        <a (click)="openAddAddress()" class="btn modal-black-btn">Add Address</a>
                    </div>
                </div>
                <p class="col-12" *ngIf="addressBooks?.length <= 0 && order.order_type == 'delivery'"
                    class="text-danger address-text-error text-center">
                    Please add address first!</p>

                <div class="col-sm-12">
                    <div class="address-list">
                        <ul *ngIf="order.order_type == 'delivery'">
                            <li *ngFor=" let addressBook of addressBooks; let i=index">
                                <div class="form-check">
                                    <input [(ngModel)]="order.address_id" type="radio" class="form-check-input"
                                        (ngModelChange)="showDeliveryCharge(addressBook)" type="radio"
                                        class="form-check-input" name="checkout_address"
                                        [id]="'inlineRadio'+addressBook.id" [value]="addressBook.id">
                                    <label class="form-check-label" for="inlineRadio{{addressBook.id}}">
                                        {{addressBook.title.includes(addressBook.flat_no)?"":
                                        addressBook.flat_no+','}}{{addressBook.title}}
                                        ,{{addressBook.address}}</label>
                                    <div class="edit-delete">
                                        <ul>
                                            <li>
                                                <button class="edit-btn" (click)="openAddAddress(addressBook)">
                                                    <svg class="fa-regular fa-pen-to-square"></svg>
                                                </button>
                                            </li>
                                            <li>
                                                <button class="delete-btn" (click)="deleteAddress(addressBook)">
                                                    <svg class="fa-regular fa-trash-can"></svg>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </li>
                            <li *ngFor=" let notDeliveryaddressBook of notDeliveryaddressBooks; let i=index">
                                <div class="form-check not-deliverable">
                                    <input type="radio" class="form-check-input" type="radio" class="form-check-input"
                                        name="checkout_address">
                                    <label class="form-check-label">
                                        {{notDeliveryaddressBook.title.includes(notDeliveryaddressBook.flat_no)?"":
                                        notDeliveryaddressBook.flat_no+','}}{{notDeliveryaddressBook.title}}
                                        ,{{notDeliveryaddressBook.address}} <span class="not-deliverable-text">(Not
                                            Deliverable)</span>
                                    </label>
                                </div>
                            </li>
                        </ul>
                        <h6 class="drop-off-options-title" *ngIf="checkoutLists.length > 0">Choose your preferences</h6>
                        <ul *ngIf="checkoutLists.length > 0">
                            <li>
                                <div class="form-check" *ngFor="let checkList of checkoutLists">
                                    <input type="checkbox" class="form-check-input" [(ngModel)]="checkList.selected"
                                        id="checkoutlist_{{ checkList.id }}" name="checkbox"
                                        [value]="checkList.message">
                                    <label class="form-check-label" for="checkoutlist_{{ checkList.id }}">{{
                                        checkList.message }}</label>
                                </div>
                            </li>
                            <!-- <li>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" [(ngModel)]="order.dont_bell"
                                        id="leave-it-door" name="radio" value="1">
                                    <label class="form-check-label" for="leave-it-door">Leave it at my door</label>
                                </div>
                            </li> -->
                        </ul>
                        <div class="form-group">
                            <textarea class="form-control" nz-input [(ngModel)]="order.order_description"
                                name="order_description" id="order_description" placeholder="Enter message here.....">
                            </textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn modal-black-btn" (click)="closeSelectAddress()" *ngIf="order.order_type == 'delivery'"
                [disabled]="addressBooks?.length <= 0">Save</button>
            <button class="btn modal-black-btn" (click)="closeSelectAddress()"
                *ngIf="order.order_type == 'pickup'">Save</button>
        </div>
    </div>
</ng-template>

<!-- Add-New-Address-Popup -->
<ng-template #addAddressModal let-modal>
    <div id="add-new-address-popup">

        <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
            <svg class="fa-solid fa-xmark"></svg>
        </button>

        <div class="modal-body">
            <h5 class="login-title">Add New Address</h5>
            <form nz-form #addressForm="ngForm" (ngSubmit)="onAddressSubmit(addressForm)" nzLayout="vertical">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter title!">
                                    <nz-input-group>
                                        <input type="text" class="form-control" nz-input name="title" id="title"
                                            [(ngModel)]="addressBookAdd.title" required placeholder="Address Title">
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group postcode-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter postcode!">
                                    <nz-input-group>
                                        <input type="text" class="form-control" nz-input name="zipcode" id="zipcode"
                                            required [(ngModel)]="addressBookAdd.zipcode" placeholder="Postcode">
                                        <div class="btn modal-black-btn" [disabled]="isPostcodeLoading"
                                            (click)="findzipcode(addressBookAdd.zipcode)">
                                            <i class="spinner-border" *ngIf="isPostcodeLoading"></i>
                                            Search
                                        </div>
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                            <nz-form-item *ngIf="errorPostcode">
                                <span class="error-text">{{ errorPostcode }}</span>
                            </nz-form-item>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter house/door number!">
                                    <nz-input-group>
                                        <input type="text" class="form-control" nz-input name="flat_no" id="flat_no"
                                            [(ngModel)]="addressBookAdd.flat_no" placeholder="House Number/Door Number"
                                            required="">
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <nz-form-item>
                                <nz-form-control nzHasFeedback nzErrorTip="Please enter address!">
                                    <nz-input-group>
                                        <input type="text" class="form-control" nz-input name="address" id="address"
                                            [(ngModel)]="addressBookAdd.address" placeholder="Address" required>
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                        </div>
                    </div>

                    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
                        [(ngModel)]="addressBookAdd.latitude" />
                    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
                        [(ngModel)]="addressBookAdd.longitude" />
                </div>

                <nz-form-item *ngIf="errorAddAddress">
                    <span class="text-danger">{{ errorAddAddress }}</span>
                </nz-form-item>

                <button nz-button class="btn modal-black-btn cursor" [disabled]="isAddAddressLoading">
                    <i class="spinner-border" *ngIf="isAddAddressLoading"></i>
                    Add Address
                </button>
            </form>
        </div>
    </div>
</ng-template>

<!-- Schedule-Popup -->
<ng-template #timeSlotModal let-modal>
    <div id="schedule-later-popup">
        <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
            <svg class="fa-solid fa-xmark"></svg>
        </button>

        <div class="modal-body" *ngIf="isTimeSlotLoading">
            <div class="grubz-loader">
                <div class="set-one">
                    <div class="circle"></div>
                    <div class="circle"></div>
                </div>
                <div class="set-two">
                    <div class="circle"></div>
                    <div class="circle"></div>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isTimeSlotLoading">
            <h6>Select a {{ order.order_type | titlecase }} Date</h6>
            <ul class="nav nav-tabs">
                <li class="nav-item" *ngFor="let selectDate of selectDates;let i = index">
                    <a class="nav-link" (click)="fetchSlot(selectDate.fullDate)"
                        [ngClass]="{'active' : order.delivery_date == selectDate.fullDate}" *ngIf="i == 0">Today<span>{{
                            selectDate.date }}</span></a>
                    <!-- <a class="nav-link" (click)="fetchSlot(selectDate.fullDate)"
                        [ngClass]="{'active' : order.delivery_date == selectDate.fullDate}" *ngIf="i != 0">{{
                        selectDate.day }}<span>{{ selectDate.date }}</span></a> -->
                </li>
            </ul>
            <div class="d-flex w-100 justify-content-beetwen pb-lg-1 mb-2">
                <h6 class="w-100 mb-0">Desired {{ order.order_type | titlecase }} Time</h6>
                <!-- <span class="pt-1 text-nowrap">All times in EST</span> -->
            </div>
            <div class="pickup-time-list">
                <ul *ngIf="timeSlots.length > 0">
                    <li *ngFor="let time of timeSlots;">
                        <a (click)="selectTime(time)" [ngClass]="{'active' : order.delivery_time == time}">{{time}}</a>
                    </li>
                </ul>
                <ul *ngIf="timeSlots.length <= 0">
                    <li><a class="text-danger active">Close</a></li>
                </ul>
            </div>
        </div>
    </div>
</ng-template>

<!-- Edit-Phone-Number-Popup -->
<ng-template #profileModal let-modal>
    <div id="edit-number-popup">
        <button type="button" class="btn-close" data-bs-dismiss="modal"
            (click)="modal.dismiss('Cross click');counter=20;mySubscription.unsubscribe()">
            <svg class="fa-solid fa-xmark"></svg>
        </button>
        <form nz-form #userForm="ngForm" (ngSubmit)="updateUser(userForm)">
            <div class="modal-body">
                <img class="verification-icon" src="assets/images/verification.png" alt="GoGrubz-verification">
                <h6 class="mb-4">Edit Phone Number</h6>
                <div class="form-group">
                    <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="phoneErrorTpl">
                            <nz-input-group>
                                <input type="text" inputmode="numeric" class="form-control" nz-input
                                    (keydown.space)="onSpaceKeyDown($event)" (keypress)="validateMobile($event)"
                                    id="phone_number" name="phone_number" [(ngModel)]="loginuser.phone_number" required
                                    placeholder="Enter phone number">
                            </nz-input-group>
                            <ng-template #phoneErrorTpl let-control>
                                <ng-container *ngIf="control.hasError('required')">
                                    Please enter mobile number!
                                </ng-container>
                                <ng-container *ngIf="control.hasError('minlength')">
                                    statement mobile number at least 10 digit!
                                </ng-container>
                                <ng-container *ngIf="control.hasError('maxlength')">
                                    statement mobile number maximum 11 digits long!
                                </ng-container>
                            </ng-template>
                        </nz-form-control>
                    </nz-form-item>
                </div>
            </div>
            <div class="modal-footer">

                <nz-form-item *ngIf="ModelProfileerror">
                    <span class="text-danger">{{ ModelProfileerror }}</span>
                </nz-form-item>

                <button class="btn modal-black-btn" nz-button>
                    <i class="spinner-border" *ngIf="isModelProfileLoading"></i>
                    Save
                </button>
            </div>
        </form>
    </div>
</ng-template>

<!-- Multiple-Price-Offer-Modal -->
<ng-template #multiplePriceOfferModal let-modal>
    <div id="free-products-popup">
        <div class="modal-body">
            <h6>You qualify for free below products, please select {{eligibleQty}}</h6>
            <div class="free-products-box">
                <ul *ngFor="let eligibleOffer of order.eligible_offers; let i = index" class="free-products-list">
                    <li>
                        <div class="checkbox">
                            <input class="form-check-input" type="checkbox" id="offer_{{i}}"
                                (ngModelChange)="selectCheckBox(eligibleOffer,$event,i)" class="offer_{{i}}"
                                [(ngModel)]="eligibleOffer.selected" name="offer_radio_{{i}}">
                            <label class="form-check-label" for="offer_{{i}}">
                                {{ eligibleOffer?.menu_name}}
                            </label>
                        </div>
                        <div class="add-to-product">
                            <ul>
                                <li><a (click)="updateToOffer(eligibleOffer,'update',i)">
                                        <!-- <svg class="fa-solid fa-trash-can"></svg> -->
                                        <svg class="fa-solid fa-minus"></svg>
                                    </a>
                                </li>
                                <li><span>{{eligibleOffer.quantity}}</span></li>
                                <li><a (click)="updateToOffer(eligibleOffer,'add',i)"><svg
                                            class="fa-solid fa-plus"></svg></a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="modal-footer">
            <nz-form-item *ngIf="OfferModelerror">
                <span class="error-text">{{ OfferModelerror }}</span>
            </nz-form-item>
            <button class="btn modal-black-btn" nz-button [disabled]="isOfferModelLoading || !isEligible"
                (click)="validateOffer()">
                <i class="spinner-border" *ngIf="isOfferModelLoading"></i>
                Add Product
            </button>
        </div>
    </div>
</ng-template>

<!-- Phone-Number-Verification-Popup -->
<ng-template #otpModal let-modal>
    <div id="number-verification-popup">
        <button type="button" class="btn-close" data-bs-dismiss="modal"
            (click)="modal.dismiss('Cross click');counter=20;mySubscription.unsubscribe()">
            <svg class="fa-solid fa-xmark"></svg>
        </button>
        <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
            <div class="modal-body">
                <img class="verification-icon" src="assets/images/verification.png" alt="GoGrubz-verification">
                <h6>Phone Number Verification</h6>
                <p>Before we place your order, we need to verify your phone<br> number for security purposes</p>
                <div class="form-group">
                    <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                            <nz-input-group>
                                <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                                    (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                                    (keypress)="validateMobile($event)" [(ngModel)]="user.otp" required
                                    placeholder="Enter 6-digit code">
                            </nz-input-group>
                            <ng-template #phoneVeriErrorTpl let-control>
                                <ng-container *ngIf="control.hasError('required')">
                                    Please enter verification code!
                                </ng-container>
                                <ng-container *ngIf="control.hasError('minlength')">
                                    statement verification code at least 6 digit!
                                </ng-container>
                                <ng-container *ngIf="control.hasError('maxlength')">
                                    statement verification code maximum 6 digits long!
                                </ng-container>
                            </ng-template>
                        </nz-form-control>
                    </nz-form-item>
                </div>
                <span>We sent a code to {{ user.phone_number }}</span>
                <ul class="more-option">
                    <li><a class="d-flex text-nowrap cursor" (click)="resendOtp()"
                            [style.pointer-events]="counter >= 1 ?'none':'auto'"
                            [ngClass]="{'opacity-resend' : counter > 0}"> Resend Code
                            <span class="ps-2" *ngIf="counter > 1"></span>({{ counter }})
                        </a>
                    </li>
                    <li><svg class="fa-solid fa-circle"></svg></li>
                    <li><a class="cursor" (click)="openPhoneEdit()"> Update Number</a></li>
                </ul>
            </div>
            <div class="modal-footer">
                <nz-form-item *ngIf="Modelotperror">
                    <span class="text-danger">{{ Modelotperror }}</span>
                </nz-form-item>

                <button class="btn modal-black-btn" nz-button [disabled]="isModelOtpLoading">
                    <i class="spinner-border" *ngIf="isModelOtpLoading"></i>
                    Verify
                </button>
            </div>
        </form>
    </div>
</ng-template>

<!-- Place-Order -->
<ng-template #placeModal let-modal>
    <div class="modal-body">
        <ng-lottie height="340px" [options]="optionPlace" loop autoplay containerClass="moving-box another-class">
        </ng-lottie>
    </div>
</ng-template>

<!-- Item Refresh Modal -->
<ng-template #itemModal let-modal>
    <div id="items-already-in-cart-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Items already in cart</h5>
        </div>
        <div class="modal-body">
            <p>Your cart contains items from other restaurant. Would you like to reset your cart for adding items from
                this restaurant?</p>
            <div class="d-flex justify-content-center pt-2">
                <button class="btn modal-black-btn" (click)="modal.dismiss('Cross click')">Close</button>
                <button class="btn modal-black-btn" (click)="cartempty()">Yes,Start Fresh</button>
            </div>
        </div>
    </div>
</ng-template>

<!-- Add-New-Card-Popup -->
<!-- <ng-template #addNewCardPopup let-modal>
    <div id="add-new-card-popup">
        <div class="modal-body">
            <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title">Add New Card</h5>
            <form nz-form #cardInfo="ngForm" (ngSubmit)="handleForm($event)" nzLayout="vertical">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <div class="input-group">
                                <span id="card-number" class="form-control rounded"></span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <span id="card-exp" nz-input class="form-control rounded"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <span id="card-cvc" class="form-control rounded"> </span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <span id="postalCode" nz-input class="form-control rounded"></span>
                        </div>
                    </div>

                    <nz-form-item *ngIf="Modelerror">
                        <span class="text-danger">{{ Modelerror }}</span>
                    </nz-form-item>

                    <div class="col-md-12 text-center">
                        <button class="btn modal-black-btn" nz-button>
                            <i class="spinner-border" *ngIf="isModelLoading"></i>
                            Save Card
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</ng-template> -->

<!-- product-multiple-options-popup -->
<ng-template #menuModal let-modal>
    <div *ngIf="isAddonModelLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="modal-body product-body" *ngIf="!isModelLoading && !subAddonVisible && !isAddonModelLoading">
        <button type="button" class="btn-close product-close" data-bs-dismiss="modal" (click)="closeAll();">
            <svg class="fa-solid fa-xmark"></svg>
        </button>
        <div class="popup-product-box">
            <h6>{{ selectedMenu?.menu_name }}</h6>
            <p>{{ selectedMenu?.menu_description }}</p>
            <div class="product-image" *ngIf="selectedMenu?.image_url">
                <img [src]="selectedMenu?.image_url" [alt]="selectedMenu?.menu_name">
            </div>
        </div>
        <div class="choose-options">
            <h6>Choose Option</h6>
            <div class="required-select">
                <div class="required" *ngIf="selectedVariant?.required_error">
                    <svg class="fa-solid fa-circle-check"></svg>Required
                </div>
                <svg class="fa-solid fa-circle" *ngIf="selectedVariant?.required_error"></svg>
                <span *ngIf="selectedVariant?.required_error">Select 1</span>
            </div>
            <ul class="choose-list">
                <li *ngFor="let variant of selectedMenu?.variants; let i = index ; trackBy: trackByFn">
                    <div class="form-check">
                        <input type="radio" class="form-check-input radio-button" id="menu_{{ variant.id }}"
                            name="menu_{{ selectedMenu.id }}" [value]="variant.id" [(ngModel)]="addVariant.id"
                            (ngModelChange)="fetchVartiantAddonItem(addonModal,selectedMenu.id,variant.id)"
                            [attr.disabled]="isAddonModelLoading ? '' : null">
                        <label for="menu_{{ variant.id }}">
                            <div class="form-check-label">
                                <div class="d-flex">
                                    <div class="burger-name"> {{ variant?.sub_name }}</div>
                                    <button class="edit-addon-btn"
                                        *ngIf="selectedMenu?.menu_addon == 'Yes' && selectedSubAddonstring && variant?.id == selectedVariant?.id"
                                        (click)="editAddon()">
                                        Edit
                                    </button>
                                </div>
                                <ul class="extra-addon-list"
                                    *ngIf="selectedSubAddonstring && variant?.id == selectedVariant?.id">
                                    <li>
                                        {{ selectedSubAddonstring }}
                                        <svg class="fa-solid fa-circle"></svg>
                                    </li>
                                </ul>
                                <span class="price" *ngIf="selectedMenu.product_percentage <= 0">
                                    {{ variant.orginal_price | currency: "GBP" }}
                                </span>
                                <span class="price" *ngIf="selectedMenu.product_percentage > 0">
                                    <span class="px-1 text-decoration-line-through">
                                        {{ variant.orginal_price | currency: "GBP" }}
                                    </span>
                                    {{ (variant.orginal_price - (variant.orginal_price * selectedMenu.product_percentage
                                    /
                                    100)) | currency: "GBP" }}
                                </span>
                            </div>
                            <div class="right-arrow" *ngIf="selectedMenu?.menu_addon == 'Yes'"><svg
                                    class="fa-solid fa-chevron-right"></svg></div>
                        </label>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="modal-footer product-footer" *ngIf="!subAddonVisible && !isAddonModelLoading">
        <div class="add-to-product" *ngIf="!isModelLoading">
            <ul>
                <li><a (click)="updateSelected('remove')">
                        <!-- <svg class="fa-solid fa-trash-can"></svg> -->
                        <svg class="fa-solid fa-minus"></svg>
                    </a></li>
                <li><span>{{ selectedQnt }}</span></li>
                <li><a (click)="updateSelected('add')"><svg class="fa-solid fa-plus"></svg></a></li>
            </ul>
        </div>
        <button type="button" class="btn modal-black-btn w-100" (click)="validateMultiple(addonModal)">
            <i class="spinner-border" *ngIf="isModelLoading"></i>
            Add to cart
            <span *ngIf="selectedSubAddonPrice > 0"> <span class="price-circle"> <svg
                        class="fa-solid fa-circle"></svg></span> {{ selectedSubAddonPrice * selectedQnt | currency:
                "GBP"
                }}
            </span>
        </button>
    </div>

    <div id="addonpopup" *ngIf="subAddonVisible && !isAddonModelLoading">
        <div class="modal-header border-0">
            <button type="button" class="btn-close" data-bs-dismiss="modal"
                (click)="selectedAddonPrice = 0;selectedAddonDummy = [];subAddonVisible = false;">
                <svg class="fa-solid fa-chevron-left"></svg>
            </button>
            <div>
                <h6>{{ selectedMenu?.menu_name }}</h6>
                <span> {{ selectedVariant?.sub_name }}</span>
                <span class="subaddon-circle"> <svg class="fa-solid fa-circle"></svg></span>
                <span class="price" *ngIf="selectedMenu.product_percentage <= 0">
                    {{ selectedVariant?.orginal_price | currency: "GBP" }}
                </span>
                <span class="price" *ngIf="selectedMenu.product_percentage > 0">
                    <span class="px-1 text-decoration-line-through">
                        {{ selectedVariant?.orginal_price | currency: "GBP" }}
                    </span>
                    {{ (selectedVariant?.orginal_price - (selectedVariant?.orginal_price *
                    selectedMenu.product_percentage
                    / 100)) | currency: "GBP" }}
                </span>
            </div>
        </div>

        <div class="modal-body product-body" *ngIf="isAddonModelLoading">
            <div class="row text-center align-middle justify-content-center" *ngIf="isAddonModelLoading">
                <div class="col-md-12 ms-2 spinner-border text-primary text-center">
                    <span class="visually-hidden text-center">Loading...</span>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isAddonModelLoading">
            <div class="addon-list" *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
                <h6>
                    {{ mainAddon?.mainaddons_name }}
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count == 0">(Optional) Select up to {{
                        mainAddon.mainaddons_count }} add on(s)</span>
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0">(Required) Choose upto {{
                        mainAddon.mainaddons_count }} - Min. {{ mainAddon.mainaddons_mini_count}}</span>
                </h6>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count== 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.min_error">
                    (Required) • Select at least {{ mainAddon.mainaddons_mini_count }} add on(s).
                </span>
                <span class="text-danger"
                    *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.mainaddons_count!= 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <ul>
                    <li *ngFor="let subAddon of mainAddon.sub_addons">
                        <!-- <div class="form-check" *ngIf="mainAddon.mainaddons_count <= 1">
                            <input type="radio" class="form-check-input radio-button subaddon_{{subAddon?.id}}"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}" [value]="subAddon?.id"
                                [(ngModel)]="mainAddon.selectedSubAddonId"
                                (change)="addprice($event,subAddon?.subaddons_price,mainAddon?.id)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div> -->
                        <!-- *ngIf="mainAddon.mainaddons_count > 1" -->
                        <div class="form-check">
                            <input class="form-check-input subaddon_{{subAddon?.id}}" type="checkbox"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}"
                                [(ngModel)]="subAddon.selected"
                                (change)="addCheckboxPrice($event.target,subAddon?.subaddons_price)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div>
                    </li>
                </ul>
            </div>
            <button class="btn modal-black-btn" type="button" (click)="validate(addonModal)"
                *ngIf="!isAddonModelLoading">
                Confirm <span *ngIf="selectedAddonPrice > 0"> <span class="price-circle"> <svg
                            class="fa-solid fa-circle"></svg></span>
                    {{ selectedAddonPrice | currency: "GBP" }} </span>
            </button>
        </div>
    </div>
</ng-template>

<!-- Addon-Modal -->
<ng-template #addonModal let-modal>
    <div id="addonpopup">
        <div class="modal-header border-0" *ngIf="!isAddonModelLoading">
            <button type="button" class="btn-close" data-bs-dismiss="modal"
                (click)="modal.dismiss('Cross click');selectedAddonPrice = 0;selectedAddonDummy = [];">
                <svg class="fa-solid fa-chevron-left"></svg>
            </button>
            <div>
                <h6>{{ selectedMenu?.menu_name }}</h6>
                <span> {{ selectedVariant?.sub_name }}</span>
            </div>
        </div>

        <div class="modal-body product-body" *ngIf="isAddonModelLoading">
            <div class="row text-center align-middle justify-content-center" *ngIf="isAddonModelLoading">
                <div class="col-md-12 ms-2 spinner-border text-primary text-center">
                    <span class="visually-hidden text-center">Loading...</span>
                </div>
            </div>
        </div>

        <div class="modal-body" *ngIf="!isAddonModelLoading">
            <div class="addon-list" *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
                <h6>
                    {{ mainAddon?.mainaddons_name }}
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count == 0">(Optional) Select up to {{
                        mainAddon.mainaddons_count }} add on(s)</span>
                    <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0">(Required) Choose upto {{
                        mainAddon.mainaddons_count }} - Min. {{ mainAddon.mainaddons_mini_count}}</span>
                </h6>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count== 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.min_error">
                    (Required) • Select at least {{ mainAddon.mainaddons_mini_count }} add on(s).
                </span>
                <span class="text-danger"
                    *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.mainaddons_count!= 0 && mainAddon.max_error">
                    You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
                </span>
                <ul>
                    <li *ngFor="let subAddon of mainAddon.sub_addons">
                        <!-- <div class="form-check" *ngIf="mainAddon.mainaddons_count <= 1">
                            <input type="radio" class="form-check-input radio-button subaddon_{{subAddon?.id}}"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}" [value]="subAddon?.id"
                                [(ngModel)]="mainAddon.selectedSubAddonId"
                                (change)="addprice($event,subAddon?.subaddons_price,mainAddon?.id)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div> -->
                        <!-- *ngIf="mainAddon.mainaddons_count > 1" -->
                        <div class="form-check">
                            <input class="form-check-input subaddon_{{subAddon?.id}}" type="checkbox"
                                id="subaddon_{{subAddon?.id}}" name="subaddon_radio_{{i}}"
                                [(ngModel)]="subAddon.selected"
                                (change)="addCheckboxPrice($event.target,subAddon?.subaddons_price)">
                            <label class="form-check-label" for="subaddon_{{subAddon?.id}}">
                                {{ subAddon?.subaddons_name }} ( {{convertNumber(subAddon?.subaddons_price)}} )
                            </label>
                        </div>
                    </li>
                </ul>
            </div>
            <button class="btn modal-black-btn" type="button" (click)="validate(addonModal)"
                *ngIf="!isAddonModelLoading">
                Add <span *ngIf="selectedAddonPrice > 0">
                    <span class="price-circle"> <svg class="fa-solid fa-circle"></svg></span>
                    {{ selectedAddonPrice | currency: "GBP" }} </span>
            </button>
        </div>
    </div>
</ng-template>

<!-- Item Not Available Modal -->
<ng-template #itemNotAvailableModal let-modal>
    <div id="allergy-popup">
        <div class="modal-header justify-content-center border-0">
            <button type="button" class="btn-close" (click)="handleItemNotAvailable('no')">
                <svg class="fa-solid fa-xmark"></svg>
            </button>
            <h5 class="login-title pt-lg-1 mt-2 m-0">Not Available</h5>
        </div>
        <div class="modal-body login-body">
            <p class="text-center"*ngIf="hasOrderTypeMismatch && !hasDayMismatch">Some items in your cart are not available for the selected order type.</p>
            <p class="text-center"*ngIf="hasDayMismatch && !hasOrderTypeMismatch">Some items are not available on the selected day.</p>
            <p class="text-center"*ngIf="hasOrderTypeMismatch && hasDayMismatch">Some items are not available for the selected order type or delivery day.</p>
            <div class="d-flex justify-content-center pt-2">
                <button class="btn modal-black-btn" (click)="handleItemNotAvailable('no')">No</button>
                <button class="btn modal-black-btn" (click)="handleItemNotAvailable('yes')">Yes</button>
            </div>
        </div>
    </div>
</ng-template>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>