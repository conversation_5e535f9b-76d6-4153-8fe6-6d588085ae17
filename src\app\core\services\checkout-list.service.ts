import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { CheckoutList } from '../models/checkout-list';
import { environment } from '../../../environments/environment';
import { ErrorHandler } from '../../shared/error-handler';

@Injectable({
  providedIn: 'root',
})
export class CheckoutListService {
  private url = environment.apiBaseUrl + 'checkout-list/';

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.all_type) params = params.set('all_type', options.all_type);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);
    params = params.set('status', 1);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<CheckoutList> {
    return this.http.get<CheckoutList>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(checkout_list: CheckoutList): Observable<any> {
    return this.http.post<CheckoutList>(this.url, CheckoutList.toFormData(checkout_list))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(checkout_list: CheckoutList): Observable<any> {
    return this.http.post<CheckoutList>(this.url + checkout_list.id, CheckoutList.toFormData(checkout_list))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<CheckoutList>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }
}
