<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="reservations-tab" *ngIf="!isLoading">
  <ul>
    <li [ngClass]="{'active' : status == 'upcoming'}">
      <button class="cursor" (click)="statusChange('upcoming')">Current
        Reservations</button>
    </li>
    <li [ngClass]="{'active' : status == 'previous'}">
      <button class="cursor" (click)="statusChange('previous')">Reservation
        History</button>
    </li>
  </ul>
</div>

<div class="dont-have-order text-center" *ngIf="status == 'upcoming' && upcomingBookings.length <= 0 && !isLoading">
  <p>You don't have any previous booking.</p>
  <span>Make your first booking by clicking the button below.</span>
  <br>
  <button class="btn" (click)="redirectOnRest()">Booking Now</button>
</div>

<div class="dont-have-order text-center" *ngIf="status == 'previous' && previousBookings.length <= 0 && !isLoading">
  <p>You don't have any previous booking.</p>
  <span>Make your first booking by clicking the button below.</span>
  <br>
  <button class="btn" (click)="redirectOnRest()">Booking Now</button>
</div>

<div *ngIf="upcomingBookings.length > 0 && !isLoading && status == 'upcoming'">
  <div class="order-history-main-box" *ngFor="let booking of upcomingBookings;let i = index;">
    <p class="requested-date">Requested - {{convertToDate(booking.created)}}</p>
    <div class="order-history-box">
      <div class="order-image">
        <div class="order-logo">
          <img [src]="booking?.restaurant?.image_url" [alt]="booking?.restaurant?.restaurant_name"
            onerror="this.src='./assets/favicon.png';">
        </div>
        <img class="order-main-image" [src]="booking?.restaurant?.promotions[0]?.image_url"
          [alt]="booking?.restaurant?.restaurant_name" onerror="this.src='./assets/images/product-main-image.png';">
      </div>

      <div class="order-details">
        <div class="order-title">
          <h6>{{ booking?.restaurant?.restaurant_name }}</h6>
        </div>
        <div class="booking-details">
          <p>Reservation Details:</p>
          <ul class="order-date-time">
            <li>{{booking.booking_date}}</li>
            <li>{{booking.booking_time}}</li>
            <li>{{booking.guest_count}} Guests</li>
          </ul>
          <ul class="order-date-time">
            <li *ngIf="booking.booking_amount">£ {{booking.booking_amount}}</li>
            <li>{{(booking.booking_amount && booking.status != 'Processing') ? 'Paid' : 'Unpaid'}}</li>
          </ul>
          <div class="reservation-id">
            <p><strong>Reservation ID</strong> : {{ booking.booking_id }}</p>
          </div>
          <p class="pending-text" *ngIf="booking.status == 'Pending'">Pending Confirmation</p>
          <p class="success-text" *ngIf="booking.status == 'Approved'">Confirmed By Restaurant</p>
        </div>
      </div>

      <div class="view-store">
        <ul>
          <li><button class="btn" (click)="cancelBooking(reservationModal,booking)"
              *ngIf="booking.status == 'Pending'">Cancel</button></li>
          <li><button class="btn" (click)="bookingStore(booking)" *ngIf="booking.status == 'Approved'">Re-Book</button>
          </li>
          <li><button class="btn" (click)="viewStore(booking)">View Store</button></li>
          <li><button class="btn" (click)="viewAddress(booking)">View Address</button></li>
        </ul>
      </div>
    </div>

    <div #targetUpcoming *ngIf="i+1 == upcomingBookings.length - 1"></div>
  </div>

  <div class="grubz-loader" *ngIf="isScrollLoading">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div *ngIf="previousBookings.length > 0 && !isLoading && status == 'previous'">
  <div class="order-history-main-box" *ngFor="let booking of previousBookings;let i = index;">
    <p class="requested-date">Requested - {{convertToDate(booking.created)}}</p>
    <div class="order-history-box">
      <div class="order-image">
        <div class="order-logo">
          <img [src]="booking?.restaurant?.image_url" [alt]="booking?.restaurant?.restaurant_name"
            onerror="this.src='./assets/favicon.png';">
        </div>
        <img class="order-main-image" [src]="booking?.restaurant?.promotions[0]?.image_url"
          [alt]="booking?.restaurant?.restaurant_name" onerror="this.src='./assets/images/product-main-image.png';">
      </div>

      <div class="order-details">
        <div class="order-title">
          <h6>{{ booking?.restaurant?.restaurant_name }}</h6>
        </div>
        <div class="booking-details">
          <p>Reservation Details:</p>
          <ul class="order-date-time">
            <li>{{booking.booking_date}}</li>
            <li>{{booking.booking_time}}</li>
            <li>{{booking.guest_count}} Guests</li>
          </ul>
          <ul class="order-date-time">
            <li *ngIf="booking.booking_amount">£ {{booking.booking_amount}}</li>
            <li>{{(booking.booking_amount && booking.status != 'Processing') ? 'Paid' : 'Unpaid'}}</li>
          </ul>
          <div class="reservation-id">
            <p><strong>Reservation ID</strong> : {{ booking.booking_id }}</p>
          </div>
          <p class="pending-text" *ngIf="booking.status == 'Pending'">Pending Confirmation</p>
          <p class="success-text" *ngIf="booking.status == 'Approved'">Completed</p>
          <p class="pending-text" *ngIf="booking.status == 'Cancel'">Cancelled</p>
        </div>
      </div>

      <div class="view-store">
        <ul>
          <li><button class="btn" (click)="bookingStore(booking)">Re-Book</button></li>
          <li><button class="btn" (click)="viewStore(booking)">View Store</button></li>
          <li><button class="btn" (click)="viewAddress(booking)">View Address</button></li>
        </ul>
      </div>
    </div>

    <div #targetPrevious *ngIf="i+1 == previousBookings.length - 1"> </div>
  </div>

  <div class="grubz-loader" *ngIf="isScrollLoading">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>

</div>


<ng-template #reservationModal let-modal>
  <div id="add-review-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <div class="modal-body">
      <h5 class="login-title">Cancel Reservation</h5>
      <div class="row">
        <form nz-form #reservationForm="ngForm" (ngSubmit)="validateBooking(reservationForm)">

          <div class="col-md-12">
            <div class="form-group">
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter cancel reason!">
                  <nz-input-group>
                    <textarea nz-input [(ngModel)]="booking.cancel_reason" name="cancel_reason" id="cancel_reason"
                      class="form-control reviewMessage" placeholder="Enter your cancel reason here...."
                      required=""></textarea>
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>

          </div>


          <nz-form-item *ngIf="Modelerror">
            <span class="text-danger fw-bold">{{ Modelerror }}</span>
          </nz-form-item>

          <div class="col-md-12">
            <button class="btn modal-black-btn" nz-button>
              <i class="spinner-border" *ngIf="isModelLoading"></i>
              Submit
            </button>
          </div>

        </form>
      </div>
    </div>
  </div>
</ng-template>