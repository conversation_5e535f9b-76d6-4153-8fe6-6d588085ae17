$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}


#edit-number-popup .modal-body h6,
#update-number-popup .modal-body h6,
#number-verification-popup .modal-body h6 {
  font-size: 26px;
  font-weight: 800;
  font-family: 'Visby CF';
  text-align: center;
}

#number-verification-popup .modal-body p {
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  margin-bottom: 35px;
}

#number-verification-popup .modal-body .form-group,
#edit-number-popup .modal-body .form-group,
#update-number-popup .modal-body .form-group {
  margin-bottom: 15px !important;
}

#edit-number-popup .modal-body .form-group input.form-control,
#number-verification-popup .modal-body .form-group input.form-control,
#update-number-popup .modal-body .form-group input.form-control {
  padding: 4px 20px 7px;
  height: 50px;
  background-color: #f4f3f3!important;
}

#number-verification-popup .modal-body span {
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  display: inline-block;
}

#number-verification-popup .more-option {
  display: flex;
  justify-content: center;
  margin: 0;
  padding-top: 15px;
}

#number-verification-popup .more-option li {
  padding: 0 5px;
}

#number-verification-popup .more-option li svg {
  color: #d7d7d7;
  font-size: 4px;
  position: relative;
  top: -4px;
}

#number-verification-popup .more-option li a {
  font-size: 16px;
  color: #202020;
  text-decoration: underline;
  font-weight: 600;
}

#edit-number-popup .modal-footer,
#update-number-popup .modal-footer,
#number-verification-popup .modal-footer {
  padding: 0 30px 30px 30px;
}

#edit-number-popup .modal-footer button.btn,
#update-number-popup .modal-footer button.btn,
#number-verification-popup .modal-footer button.btn {
  margin: 0;
  width: 100%;
}

.add-restaurant-banner-bg {
  position: relative;
  overflow: hidden;
  padding: 190px 135px 110px 135px;
  background-color: #FDF6F2;
  border-radius: 50px;
}

.add-restaurant-banner-bg .shape-one {
  position: absolute;
  left: -12px;
  bottom: -12px;
}
.add-restaurant-banner-bg .shape-two {
  position: absolute;
  top: 265px;
  right: 0;
}

.add-restaurant-banner-content {  
  position: relative;
  z-index: 1;
}

.add-restaurant-banner-content h2 {
  margin-bottom: 60px;
}

.add-restaurant-banner-content h4 {
  font-size: 52px;
  line-height: 68px;
}

.add-restaurant-banner-content h4 span {
  color: #1F76E0;
}

.add-restaurant-form-card {
  padding: 35px 35px 10px 35px;
  background-color: #F3EBE7;
  border-radius: 38px;
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 30px;
}

.form-group input.form-control {
  font-size: 20px;
  color: #4D4D4D;
  font-family: 'Visby CF';
  font-weight: 700;
  padding: 5px 25px;
  width: 100%;
  height: 65px;
  background-color: #fff !important;
  border-radius: 15px;
}

.form-group.postcode-group input.form-control {
  padding-right: 120px;
}

.form-group.postcode-group .btn {
  position: absolute;
  top: 11px;
  right: 15px;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Visby CF';
  width: 95px;
  height: 42px;
  line-height: 36px;
  color: #000000 !important;
  border-color: #F3EBE7 !important;
  background-color: #F3EBE7 !important;
  border-radius: 10px;
}

.form-group.select-box {
  background-color: #fff !important;
  border-radius: 15px;
}

.form-group.select-box::before {
  color: #000000;
  top: 50%;
  transform: translate(0, -50%);
  right: 30px;
}

.form-group select.form-control {
  font-size: 22px;
  color: #4D4D4D;
  font-family: 'Visby CF';
  font-weight: 700;
  padding: 5px 25px;
  width: 100%;
  height: 75px;
}

.add-restaurant-form-card button.btn {
  color: #000000 !important;
  font-size: 22px;
  font-weight: 500;
  font-family: 'Fredoka';
  padding: 0 10px;
  text-align: center;
  width: 230px;
  height: 66px;
  line-height: 64px;
  border-color: #f3ebe7 !important;
  background-color: #f3ebe7 !important;
  box-shadow: 0px 0px 40px 4px #00000026;
  border-radius: 100px;
  position: absolute;
  right: -20px;
  bottom: -40px;
}

.add-restaurant-form-card button.btn img {
  margin-top: -5px;
  margin-left: 20px;
  width: 28px;
}

/*----No-Upfront-Cost----*/
.no-upfront-cost-bg {
  margin-top: 75px;
  padding-top: 38px;
  padding-bottom: 55px;
  overflow: hidden;
}

.no-upfront-image {
  position: relative;  
}

.no-upfront-image .shape-one {
  position: absolute;
  left:-80px;
  bottom: -65px;
}
.no-upfront-image .shape-one img{
  width: 448px;
}
.no-upfront-image img {
  max-width:505px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.no-upfront-content {
  position: relative;
  padding-right: 30px;
}

.no-upfront-content h2 {
  margin-bottom: 65px;
}

.no-upfront-content p {
  font-size: 22px;
  font-weight: 700;
  color: #202020;
  font-family: 'Visby CF';
  line-height: 27px;
  margin-bottom: 65px;
}

.no-upfront-content .upfront-shape-two {
  position: absolute;
  top: -30px;
  right:-30px;
}
.no-upfront-content .upfront-shape-two img{
  width: 278px;
}
.no-upfront-content-inner {
  position: relative;
  z-index: 1;
}

/*----No-Hidden-Fees----*/
.no-hidden-fees-bg {
  margin-top: 45px;
  padding-bottom: 60px;
  padding-bottom: 15px;
  margin-bottom: 120px;
  overflow: hidden;
}

.no-hidden-content {  
  position: relative;
}

.no-hidden-content h2 {
  margin-bottom: 50px;
}

.no-hidden-content p {
  font-size: 22px;
  color: #202020;
  font-weight: 700;
  line-height: 27px;
  margin-bottom: 50px;
}

.no-hidden-content-inner {
  position: relative;
  z-index: 1;
}

.no-hidden-content .shape-one {
  position: absolute;
  left: -35px;
  bottom: -35px;
}
.no-hidden-content .shape-one img{
  width:172px;
}

.no-hidden-image {
  position: relative;
  text-align: right;  
}

.no-hidden-image img {
  max-width: 505px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.no-hidden-image .shape-two {
  position: absolute;
  top: 50%;
  right: -80px;
  transform: translate(0, -50%);
}
.no-hidden-image .shape-two img{
  width: 310px;
}

/*----No-Contracts----*/
.no-contracts-bg {
  margin-bottom: 65px;
  overflow: hidden;
}

.no-contracts-image {
  position: relative;
  padding-top: 75px;
}

.no-contracts-image img {
  max-width: 505px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.no-contracts-image .shape-one {
  position: absolute;
  top: 0px;
  left:-50px;
}
.no-contracts-image .shape-one img{
  width: 310px;
}

.no-contracts-content {
  position: relative; 
  padding-top: 55px;
}

.no-contracts-content h2 {
  margin-bottom: 50px;
}

.no-contracts-content p {
  font-family: 'Visby CF';
  font-size: 22px;
  color: #202020;
  font-weight: 700;
  line-height: 27px;
  margin-bottom: 80px;
}

.no-contracts-inner-content {
  position: relative;
  z-index: 1;
}

.no-contracts-content .shape-two {
  position: absolute;
  right: -40px;
  bottom: -10px;
}
.no-contracts-content .shape-two img{
  width: 168px;
}
/*----FAQ-Section----*/
.faq-section-bg {  
  margin-bottom: 35px;
}

.faq-section-bg .main-heading {
  text-align: center;
  margin-bottom: 35px;
}

.faq-section-bg .main-heading h4 {
  font-size: 55px;
  line-height: 85px;
}

.accordion-item {
  border: 0;
  margin-bottom: 25px;
}

.accordion-header button {
  font-family: 'Visby CF';
  font-size: 35px;
  color: #202020;
  cursor: pointer;
  font-weight: 700;
  line-height: 48px;
  border: 0;
  text-decoration: none;
  padding: 14px 100px 14px 40px;
  display: inline-block;
  width: 100%;
  border-radius: 10px;
  box-shadow: none;
  background-color: #FDF6F2;
}

.accordion-button:after {
  display: none;
}

.accordion-header button::before {
  font-size: 24px;
  color: #202020;
  position: absolute;
  content: "\f078";
  font-family: 'fontawesome';
  top: 26px;
  right: 35px;
  line-height: normal;
  transform: rotate(180deg);
}

.accordion-header button.collapsed::before {
  transform: rotate(0deg);
}

.accordion-body {
  padding: 25px 40px 15px 40px;
}

.accordion-body p {
  font-family: 'Visby CF';
  font-size: 24px;
  color: #202020;
  font-weight: 700;
  line-height: 36px;
}
.error-text{
  color:#f5222d !important;
}

@media screen and (max-width:1800px) {
  .add-restaurant-banner-bg {
    padding: 165px 92px 110px;
  }  

  .add-restaurant-banner-content h2 {
    margin-bottom: 50px;
  }

  .add-restaurant-banner-bg .shape-two img {
    width: 80px;
  }

  .no-upfront-image .shape-one img {
    max-width: 400px;
  }
  .no-upfront-content h2 {
    margin-bottom: 55px;
  }

}

@media screen and (max-width:1500px) {
  .add-restaurant-banner-bg {
    border-radius: 36px;
    padding-top: 140px;
    padding-bottom: 80px;
  }
  .add-restaurant-banner-bg .shape-two {
    top: 200px;
  }
  .add-restaurant-banner-bg .shape-two img {
    width: 75px;
  }

  .add-restaurant-banner-bg .shape-one {
    width: 365px;
  }

  .add-restaurant-banner-bg .shape-one img{
    width: 300px;
  }
  .add-restaurant-form-card{
    max-width: 480px;
    float: right;
    width: 100%;
  }
  .add-restaurant-banner-content h2 {
    margin-bottom: 55px;
  }

  .add-restaurant-banner-content h4 {
    font-size: 48px;
    line-height: 60px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-group input.form-control {
    font-size: 16px;
    padding: 5px 15px;
    height: 56px;
    border-radius: 12px;
  }

  .form-group select.form-control {
    font-size: 16px;
    padding: 5px 15px;
    height: 56px;
    border-radius: 12px;
  }

  .form-group.postcode-group .btn {
    font-size: 14px;
    top: 12px;
    width: 78px;
    height: 32px;
    right: 15px;
    border-radius: 8px;
    line-height: 28px;
  }
  .form-group.postcode-group input.form-control {
    padding-right: 105px;
  }
  .add-restaurant-form-card button.btn {
    bottom: -45px;
  }

  .add-restaurant-form-card button.btn img {
    margin-left: 20px;
    width: 30px;
  }

  .no-upfront-cost-bg {    
    padding: 30px 92px 55px 92px;
  }

  .no-upfront-content {
    padding-right: 0px;
  }

  .no-upfront-content button.btn {
    font-size: 14px;
    padding: 5px 12px;
  }

  .no-hidden-fees-bg{
    padding-left: 92px;
    padding-right: 92px;
  }

  .no-hidden-content button.btn {
    font-size: 14px;
    padding: 5px 13px;
  }

  .no-hidden-content .shape-one img {
    max-width: 172px;
  }

  .no-hidden-image .shape-two img {
    max-width: 310px;
  }

  .no-hidden-image img {
    max-width: 504px;
  }

  .no-contracts-bg {
    padding-left: 92px;
    padding-right: 92px;
    margin-bottom: 60px;
  }

  .no-contracts-image img {
    max-width: 504px;
  }

  .no-contracts-image .shape-one img {
    max-width: 310px;
  }

  .no-contracts-content .shape-two img {
    max-width: 168px;
  }

  .no-contracts-content button.btn {
    font-size: 14px;
    padding: 5px 13px;
  }

  .faq-section-bg {
    padding-left: 92px;
    padding-right: 92px;
    margin-bottom: 35px;
  }

  .faq-section-bg .main-heading h4 {
    font-size: 52px;
    line-height: 64px;
  }

  .accordion-header button {
    font-size: 30px;    
  }

  .error-text{
    font-size: 13px;
  }

  #number-verification-popup .modal-body h6,
  #edit-number-popup .modal-body h6,
  #update-number-popup .modal-body h6 {
    font-size: 18px;
  }

  #edit-number-popup .modal-body .add-btn a.btn,
  #update-number-popup .modal-body .add-btn a.btn,
  #number-verification-popup .modal-body .add-btn a.btn {
    font-size: 14px;
    padding: 4px 15px;
  }

  #edit-number-popup .modal-body .form-group input.form-control,
  #update-number-popup .modal-body .form-group input.form-control,
  #number-verification-popup .modal-body .form-group input.form-control {
    padding: 5px 15px 6px;
    height: 36px;
  }

  #number-verification-popup .modal-body span {
    font-size: 14px;
  }

}

@media screen and (max-width:1300px) {
  .add-restaurant-banner-bg {
    padding: 150px 70px 80px;
  }
  .add-restaurant-banner-bg .shape-two img {
    width: 60px;
  }
  .no-upfront-cost-bg{
    padding-left: 72px;
    padding-right: 72px;
  }
  .no-upfront-image img {
    max-width: 100%;
  }

  .no-upfront-image .shape-one {
    left: -40px;
  }

  .no-upfront-image .shape-one img {
    max-width: 300px;
  }

  .no-upfront-content {
    padding-right: 0;
  }

  .no-upfront-content .upfront-shape-two img {
    max-width: 220px;
  }

  .no-hidden-content .shape-one img {
    max-width: 130px;
  }

  .no-hidden-image img {
    max-width: 100%;
  }

  .no-hidden-image .shape-two img {
    max-width: 325px;
  }

  .no-hidden-fees-bg {
    padding-left: 72px;
    padding-right: 72px;
    margin-bottom: 100px;
  }
  .no-contracts-bg{
    padding-left: 72px;
    padding-right: 72px;
  }
  .no-contracts-image {
    padding-top: 70px;    
  }

  .no-contracts-image img {
    max-width: 100%;
  }

  .no-contracts-image .shape-one img {
    max-width: 300px;
  }

  .no-contracts-content .shape-two {
    right: 0px;
  }

  .no-contracts-content .shape-two img {
    max-width: 180px;
  }
  .faq-section-bg{
    padding-left: 72px;
    padding-right: 72px;
  }

}

@media screen and (max-width:1199px) {
  .add-restaurant-banner-bg {
    padding: 130px 80px 80px;
  }
  .add-restaurant-banner-content h4 {
    font-size: 36px;
    line-height: 50px;
  }
  .add-restaurant-form-card {
    padding: 25px 25px 10px;
    border-radius: 25px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .add-restaurant-banner-bg .shape-two {
    top: 160px;
  }

  .add-restaurant-banner-bg .shape-two img {
    width: 40px;
  }
  
  .add-restaurant-form-card button.btn {
    font-size: 20px;
    width: 200px;
    height: 60px;
    line-height: 58px;
  }

  .add-restaurant-form-card button.btn img {
    margin-left: 12px;
    width: 22px;
  }

  .no-upfront-cost-bg {
    margin-top: 70px;
  }

  .no-upfront-image .shape-one {
    bottom: -65px;
  }

  .no-upfront-image .shape-one img {
    max-width: 250px;
  }

  .no-upfront-content h2 {
    font-size: 50px;
    margin-bottom: 30px;
  }

  .no-upfront-content p {
    font-size: 20px;
    margin-bottom: 40px;
  }

  .no-upfront-content .upfront-shape-two img {
    max-width: 180px;
  }

  .no-hidden-content h2 {
    font-size: 50px;
    margin-bottom: 30px;
  }

  .no-hidden-content p {
    font-size: 20px;
    margin-bottom: 40px;
  }

  .no-contracts-content h2 {
    font-size: 50px;
    margin-bottom: 30px;
  }

  .no-contracts-content p {
    font-size: 20px;
    margin-bottom: 40px;
  }

  .no-contracts-bg {
    margin-bottom: 60px;
  }

  .faq-section-bg .main-heading h4 {
    font-size: 48px;
  }

  .accordion-item {
    margin-bottom: 20px;
  }

  .accordion-header button {
    font-size: 28px;
  }

  .accordion-header button:before {
    top: 28px;
    right: 30px;
  }  

}

@media screen and (max-width:991px) {
  .add-restaurant-banner-bg {
    padding-left: 30px;
    padding-right: 30px;
    border-radius: 30px;
  }
  
  .add-restaurant-banner-content {
    padding-top: 65px;
    padding-bottom: 30px;
    text-align: center;
  }

  .add-restaurant-banner-bg .shape-one {
    width: 100px;
  }

  .add-restaurant-banner-bg .shape-one img {
    width: 90px;
  }

  .add-restaurant-banner-content h2 {
    margin-bottom: 35px;
  }

  .add-restaurant-banner-content h4 {
    font-size: 38px;
    line-height: 50px;
  }
  .add-restaurant-form-card{
    margin: auto;
    float: none;
  } 
  .no-upfront-cost-bg {    
    padding: 0 30px 70px 30px;
  }

  .no-upfront-image {
    padding-left: 0px;
    text-align: center;
    margin-bottom: 40px;
  }

  .no-upfront-image .shape-one {
    left:-30px;
    bottom: -50px;
  }

  .no-upfront-image .shape-one img {
    max-width: 200px;
  }

  .no-upfront-content {
    text-align: center;
  }

  .no-upfront-content .upfront-shape-two {
    top: 0;
  }

  .no-upfront-content .upfront-shape-two img {
    max-width: 120px;
  }

  .no-hidden-fees-bg {
    padding-left:30px;
    padding-right:30px;
    margin-top: 0;
    margin-bottom: 80px;
  }

  .no-hidden-content {
    text-align: center;
    padding-top: 0;
    padding-left: 0;
  }

  .no-hidden-content .shape-one {
    left: -20px;
    bottom: 0;
  }

  .no-hidden-content .shape-one img {
    max-width: 120px;
  }

  .no-hidden-image {
    padding-right: 0;
    text-align: center;
    margin-bottom: 40px;
  }
  .no-hidden-image .shape-two{
    right:-30px;
  }
  .no-hidden-image .shape-two img {
    max-width: 200px;
  }
  .no-contracts-bg{
    padding-left: 30px;
    padding-right: 30px;
  }
  .no-contracts-image {
    padding-top: 40px;
    padding-left: 0;
    text-align: center;
  }
  .no-contracts-image .shape-one{
    left:-30px;
  }
  .no-contracts-image .shape-one img {
    max-width: 200px;
  }
  .no-contracts-content .shape-two{
    bottom:0;
  }
  .no-contracts-content .shape-two img {
    max-width: 120px;
  }
  .no-contracts-content {
    padding-top: 40px;
    padding-right: 0;
    text-align: center;
  }
  .faq-section-bg{
    padding-left: 30px;
    padding-right: 30px;
  }
  .faq-section-bg .main-heading {
    margin-bottom: 40px;
  }

  .faq-section-bg .main-heading h4 {
    font-size: 45px;
    line-height: 65px;
  }

}

@media screen and (max-width:767px) {
  .add-restaurant-banner-content {
    padding-bottom: 30px;
  }

  .add-restaurant-banner-content h2 {
    margin-bottom: 25px;
  }

  .add-restaurant-banner-content h4 {
    font-size: 42px;
  }  

}

@media screen and (max-width:575px) {
  .add-restaurant-banner-bg {
    margin-bottom: 45px;
  }

  .add-restaurant-banner-content h2 {
    font-size: 48px;
    margin-bottom: 20px;
  }

  .add-restaurant-banner-content h4 {
    font-size: 36px;
    line-height: 48px;
  }

  .add-restaurant-banner-content h4 br {
    display: none;
  }

  .add-restaurant-form-card {
    padding: 25px 25px 10px;
    border-radius: 25px;
  }

  .add-restaurant-form-card button.btn {
    font-size: 22px;
    width: 240px;
    height: 60px;
    line-height: 56px;
  }

  .add-restaurant-form-card button.btn img {
    margin-left: 15px;
    width: 30px;
  }

  .no-upfront-cost-bg {
    margin-top: 60px;
    padding-bottom: 60px;
  }

  .no-upfront-image {
    margin-bottom: 30px;
  }

  .no-upfront-content h2 {
    margin-bottom: 20px;
  }

  .no-upfront-content p {
    margin-bottom: 30px;
  }

  .no-hidden-fees-bg {
    margin-bottom: 60px;
  }

  .no-hidden-image {
    margin-bottom: 30px;
  }

  .no-hidden-content h2 {
    margin-bottom: 20px;
  }

  .no-hidden-content p {
    margin-bottom: 30px;
  }

  .no-contracts-content {
    padding-top: 30px;
  }

  .no-contracts-content h2 {
    margin-bottom: 20px;
  }

  .no-contracts-content p {
    margin-bottom: 30px;
  }

  .faq-section-bg .main-heading {
    margin-bottom: 30px;
  }

  .faq-section-bg .main-heading h4 {
    font-size: 42px;
    line-height: 55px;
  }

  .accordion-header button {
    font-size: 26px;
    padding: 10px 60px 10px 25px;
  }

  .accordion-header button:before {
    font-size: 22px;
    top: 20px;
    right: 25px;
  }

  .accordion-body {
    padding: 15px 25px 10px;
  }

  .accordion-body p {
    font-size: 20px;
    line-height: 28px;
  }

}

@media screen and (max-width:480px) {
  .add-restaurant-banner-bg {
    margin-bottom: 35px;
    padding: 115px 13px 70px 13px;
    border-radius: 20px;
  }
  .add-restaurant-banner-bg .shape-one img {
    width: 70px;
  }
  .add-restaurant-banner-content {
    padding-top: 0;
    padding-bottom: 20px;
  }

  .add-restaurant-banner-content h2 {
    font-size: 42px;
    margin-bottom: 20px;
  }

  .add-restaurant-banner-content h4 {
    font-size: 34px;
    line-height: 44px;
  }

  .add-restaurant-form-card {
    padding: 20px 20px 10px;
    border-radius: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group input.form-control,
  .form-group select.form-control {
    border-radius: 10px;
    height: 50px;
    font-size: 15px;
  }

  .form-group.postcode-group input.form-control {
    padding-right: 85px;
  }

  .form-group.postcode-group .btn {
    top: 9px;
    right: 10px;
    width: 68px;
    font-size: 13px;
  }

  .add-restaurant-form-card button.btn {
    font-size: 20px;
    width: 190px;
    height: 55px;
    line-height: 52px;
    right: 0;
    bottom: -45px;
  }

  .add-restaurant-form-card button.btn img {
    margin-left: 10px;
    width: 26px;
  }

  .no-upfront-cost-bg {
    margin-top: 40px;
    padding: 0 13px 40px 13px;    
  }

  .no-upfront-content h2 {
    font-size: 42px;
    margin-bottom: 15px;
  }

  .no-upfront-content p {
    font-size: 18px;
    margin-bottom: 25px;
  }

  .no-hidden-fees-bg {
    padding:0 13px;
    margin-bottom: 50px;
  }

  .no-hidden-content h2 {
    font-size: 42px;
    margin-bottom: 15px;
  }

  .no-hidden-content p {
    font-size: 18px;
    margin-bottom: 25px;
  }

  .no-contracts-bg {
    padding: 0 13px;
    margin-bottom: 50px;
  }

  .no-contracts-content h2 {
    font-size: 42px;
    margin-bottom: 15px;
  }

  .no-contracts-content p {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .faq-section-bg {
    padding: 0 13px;
    margin-bottom: 35px;
  }

  .faq-section-bg .main-heading {
    margin-bottom: 25px;
  }

  .faq-section-bg .main-heading h4 {
    font-size: 35px;
    line-height: 48px;
  }

  .accordion-item {
    margin-bottom: 15px;
  }

  .accordion-header button{
    font-size: 22px;
    padding: 6px 40px 6px 20px;
  }

  .accordion-header button:before {
    top: 20px;
    font-size: 20px;
    right: 20px;
  }

  .accordion-body {
    padding: 15px 20px 0;
  }

  .accordion-body p {
    font-size: 18px;
    line-height: 28px;
  }
  .no-upfront-image .shape-one {
    left: -10px;
  }
  .no-upfront-image .shape-one img {
    max-width: 120px;
  }

  .no-upfront-content .upfront-shape-two{
    right: -10px;
  }
  .no-upfront-content .upfront-shape-two img {
    max-width: 80px;
  }
  .no-hidden-image .shape-two {
    right: -10px;
  }
  .no-hidden-image .shape-two img{
    max-width: 100px; 
  }
  .no-hidden-content .shape-one {
    left: 15px;
    bottom: 0;
  }
  .no-contracts-image .shape-one {
    left: -10px;
  }
  .no-contracts-image .shape-one img {
    max-width: 150px;
  }
  .no-hidden-content .shape-one img {
    max-width: 100px;
  }

  .no-contracts-content .shape-two {
    bottom: 30px;
  }

  .no-contracts-content .shape-two img {
    max-width: 100px;
  }


}
