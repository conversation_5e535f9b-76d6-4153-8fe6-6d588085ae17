import { Component, ElementRef, HostListener, Inject, Input, OnInit, PLATFORM_ID, Renderer2, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { Restaurant } from '../../core/models/restaurant';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { environment } from '../../../environments/environment';
import { DomSanitizer, Meta, SafeResourceUrl, Title } from '@angular/platform-browser';
import { CurrencyPipe, DOCUMENT, Location, ViewportScroller, formatDate, isPlatformBrowser } from '@angular/common';
import { ModalDismissReasons, NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Menu } from '../../core/models/menu';
import { Cart } from '../../core/models/cart';
import { Category } from '../../core/models/category';
import { Order } from '../../core/models/order';
import { User } from '../../core/models/user';
import { Variant } from '../../core/models/variant';
import { CategoryService } from '../../core/services/category.service';
import { CartService } from '../../core/services/cart.service';
import { MenuService } from '../../core/services/menu.service';
import { Promotions } from '../../core/models/promotions';
import { Review } from '../../core/models/review';
import { ReviewService } from '../../core/services/review.service';
import { SlickCarouselComponent } from 'ngx-slick-carousel';
import { CanonicalService } from '../../shared/canonical.service';

@Component({
  selector: 'app-menus',
  host: { ngSkipHydration: 'true' },
  templateUrl: './menus.component.html',
  styleUrls: ['./menus.component.scss']
})

export class MenusComponent implements OnInit {
  @ViewChild('addonModal', { static: true }) addonModal: ElementRef;
  @ViewChild('allergyModal', { static: true }) allergyModal: ElementRef;
  @ViewChild('itemModal', { static: true }) itemModal: ElementRef;
  @ViewChild('closeRetaurantModal', { static: true }) closeRetaurantModal: ElementRef;
  @ViewChild('preRetaurantModal', { static: true }) preRetaurantModal: ElementRef;
  @ViewChild('currentUnavailableRetaurantModal', { static: true }) currentUnavailableRetaurantModal: ElementRef;
  @ViewChild('itemNotAvailableModal', { static: true }) itemNotAvailableModal: ElementRef;

  @Input() public spiedTags = [];

  private subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  menus: Menu[] = [];
  carts: Cart[] = [];
  originalCategories: Category[] = [];
  menuRecommended: Menu[] = [];
  categories: Category[] = [];
  reviews: Review[] = [];
  selectedAddonDummy: NewAddon[] = [];
  reviewCounts: any;
  order: Order = new Order();

  promotions: Promotions = new Promotions();
  modalOptions: NgbModalOptions;

  currentIndex = 0;

  selectedMenu: Menu;
  selectedDummyMenu: Menu;
  selectedVariant: Variant = new Variant();
  selectedDummyVariant: Variant = new Variant();
  addVariant: Variant;

  addonVisible: boolean = true;
  subAddonVisible: boolean = false;
  editAddonVisible: boolean = false;
  searchData: string;
  openTime: string;
  closeTime: string;
  selectedQnt: number = 1;
  selectedSubAddonstring: string;
  selectedSubAddonPrice: number = 0;
  selectedAddonPrice: number = 0;
  modalReference: any;
  scrollingCategoryId: string;
  timingOrderType: string = 'delivery';
  streetAddress: SafeResourceUrl;
  streetRedirectAddress: string;

  hasOrderTypeMismatch = false;
  hasDayMismatch = false;

  weekday = new Array('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday');

  slideConfig = {
    // "slidesToShow": 9,
    "slidesToScroll": 1,
    "infinite": false,
    "draggable": false,
    "variableWidth": true,
    // "autoplay": false,
    // "autoplaySpeed": 1500,
    useTransform: false,
    cssEase: 'linear',
    "responsive": [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 7,
          slidesToScroll: 5,
          infinite: true,
          dots: false,
          draggable: true
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 6,
          slidesToScroll: 5
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 5,
          slidesToScroll: 5
        }
      },
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2
        }
      }
    ]
  };

  slickInit(e) {
    console.log('slick initialized');
  }

  breakpoint(e) {
    console.log('breakpoint');
  }

  afterChange(e) {
    console.log('afterChange');
  }

  beforeChange(e) {
    console.log('beforeChange');
  }

  isLoading = false; error = null;
  visible = false;
  isModelLoading = false; isAddonModelLoading = false;
  isReviewLoading = false; errorReview = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  categoryOptions = { nopaginate: 1, prefilled: 1 };

  constructor(
    private restaurantService: RestaurantService,
    public userService: UserService,
    private categoryService: CategoryService,
    private menuService: MenuService,
    private cartService: CartService,
    private reviewService: ReviewService,
    private route: ActivatedRoute,
    @Inject(DOCUMENT) private document: Document,
    private renderer: Renderer2,
    private modalService: NgbModal,
    // private currencyPipe: CurrencyPipe,
    private metaTagService: Meta,
    private titleService: Title,
    private notification: NzNotificationService,
    private router: Router,
    public sanitizer: DomSanitizer,
    public canonicalService: CanonicalService,
    private el: ElementRef,
    private location: Location,
  ) { }

  ngOnInit() {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    this.order.order_type = this.cartService.getOrderType();
    if (this.route.snapshot.paramMap.get('name') && this.route.snapshot.paramMap.get('name') != null) {
      this.restaurant.seo_url = this.route.snapshot.paramMap.get('name');
      this.fetchRestaurantName();
    } else if (this.restaurant.id) {
      this.fetchRestaurant();
    } else {
      this.router.navigateByUrl('/location/' + this.restaurantService.zipcode)
    }
  }

  @HostListener('contextmenu', ['$event'])
  onContextMenu(event: Event): void {
    event.preventDefault();
  }

  @HostListener('paste', ['$event']) blockPaste(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('copy', ['$event']) blockCopy(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('cut', ['$event']) blockCut(e: KeyboardEvent) {
    e.preventDefault();
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(): void {
    const scrollPosition = window.scrollY;
    const categoryElements = this.document.querySelectorAll('.category-section');

    for (let i = categoryElements.length - 1; i >= 0; i--) {
      const categoryElement = categoryElements[i] as HTMLElement;
      const categoryTop = categoryElement.offsetTop;
      if (scrollPosition >= categoryTop - 200) {
        this.scrollingCategoryId = categoryElement.getAttribute('data-category-id')
        break;
      }
    }
  }

  // Custom arrow button methods
  prevSlide() {
    this.carousel.slickPrev();
  }

  nextSlide() {
    this.carousel.slickNext();
  }

  menuOpen(): void {
    this.visible = true;
    this.renderer.addClass(this.document.body, 'open-dropdown');
  }

  menuClose(): void {
    this.visible = false;
    this.renderer.removeClass(this.document.body, 'open-dropdown');
  }

  trackByFn(index, item) {
    return item.id;
  }

  fetchRestaurant() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = true))
      .subscribe(res => {
        this.restaurant = res;
        this.metaTagService.updateTag({ property: 'robots', content: 'index, follow' });
        if (this.restaurant.gogrubz_meta_title) {
          this.titleService.setTitle(this.restaurant.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.gogrubz_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.gogrubz_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.gogrubz_meta_description });
        }
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        if (this.restaurant.sourcelatitude) {
          let address = this.restaurant.street_address.split(' ').join('+');
          let addresses = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q='
            + address.split(',').join('') + '&z=14&output=embed';
          this.streetAddress = this.sanitizer.bypassSecurityTrustResourceUrl(addresses);
          this.streetRedirectAddress = 'https://www.google.com/maps?q=' + this.restaurant.sourcelatitude + ',' + this.restaurant.sourcelongitude;
        }

        if (!this.user?.id || this.user?.phone_verify) {
          if ((res.restaurant_delivery == 'No' && this.restaurant.restaurant_pickup == 'No') || this.restaurant.online_order == 'No') {
            this.modalService.open(this.currentUnavailableRetaurantModal, {});
          } else if (res.currentStatus == 'Closed') {
            this.modalService.open(this.closeRetaurantModal, {});
          } else if (res.currentStatus == 'PreOrder') {
            this.modalService.open(this.preRetaurantModal, {});
          } else { }
        }

        if (this.order.order_type == 'delivery' && res.restaurant_delivery == 'No') {
          this.orderType('pickup');
        }
        if (this.order.order_type == 'pickup' && this.restaurant.restaurant_pickup == 'No') {
          this.orderType('delivery');
        }
        this.promotions = this.restaurant.promotions[0];

        var objToday = new Date();
        if (this.restaurant.working_time == 'both' || this.restaurant.working_time == 'morning') {
          var wk = this.weekday[objToday.getDay()] + '_first_closetime';
          this.closeTime = res[wk];
          var wks = this.weekday[objToday.getDay()] + '_first_opentime';
          this.openTime = res[wks];
        } else {
          var wk = this.weekday[objToday.getDay()] + '_second_closetime';
          this.closeTime = res[wk];
          var wks = this.weekday[objToday.getDay()] + '_second_opentime';
          this.openTime = res[wks];
        }

        this.fetchReviews();
        this.fetchReviewCounts();
        this.fetchCategories();
      }, err => this.error = err)
    );
  }

  fetchRestaurantName() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.show_name(this.restaurant.seo_url)
      .pipe(finalize(() => this.isLoading = true))
      .subscribe(res => {
        this.restaurant = res;
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem(environment.googleFirebase, this.restaurant.id);
          if (!localStorage.getItem(environment.zipcode)) {
            localStorage.setItem(environment.zipcode, res.zipcode);
          }
        }
        this.metaTagService.updateTag({ property: 'robots', content: 'index, follow' });
        if (this.restaurant.gogrubz_meta_title) {
          this.titleService.setTitle(this.restaurant.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.gogrubz_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.gogrubz_meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.gogrubz_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.gogrubz_meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.gogrubz_meta_description });
          this.metaTagService.updateTag({ property: 'og:title', content: this.restaurant.site_setting.gogrubz_meta_title });
          this.metaTagService.updateTag({ property: 'og:description', content: this.restaurant.site_setting.gogrubz_meta_description });
        }
        if (this.restaurant.image_url) {
          this.metaTagService.updateTag({ property: 'og:image', content: this.restaurant.image_url });
        }
        if (this.restaurant.sourcelatitude) {
          let address = this.restaurant.street_address.split(' ').join('+');
          let addresses = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q='
            + address.split(',').join('') + '&z=14&output=embed';
          this.streetAddress = this.sanitizer.bypassSecurityTrustResourceUrl(addresses);
          this.streetRedirectAddress = 'https://www.google.com/maps?q=' + this.restaurant.sourcelatitude + ',' + this.restaurant.sourcelongitude;
        }
        let newCanonicalUrl = 'https://www.gogrubz.com' + this.router.url;
        this.metaTagService.updateTag({ property: 'og:url', content: newCanonicalUrl });
        this.canonicalService.setCanonicalURL(newCanonicalUrl);
        if (!this.user?.id || this.user?.phone_verify) {
          if ((res.restaurant_delivery == 'No' && this.restaurant.restaurant_pickup == 'No') || this.restaurant.online_order == 'No') {
            this.modalService.open(this.currentUnavailableRetaurantModal, {});
          } else if (res.currentStatus == 'Closed') {
            this.modalService.open(this.closeRetaurantModal, {});
          } else if (res.currentStatus == 'PreOrder') {
            this.modalService.open(this.preRetaurantModal, {});
          } else { }
        }

        if (this.order.order_type == 'delivery' && res.restaurant_delivery == 'No') {
          this.orderType('pickup');
        }
        if (this.order.order_type == 'pickup' && this.restaurant.restaurant_pickup == 'No') {
          this.orderType('delivery');
        }
        this.promotions = this.restaurant.promotions[0];

        var objToday = new Date();
        if (this.restaurant.working_time == 'both' || this.restaurant.working_time == 'morning') {
          var wk = this.weekday[objToday.getDay()] + '_first_closetime';
          this.closeTime = res[wk];
          var wks = this.weekday[objToday.getDay()] + '_first_opentime';
          this.openTime = res[wks];
        } else {
          var wk = this.weekday[objToday.getDay()] + '_second_closetime';
          this.closeTime = res[wk];
          var wks = this.weekday[objToday.getDay()] + '_second_opentime';
          this.openTime = res[wks];
        }

        this.fetchReviews();
        this.fetchReviewCounts();
        this.fetchCategories();
      }, err =>
        this.router.navigateByUrl('/location/' + this.restaurantService.zipcode)
      )
    );
  }

  favourite(favourite: boolean) {
    this.restaurant.restaurant_id = this.restaurant.id;
    this.restaurant.user_id = this.user.id;
    this.restaurant.is_favourite = favourite;
    this.restaurantService.favourite(this.restaurant)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.fetchRestaurant();
        },
        (err) => { }
      );
  }

  allergyShow() {
    this.modalService.dismissAll();
    this.modalService.open(this.allergyModal, {});
  }

  fetchCategories() {
    this.isLoading = true; this.error = null;

    this.subs.add(
      this.categoryService.get({ nopaginate: 1, prefilled: 1, restaurant_id: this.restaurant.id,order_type: this.order.order_type })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.originalCategories = res;
            this.menuRecommended = [];
            this.originalCategories.forEach(category => {
              let tempCategory = Object.assign({}, category);
              tempCategory.menu.forEach(menu => {
                if (menu?.featured == 'Yes') {
                  this.menuRecommended.push(menu);
                }
              });
            });
            this.slideConfig = {
              // "slidesToShow": 5,
              "slidesToScroll": 1,
              "infinite": false,
              "draggable": false,
              "variableWidth": true,
              // "autoplay": true,
              // "autoplaySpeed": 1500,
              useTransform: false,
              cssEase: 'linear',
              "responsive": [
                {
                  breakpoint: 1400,
                  settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1,
                    infinite: true,
                    dots: false,
                    draggable: true
                  }
                },
                {
                  breakpoint: 1300,
                  settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    infinite: true,
                    dots: false,
                    draggable: true
                  }
                },
                {
                  breakpoint: 1200,
                  settings: {
                    slidesToShow: 4,
                    slidesToScroll: 4,
                    infinite: true,
                    dots: false,
                    draggable: true
                  }
                },
                {
                  breakpoint: 992,
                  settings: {
                    slidesToShow: 3,
                    slidesToScroll: 3
                  }
                },
                {
                  breakpoint: 768,
                  settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2
                  }
                },
                {
                  breakpoint: 500,
                  settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                  }
                }
              ]
            };
            this.categories = res;
            this.scrollingCategoryId = this.categories[0].id;
            for (let cat of this.categories) {
              this.menus.concat(cat.menu);
            }
            this.fetchCarts();
          },
          (err) => {
            this.fetchCarts();
            this.categories = [];
          }
        )
    );
  }

  fetchReviews() {
    this.isLoading = false; this.error = null;

    this.subs.add(this.reviewService.get({ restaurant_id: this.restaurant.id, per_page: 2 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.reviews = res.data;
        }, (err) => {
          this.reviews = [];
        }
      )
    )
  }

  fetchReviewCounts() {
    this.isReviewLoading = true; this.error = null;

    this.subs.add(this.reviewService.count({ restaurant_id: this.restaurant.id })
      .pipe(finalize(() => this.isReviewLoading = false))
      .subscribe(
        (res) => {
          this.reviewCounts = res;
        }, (err) => {
          this.errorReview = err
        }
      )
    )
  }

  calculateAverageRating(targetRating: number): number {
    if (this.reviewCounts) {
      const filteredReviews = this.reviewCounts.filter(review => review.rating === targetRating);
      if (filteredReviews.length === 0 || filteredReviews[0].total_count <= 0) {
        return 0;
      }
      return filteredReviews[0].total_count / this.restaurant.total_reviews * 100;
    } else {
      return 0;
    }
  }

  fetchCarts() {
    this.carts = this.cartService.getCart();
    this.categories.forEach(category => {
      category.menu.forEach(menu => {
        menu.qty = 0;
        this.carts.forEach(cart => {
          if (cart?.menu_id == menu.id) {
            menu.qty = menu.qty + cart.quantity;
          }
        });
      });
    });

    this.menuRecommended.forEach(menu => {
      menu.qty = 0;
      this.carts.forEach(cart => {
        if (cart?.menu_id == menu.id) {
          menu.qty = menu.qty + cart.quantity;
        }
      });
    });

    if (this.carts.length > 0) {
      this.renderer.addClass(this.document.body, 'added-menu-item');
    } else {
      this.renderer.removeClass(this.document.body, 'added-menu-item');
    }
  }

  addprice(event, price, Id) {
    if (!this.editAddonVisible) {
      if (this.selectedAddonDummy.length > 0) {
        const addon = this.selectedAddonDummy.find(addon => addon.id == Id);
        var index = this.selectedAddonDummy.indexOf(addon);
        if (addon) {
          this.selectedAddonPrice = this.selectedAddonPrice - addon.price;
          this.selectedAddonPrice = this.selectedAddonPrice + price;
          this.selectedAddonDummy[index].price = price;
        } else {
          let addonObject: NewAddon = new NewAddon();
          addonObject.id = Id;
          addonObject.price = price;
          this.selectedAddonDummy.push(addonObject);
          this.selectedAddonPrice = this.selectedAddonPrice + price;
        }
      } else {
        let addonObject: NewAddon = new NewAddon();
        addonObject.id = Id;
        addonObject.price = price;
        this.selectedAddonDummy.push(addonObject);
        this.selectedAddonPrice = this.selectedAddonPrice + price;
      }
    }
  }

  addCheckboxPrice(event, price) {
    if (!this.editAddonVisible) {
      if (event.checked) {
        this.selectedAddonPrice = this.selectedAddonPrice + price;
      } else {
        this.selectedAddonPrice = this.selectedAddonPrice - price;
      }
    }
  }

  saveCarts(carts: Cart[]) {
    this.cartService.saveCart(carts);
    this.fetchCarts();
    this.selectedMenu = null;
    this.selectedSubAddonstring = null;
    this.selectedSubAddonPrice = 0;
    this.selectedAddonPrice = 0;
    this.editAddonVisible = false;
    this.subAddonVisible = false;
    this.selectedAddonDummy = [];
    this.selectedVariant = new Variant();
    this.addVariant = null;
    this.selectedQnt = 1;
  }

  orderType(order_type: string) {
    let oldOrderType = this.order.order_type;
    this.order.order_type = order_type;
    this.cartService.saveOrderType(order_type);
    // Reset mismatch flags
    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;
 
    this.carts = this.cartService.getCart();
    if (this.carts.length > 0) {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      this.carts.forEach(item => {
        const itemOrderType = item.product_order_type?.toLowerCase();
        const itemDaysRaw = item.product_day?.toLowerCase() || 'all';
        const itemDays = itemDaysRaw.split(',').map(day => day.trim());
        if (itemOrderType !== 'both' && itemOrderType !== order_type.toLowerCase()) {
          this.hasOrderTypeMismatch = true;
        }
        if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
          this.hasDayMismatch = true;
        }
      });
      if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
        this.modalService.dismissAll();
        this.modalReference = this.modalService.open(this.itemNotAvailableModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return order_type;
      }
 
      // ✅ Save new order type if no mismatch
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    } else {
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    }
 
    return order_type;
  }

  handleItemNotAvailable(type: string) {
    if (type === 'yes') {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });
 
      itemsToRemove.forEach(item => {
        const index = this.carts.indexOf(item);
        if (index !== -1) {
          this.carts.splice(index, 1);
          this.saveCarts(this.carts);
        }
      });
    } else {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });
 
      if (itemsToRemove.length > 0) {
        if (this.order.order_type == 'delivery') {
          this.order.order_type = 'pickup';
          this.cartService.saveOrderType('pickup'); 
        }else if (this.order.order_type == 'pickup') {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }else{
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }
      }
    }
    this.modalService.dismissAll();
  } 

  search(search: string) {
    if (!search) {
      this.searchData = '';
      this.categories = this.originalCategories
    } else {
      // search = search.replace(/\s/g, "");
      // search = search.trim();
      this.searchData = search;
      let tempAllItems = Object.assign([], this.originalCategories)
      let count = 0;
      let filteredArray = []
      tempAllItems.forEach(category => {
        let tempCategory = Object.assign({}, category);
        let searchedMenu = []
        tempCategory.menu.forEach(menu => {
          if (menu?.menu_name?.toLocaleLowerCase().includes(search.toLocaleLowerCase())) {
            searchedMenu.push(Object.assign({}, menu));
            try {
              if (window.innerWidth <= 1199) {
                this.document.getElementById("allMenu").scrollIntoView({ behavior: 'smooth' });
              }
            } catch (e) { }
          }
        });
        if (searchedMenu?.length > 0) {
          tempCategory.menu = searchedMenu;
          if (count <= 0) {
            this.scrollingCategoryId = tempCategory?.id;
            count++;
          }
          filteredArray.push(tempCategory);
        }
      });
      this.categories = Object.assign([], filteredArray);
      return;

    }
  }

  fetchMenuItem(model, menuId) {
    this.isModelLoading = true;

    this.subs.add(
      this.menuService
        .show(menuId)
        .pipe(finalize(() => (this.isModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            this.selectedVariant = this.selectedMenu?.variants[0];
            this.addVariant = this.selectedVariant;
            this.openModal(model);
          },
          (err) => { }
        )
    );
  }

  fetchVartiantItem(model, menuId, varaintId) {
    this.isAddonModelLoading = true;

    this.subs.add(
      this.menuService.show(menuId)
        .pipe(finalize(() => (this.isAddonModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            if (this.selectedMenu?.variants.length > 1) {
              this.selectedVariant = this.selectedMenu?.variants.find(
                (varint) => varint.id == varaintId
              );
              this.addVariant = this.selectedVariant;
              if (this.selectedVariant.main_addons) {
                this.openModal(model);
              } else {
                this.selectedSubAddonstring = '';
                this.selectedSubAddonPrice = 0;
                this.selectedAddonPrice = 0;
              }
            } else {
              this.selectedVariant = this.selectedMenu?.variants[0];
              this.modalReference = this.modalService.open(this.addonModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
            }
          },
          (err) => { }
        )
    );
  }

  fetchVartiantAddonItem(model, menuId, varaintId) {
    this.isAddonModelLoading = true; this.selectedQnt = 1;

    this.subs.add(
      this.menuService.show(menuId)
        .pipe(finalize(() => (this.isAddonModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            this.selectedDummyMenu = res;
            this.selectedSubAddonstring = '';
            this.selectedSubAddonPrice = 0;
            this.selectedAddonPrice = 0;
            this.subAddonVisible = false;
            this.selectedAddonDummy = [];
            if (this.selectedMenu?.variants.length > 1) {
              this.selectedVariant = this.selectedMenu?.variants.find(
                (varint) => varint.id == varaintId
              );
              this.selectedDummyVariant = Object.assign({}, this.selectedVariant);
              if (this.selectedVariant.main_addons) {
                this.subAddonVisible = true;
                // this.openModal(model);
              } else {
                this.selectedSubAddonstring = '';
                this.selectedSubAddonPrice = 0;
                this.selectedAddonPrice = 0;
              }
            } else {
              this.selectedVariant = this.selectedMenu?.variants[0];
              this.modalReference = this.modalService.open(this.addonModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
            }
          },
          (err) => { }
        )
    );
  }

  editAddon() {
    this.subAddonVisible = true;
    this.editAddonVisible = true;
    this.selectedMenu = Object.assign({}, this.selectedDummyMenu);
    this.selectedVariant = Object.assign({}, this.selectedDummyVariant);
  }

  cartempty() {
    this.carts = [];
    this.cartService.saveCart(this.carts);
    this.cartService.removeCart();
    this.modalService.dismissAll();
    this.renderer.removeClass(this.document.body, 'added-menu-item');
  }

  addItemToCart(model, menu, menuId) {
    this.selectedMenu = menu;
    const cart = this.carts.find(cart => cart.menu_id == menu.id);
    var index = this.carts.indexOf(cart);

    if (this.carts.length > 0) {
      if (this.carts[0].restaurant_id != this.restaurant.id) {
        this.modalService.open(this.itemModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return;
      }
    }

    if (!cart || this.carts[index].subaddons_name || menu.menu_addon != 'No') {
      if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'single') {
        let cart = new Cart();
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant.id;
        cart.menu_name = this.selectedMenu.menu_name;
        if (this.selectedMenu.product_percentage > 0) {
          cart.menu_price = this.selectedMenu?.variants[0]?.orginal_price - (this.selectedMenu?.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = this.selectedMenu?.variants[0]?.orginal_price - (this.selectedMenu?.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price.toFixed(2);
        } else {
          cart.menu_price = this.selectedMenu?.variants[0]?.orginal_price;
          cart.total_price = this.selectedMenu?.variants[0]?.orginal_price;
          cart.total_price.toFixed(2);
        }
        cart.quantity = 1;
        cart.product_day = this.selectedMenu.product_day;
        cart.product_order_type = this.selectedMenu.product_order_type;
        cart.customer_id = this.user?.id;
        if (this.restaurant?.image_type == 'Yes') {
          cart.image_url = menu.image_url;
        } else {
          cart.image_url = '';
        }
        this.carts.push(cart);
        this.saveCarts(this.carts);
        // this.notification.info('item added!', null);
      } else if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'multiple') {
        this.fetchMenuItem(model, menuId);
      } else if (this.selectedMenu.menu_addon == 'Yes' && this.selectedMenu.price_option == 'single') {
        this.fetchVartiantItem(model, menuId, this.selectedMenu?.variants[0].id);
      } else {
        this.fetchVartiantItem(model, menuId, this.selectedMenu?.variants[0].id);
      }
    } else {
      var index = this.carts.indexOf(cart);
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
      this.saveCarts(this.carts);
    }
    this.fetchCarts();
  }

  updateToCart(cart: Cart, index, event: string) {
    if (event == 'add') {
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;
      if (quantity > 0) {
        this.carts[index].quantity = quantity;
        this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
        this.carts[index].total_price.toFixed(2);
      } else {
        this.carts.splice(index, 1);
      }
    }

    if (event == 'delete') {
      this.carts.splice(index, 1);
    }
    this.saveCarts(this.carts);
  }

  updateSelected(event: string) {
    if (event == 'add') {
      this.selectedQnt = this.selectedQnt + 1;
    }

    if (event == 'remove') {
      var quantity = this.selectedQnt - 1;
      if (quantity > 0) {
        this.selectedQnt = quantity;
      } else {
        this.selectedQnt = 1;
      }
    }
  }

  getGrandTotal() {
    let grand_total = 0;
    this.carts.forEach(item => {
      grand_total = grand_total + item.total_price;
    });
    return grand_total;
  }

  closeAll() {
    this.modalService.dismissAll();
    this.selectedAddonPrice = 0;
    this.selectedQnt = 1;
    this.selectedAddonDummy = [];
    this.subAddonVisible = false;
    this.editAddonVisible = false;
    this.selectedVariant = new Variant();
    this.addVariant = new Variant();
  }

  validateMultiple(menuModal) {
    if (!this.selectedVariant.id) {
      this.selectedVariant.required_error = true;
      return;
    }

    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }

        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id);
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id);
          return;
        }

        if (subAddonCount == 0 && mainAddon.mainaddons_mini_count == 0 && this.addonVisible) {
          this.selectedSubAddonPrice = this.selectedVariant.orginal_price;
          this.fetchVartiantAddonItem(menuModal, this.selectedMenu.id, this.selectedVariant.id);
          return;
        }
      }
    }

    if (this.selectedMenu.menu_addon == 'No') {
      this.selectedSubAddonstring = this.selectedVariant.sub_name;
      this.selectedSubAddonPrice = this.selectedVariant.orginal_price;
    }

    const cart = this.carts.find(cart => cart.menu_id == this.selectedMenu.id);
    var index = this.carts.indexOf(cart);
    if (cart && this.carts[index].subaddons_name == this.selectedSubAddonstring) {
      var index = this.carts.indexOf(cart);
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
    } else {
      let cart = new Cart();
      cart.menu_id = this.selectedMenu.id;
      cart.restaurant_id = this.restaurant.id;
      cart.menu_name = this.selectedMenu.menu_name;
      cart.subaddons_name = this.selectedSubAddonstring;
      cart.menu_price = this.selectedSubAddonPrice;
      cart.total_price = this.selectedQnt * this.selectedSubAddonPrice;
      cart.total_price.toFixed(2);
      cart.quantity = this.selectedQnt;
      cart.customer_id = this.user?.id;
      cart.product_day = this.selectedMenu.product_day;
      cart.product_order_type = this.selectedMenu.product_order_type;
      if (this.restaurant?.image_type == 'Yes') {
        cart.image_url = this.selectedMenu.image_url;
      } else {
        cart.image_url = '';
      }
      this.carts.push(cart);
    }
    this.saveCarts(this.carts);
    // this.notification.info('item added!', null);
    this.modalService.dismissAll();
  }

  validate(addonModel) {
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }
        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          mainAddon.min_error = true;
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          mainAddon.max_error = true;
          return;
        }
      }
    }
    this.addonVisible = false;
    // create cart menu addon string 
    var mainAddonCount: number = 0;
    this.selectedSubAddonstring = '';
    this.selectedSubAddonPrice = 0;
    this.selectedAddonPrice = 0;
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        var subAddonCount: number = 0;
        if (mainAddonCount <= 0) {
          if (this.selectedVariant.sub_name && this.selectedMenu.variants.length > 1) {
            this.selectedSubAddonstring = this.selectedVariant.sub_name + ',';
          }
          if (this.selectedMenu.product_percentage > 0) {
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + (this.selectedVariant.orginal_price - (this.selectedVariant.orginal_price * this.selectedMenu.product_percentage / 100));
          } else {
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + this.selectedVariant.orginal_price;
          }
        }
        for (let subAddon of mainAddon.sub_addons) {
          if (subAddonCount == 0 && (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id)) {
            this.selectedSubAddonstring += mainAddon.mainaddons_name;
          }
          if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
            this.selectedSubAddonstring += ' (' + subAddon.subaddons_name + ') ,';
            // + '(' + this.currencyPipe.transform(subAddon.subaddons_price, 'GBP', 'symbol', '1.2-2') + ')'
            this.selectedSubAddonPrice = this.selectedSubAddonPrice + subAddon.subaddons_price;
            subAddonCount = subAddonCount + 1;
          }
        }
        mainAddonCount = mainAddonCount + 1;
        var lastChar = this.selectedSubAddonstring.slice(-1);
        if (lastChar == '+') {
          this.selectedSubAddonstring = this.selectedSubAddonstring.slice(0, -1); // trim last character
        }

        if ((mainAddonCount) != this.selectedVariant?.main_addons?.length) {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
              if (this.selectedSubAddonstring.slice(-1) != ',') {
                this.selectedSubAddonstring = this.selectedSubAddonstring + ",";
              }
            }
          }
        }
      }
    } else {
      if (this.selectedMenu.product_percentage > 0) {
        this.selectedSubAddonPrice = this.selectedVariant?.orginal_price - (this.selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        this.selectedSubAddonPrice = this.selectedVariant?.orginal_price;
      }
    }

    if (this.selectedMenu?.variants?.length <= 1) {
      const cart = this.carts.find(cart => cart.menu_id == this.selectedMenu.id);
      var index = this.carts.indexOf(cart);
      if (cart && this.carts[index].subaddons_name == this.selectedSubAddonstring) {
        var index = this.carts.indexOf(cart);
        this.carts[index].quantity = this.carts[index].quantity + 1;
        this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
        this.carts[index].total_price.toFixed(2);
      } else {
        let cart = new Cart();
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant.id;
        cart.menu_name = this.selectedMenu.menu_name;
        cart.subaddons_name = this.selectedSubAddonstring;
        cart.menu_price = this.selectedSubAddonPrice;
        cart.total_price = this.selectedQnt * this.selectedSubAddonPrice;
        cart.total_price.toFixed(2);
        cart.quantity = this.selectedQnt;
        cart.customer_id = this.user?.id;
        cart.product_day = this.selectedMenu.product_day;
        cart.product_order_type = this.selectedMenu.product_order_type;
        cart.image_url = this.selectedMenu.image_url;
        this.carts.push(cart);
      }
      if (this.selectedMenu?.variants?.length <= 1) {
        this.modalReference.close();
      } else {
        this.subAddonVisible = false;
      }
      this.saveCarts(this.carts);
    }

    if (this.selectedMenu?.variants?.length <= 1) {
      this.modalReference.close();
    } else {
      this.subAddonVisible = false;
    }
  }

  goToCheckout() {
    if (this.carts.length > 0) {
      if (this.carts[0].restaurant_id != this.restaurant.id) {
        this.modalService.open(this.itemModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
        return;
      }
    }

    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;
 
    const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
    this.carts.forEach(item => {
      const itemOrderType = item.product_order_type?.toLowerCase();
      const itemDaysRaw = item.product_day?.toLowerCase() || 'all';
      const itemDays = itemDaysRaw.split(',').map(day => day.trim());
      if (itemOrderType !== 'both' && itemOrderType !== this.order.order_type.toLowerCase()) {
        this.hasOrderTypeMismatch = true;
      }
      if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
        this.hasDayMismatch = true;
      }
    });
    if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
      this.modalService.dismissAll();
      this.modalReference = this.modalService.open(this.itemNotAvailableModal, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
      return;
    }

    this.router.navigateByUrl('/checkout');
  }

  clickOnAccordion(categoryId) {
    this.scrollingCategoryId = categoryId;
    this.scroll('category_' + categoryId);
    this.menuClose();
  }

  scroll(id) {
    var top = document.getElementById(id).offsetTop - this.document.getElementById("category-menu").offsetHeight - 10;
    window.scrollTo(0, top);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  openModal(model) {
    this.selectedSubAddonstring = '';
    this.selectedSubAddonPrice = 0;

    // , this.modalOptions
    this.modalReference = this.modalService.open(model, { backdrop: 'static', backdropClass: 'customBackdrop', keyboard: false });
    this.modalReference.result.then(
      (result) => {
        for (let mainAddon of this.selectedVariant.main_addons) {
          for (let subAddon of mainAddon.sub_addons) {
            if (mainAddon.selectedSubAddonId == subAddon.id) {
              console.log(subAddon.subaddons_name + " is selected")
            }
            if (subAddon.selected) {
              console.log(subAddon.subaddons_name + " is selected")
            }
          }
        }
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    var val = parseFloat(event);
    var val1 = (val).toFixed(2);
    // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    return val1
  }

  keyPress(event: any) {
    const pattern = /[0-9]/;
    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  convertToTime(time, format?) {
    return formatDate(time, format ? format : 'HH:mm', 'en_US')
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.renderer.removeClass(this.document.body, 'open-dropdown');
    this.modalService.dismissAll();
    this.subs.unsubscribe();
  }

  // Reference to the ngx-slick-carousel component
  carousel: SlickCarouselComponent | undefined;
}

class NewAddon {
  id: string;
  price: number;
}
