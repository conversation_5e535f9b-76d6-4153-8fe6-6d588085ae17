
export class Cuisines {
  id: string;
  cuisine_name: string;
  cuisine_image: string;
  cuisine_seo: string;
  first_bg: string;
  second_bg: string;
  delete_status: string = 'N';

  selected: boolean = false;
  status: boolean = true;

  image_url: string;

  imageFile: any;

  created_at: string;
  updated_at: string;

  static toFormData(cuisines: Cuisines) {
    const formData = new FormData();

    if (cuisines.id) formData.append('id', cuisines.id);
    if (cuisines.cuisine_name) formData.append('cuisine_name', cuisines.cuisine_name);
    if (cuisines.cuisine_image) formData.append('cuisine_image', cuisines.cuisine_image);
    if (cuisines.cuisine_seo) formData.append('cuisine_seo', cuisines.cuisine_seo);
    if (cuisines.delete_status) formData.append('delete_status', cuisines.delete_status);

    formData.append('status', cuisines.status ? '1' : '0');

    if (cuisines.imageFile) formData.append('image', cuisines.imageFile, cuisines.imageFile.name)

    return formData;
  }
}
