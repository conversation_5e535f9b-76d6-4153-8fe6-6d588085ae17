import { NgModule } from '@angular/core';
import { BrowserModule, provideClientHydration, withHttpTransferCacheOptions } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule, provideAnimations } from '@angular/platform-browser/animations';
import { SharedModule } from './shared/shared.module';
// import { SocialLoginModule } from 'angularx-social-login';
import { ToastrModule, provideToastr } from 'ngx-toastr';
import { provideHttpClient } from '@angular/common/http';
import { ShellComponent } from './shell/shell.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';

@NgModule({
  declarations: [
    AppComponent,
    ShellComponent
  ],
  imports: [
    NgbModule,
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    SharedModule,
    // SocialLoginModule,
    // ToastrModule.forRoot()
  ],
  providers: [
    provideHttpClient(),
    // provideClientHydration(),
    provideClientHydration(withHttpTransferCacheOptions({
      includePostRequests: true
    })),
    provideToastr(),
    provideAnimations(),
    // { provide: LocationStrategy, useClass: HashLocationStrategy }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
