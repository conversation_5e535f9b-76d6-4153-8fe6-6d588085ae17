import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './core/auth.guard';
import { AccountComponent } from './pages/account/account.component';
import { ShellComponent } from './shell/shell.component';

const routes: Routes = [
  {
    path: '',
    component: ShellComponent,
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    children: [
      { path: '', pathMatch: 'full', redirectTo: '' },
      { path: '', loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeModule) },
      { path: 'home', loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeModule) },
      { path: 'location/:zipcode', loadChildren: () => import('./pages/restaurants/restaurants.module').then(m => m.RestaurantModule) },
      { path: ':city/:name/menus', loadChildren: () => import('./pages/menus/menus.module').then(m => m.MenusModule) },
      { path: 'locations', loadChildren: () => import('./pages/locations/locations.module').then(m => m.LocationModule) },
      { path: ':name/reviews', loadChildren: () => import('./pages/reviews/reviews.module').then(m => m.ReviewModule) },
      { path: 'checkout', loadChildren: () => import('./pages/checkout/checkout.module').then(m => m.CheckoutModule) },
      { path: 'order-details/:id', loadChildren: () => import('./pages/order-details/order-details.module').then(m => m.OrderDetailModule) },
      { path: 'privacy-policy', loadChildren: () => import('./pages/privacy-policy/privacy-policy.module').then(m => m.PrivacyPolicyModule) },
      { path: 'terms-condition', loadChildren: () => import('./pages/terms-condition/terms-condition.module').then(m => m.TermsConditionModule) },
      { path: 'about', loadChildren: () => import('./pages/about/about.module').then(m => m.AboutModule) },
      { path: 'add-restaurant', loadChildren: () => import('./pages/add-restaurant/add-restaurant.module').then(m => m.AddRestaurantModule) },
      { path: 'contact', loadChildren: () => import('./pages/contact/contact.module').then(m => m.ContactModule) },
      { path: ':name/reservation', loadChildren: () => import('./pages/make-a-reservation/make-a-reservation.module').then(m => m.MakeReservationModule) },
      { path: 'account', component: AccountComponent, canActivateChild: [AuthGuard], canActivate: [AuthGuard], loadChildren: () => import('./pages/account/account.module').then(m => m.AccountModule) },
      { path: ':id/complaint', canActivateChild: [AuthGuard], canActivate: [AuthGuard], loadChildren: () => import('./pages/complaint/complaint.module').then(m => m.ComplaintModule) },
    ],
  },
  {
    path: '',
    component: ShellComponent,
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    children: [
      // { path: 'auth', loadChildren: () => import('./pages/auth/auth.module').then((m) => m.AuthModule), },
    ],
  },
  { path: '**', pathMatch: 'full', redirectTo: '/home' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { onSameUrlNavigation: 'reload', scrollOffset: [0, 0], scrollPositionRestoration: 'top' })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
