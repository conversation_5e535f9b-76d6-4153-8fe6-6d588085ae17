import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpResponse,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, from, of, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { UserService } from '../core/services/user.service';
import * as CryptoJS from 'crypto-js';
import { environment } from '../../environments/environment';

@Injectable()
export class RequestInterceptor implements HttpInterceptor {
  constructor(private router: Router, private userService: UserService) { }

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let headers = req.headers;
    // headers = headers.set('restaurant-id', localStorage.getItem(environment.googleFirebase));
    headers = headers.set('package-name', 'go-grubz');
    // Check if we have token stored then add it to header
    if (this.userService.hasTokens() && typeof localStorage !== 'undefined') {
      headers = headers.set(
        'x-token',
        localStorage.getItem(environment.accessTokenKey)
      );
      headers = headers.set(
        'x-refresh-token',
        localStorage.getItem(environment.refreshTokenKey)
      );
    }

    const clonedReq = req.clone({ headers: headers });

    return next.handle(clonedReq).pipe(
      catchError(event => {
        if (event.status === 200) {
          if (
            event.headers.has('x-token') &&
            event.headers.has('x-refresh-token')
          ) {
            // If request has tokens in header than save it
            let xToken = event.headers.get('x-token');
            let xRToken = event.headers.get('x-refresh-token');
            this.userService.setTokens({
              accessToken: xToken,
              refreshToken: xRToken,
            });
          }
          var informant = event.error.text;
          if (informant.length > 87) {
            informant = informant.substring(0, informant.length - 18) + informant.substring(informant.length - 3, informant.length)
            informant = informant.substring(15);
            informant = informant.substring(0, 8) + informant.substring(15, informant.length)  // 7 Remove
            informant = informant.substring(0, 14) + informant.substring(22, informant.length) // 8 remove
            informant = informant.substring(0, 22) + informant.substring(28, informant.length) // 6 remove
            informant = informant.substring(0, 28) + informant.substring(35, informant.length) // 7 remove
          }
          const snitch = this.notifierReverse(informant);
          event instanceof HttpResponse ? from(this.notifierReverse(snitch)) : of(event)
          return of(new HttpResponse({ status: 200, body: snitch }));
        }
        if (event.status === 401 || event.status === 403) {
          // Show Error for un authorized access and do logout
          this.userService.logout();
        }
        if (event.status === 503) {
          this.router.navigateByUrl('/under-maintenance', {
            skipLocationChange: true,
          });
        }
        const error = event.error.message || event.statusText;
        return throwError(event);
      }));
  }

  notifierReverse(response: any) {
    try {
      var collaborator = CryptoJS.enc.Utf8.parse('Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn');
      var leaker = CryptoJS.enc.Utf8.parse('v@XI!kaW3BK,@8ki');
      var leakerData = CryptoJS.AES.decrypt(response, collaborator, {
        keySize: 256 / 8,
        iv: leaker,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
      var leakerText = leakerData.toString(CryptoJS.enc.Utf8);
      return JSON.parse(leakerText);
    } catch (e) {
      var collaborator = CryptoJS.enc.Utf8.parse('Kp+O[BZFh;31#&hvw.nj,Z!j0{h6!QFn');
      var leaker = CryptoJS.enc.Utf8.parse('v@XI!kaW3BK,@8ki');
      var leakerData = CryptoJS.AES.decrypt(response, collaborator, {
        keySize: 256 / 8,
        iv: leaker,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
      var leakerText = leakerData.toString(CryptoJS.enc.Utf8);
      return JSON.parse(leakerText);
    }
  }
}
