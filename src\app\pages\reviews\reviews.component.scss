$primary: #FC353A;

:root {
  --primary: #FC353A;
}
::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}
.review-response {
  padding: 15px 10px 5px 20px;
  margin-top: 15px;
  background-color: #f4f3f3;
  border-radius: 0 20px 20px 0;
  border-left: 8px solid $primary;
}

.review-response h6 {
  font-size: 20px;
  margin-bottom: 5px;
}

.review-response h6 span {
  font-size: 14px;
  font-weight: 600;
  color: #b5b5b5;
  padding-left: 5px;
}
.review-response p {
  font-size: 16px;
  margin-bottom: 12px;
  line-height: 24px;
}
.product-main-box-section .container{
    display: block;
}
.view-menu-btn {
  font-family: 'Visby CF';
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  width: 185px;
  height: 42px;
  line-height: 38px;
  text-align: center;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
  border: 2px solid #fff;
  transition: all 0.5s;
  box-shadow: 0px 3px 6px 2.25px #00000040;
  background-color: #fff;
  position: absolute;
  right: 40px;
  bottom: 25px;
}

.view-menu-btn:hover {
  color: #ea3323;
  border-color: #ea3323;
}


.filter-list {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
}

.filter-list li {
  margin-right: 10px;
}

.filter-list li:last-child {
  margin-right: 0;
}

.filter-list li button {
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  cursor: pointer;
  padding: 9px 18px;
  border-radius: 35px;
  line-height: 20px;
  text-decoration: none;
  display: inline-block;
  background-color: #F9F9F9;
  border: 1px solid #F4F3F3;
}

.filter-list li button img {
  width: 23px;
  margin-right: 6px;
  margin-top: -4px;
}

.filter-list li button.active {
  color: #fff !important;
  background-color: #000;
}
.filter-list li button::after{
  display: none;
}
.filter-list li button svg.fa-sterling-sign {
  font-size: 23px;
  margin-right: 6px;
}

.filter-list li button img.white-icon {
  display: none;
}

.filter-list li button.active img.white-icon {
  display: inline-block;
}

.filter-list li button.active img.black-icon {
  display: none;
}

.filter-list li button svg.fa-chevron-down {
  font-size: 19px;
  margin-left: 6px;
  transition: all 0.5s;
}
.filter-list li.show button svg.fa-chevron-down{
  transform:rotate(180deg);
}

.filter-list .filter-box button.filter-btn {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  margin-left: 0;
}

.filter-list .filter-box button.filter-btn img {
  width: 34px;
  padding: 2px;
  margin-left: 5px;
}
.reviews-rating-box ul.reviews-list button{
  font-size: 25px;
  color: #fc353a;
  padding:0;
  border:0;
  background-color: transparent;  
}
.rating-box .rating-star li button{
  padding:0;
  color:#000;
  background-color: transparent;
  border:0;
}

.dietary-dropdown .dropdown-menu .star-icons {
  line-height: 26px;
  padding-left: 10px;
}

.dietary-dropdown .dropdown-menu .star-icons li {
  display: inline-block;
  margin-right: 4px;
}

.dietary-dropdown .dropdown-menu .star-icons li svg {
  font-size: 15px;
  color: #fd811f;
}

.filter-rating-list {
  position: relative;
  display: flex;
  align-items: center;
  padding-right: 12px;
  margin-bottom: 20px;
  cursor: pointer;
  font-size: 13px;
  color: #000;
  padding: 5px 20px;
  background-color: #f4f3f3;
  border-color: #f4f3f3;
  width: fit-content;
  border-radius: 50px;
  border: 2px solid #fff;
  font-weight: 700;
}
.reviews-rating-box ul li.active{
  background-color: $primary;
  border-color: $primary;
  color: #fff;
  .star-icons{
    li svg {
      color: #fff;
    }
  }
}

.reviews-rating-box .filter-list{
  justify-content: normal !important;
}

.filter-rating-list ul.star-icons {
  margin-bottom: 0;
}

.filter-rating-list ul.star-icons li {
  display: inline-block;
  margin-right: 3px;
}

.filter-rating-list ul.star-icons li span {
  font-size: 20px;
  font-weight: 700;
  color: #000;
  position: relative;
  top: 2px;
}

.filter-rating-list ul.star-icons li svg {
  font-size: 16px;
  color: #fd811f;
}


@media screen and (max-width:1500px) {  
.product-main-box-section{
  padding-bottom:50px;
}  
.review-response{
    padding: 10px 10px 5px 15px;
    border-radius: 0 15px 15px 0;
    border-left: 6px solid #fc353a;
}
.review-response h6 {
  font-size: 16px;
  margin-bottom: 0px;
}
.review-response h6 span {
  font-size: 12px;
  padding-left: 2px;
}
.review-response p {
  font-size: 14px;
  margin-bottom: 10px;
  line-height: 20px;
}
.view-menu-btn {
  font-size: 14px;
  width: 165px;
  height: 38px;
  line-height: 34px;
  right: 40px;
  bottom: 25px;
}
.filter-list li button {
  font-size: 14px;
  padding: 9px 14px;
}

.filter-list li button img {
  width: 17px;
  margin-right: 5px;
}

.filter-list li button svg.fa-sterling-sign {
  font-size: 17px;
  margin-right: 5px;
  position: relative;
  top: 2px;
}

.filter-list li button svg.fa-chevron-down {
  font-size: 15px;
  margin-left: 5px;
}
.reviews-rating-box ul.reviews-list button{
  font-size: 18px;
}

.dietary-dropdown .dropdown-menu .star-icons li svg {
  font-size: 12px;
}

.dietary-dropdown .dropdown-menu .star-icons {
  padding-left: 8px;
  line-height: 16px;
}

.filter-rating-list ul.star-icons li span {
  font-size: 16px;
}

.filter-rating-list ul.star-icons li {
  margin-right: 3px;
}

.filter-rating-list ul.star-icons li svg {
  font-size: 14px;
}


}

@media screen and (max-width:1199px) {
  .filter-list li {
    margin-right: 10px;
  }

  .filter-list li button img {
    width: 16px;
    margin-right: 4px;
  }

  .filter-list li button svg.fa-chevron-down {
    font-size: 14px;
    margin-left: 4px;
  }
}

  @media screen and (max-width: 991px) {
    .view-menu-btn {
      font-size: 14px;
      width: 150px;
      height: 36px;
      line-height: 32px;
      right: 30px;
      bottom: 20px;
    }
    .filter-list li button {
      white-space: nowrap;
    }  
    .filter-section-bg.fixed .filter-list {
      padding-bottom: 0;
    }

  .dietary-dropdown .dropdown-menu ul.star-icons li {
    width: auto;
  }

  .dietary-dropdown .dropdown-menu .star-icons li svg {
    font-size: 10px;
  }
  .filter-rating-list{
    padding: 5px 15px;
  }

}

@media screen and (max-width:767px) {
  .reviews-rating-box .filter-list{
    flex-wrap: wrap;
  }
  .filter-rating-list{
    width: 47%;
    margin-bottom: 10px;
    padding: 5px 15px;
  }


}

@media screen and (max-width:575px) {    
  .product-main-box-section{
    padding-bottom: 40px;
  }
  .reviews-rating-box {
    padding: 15px 15px 12px 15px;
    border-radius: 10px;
  }

  .rating-list .rating-box {
    padding-top: 10px;
  }

}

@media screen and (max-width:480px) {
.review-response{
    padding: 8px 8px 5px 10px;
    border-radius: 0 10px 10px 0;
    border-left: 5px solid #fc353a;
}
.review-response h6 {
  font-size: 14px;
  line-height: 20px;
}
.review-response h6 span {
  font-size: 11px;
  padding-left: 0px;
  width: 100%;
  display: inline-block;
}
.review-response p {
  font-size: 13px;
  margin-bottom: 5px;
}
.view-menu-btn {
  font-size: 13px;
  width: 130px;
  height: 34px;
  line-height: 30px;
  right: 15px;
  bottom: 15px;
}

}

@media screen and (max-width:425px) {
.filter-list .filter-rating-list{
  width: 100%;
  margin-right:0;
}

}