import { State } from "./state";
import { City } from "./city";

export class Location {
  id: string;
  state_id: string;
  city_id: string;
  area_name: string;
  zip_code: string;

  status: boolean;

  country: City;
  state: State;

  created_at: string;
  updated_at: string;

  static toFormData(location: Location) {
    const formData = new FormData();

    if (location.id) formData.append('id', location.id);
    if (location.state_id) formData.append('state_id', location.state_id);
    if (location.city_id) formData.append('city_id', location.city_id);
    if (location.area_name) formData.append('area_name', location.area_name);
    if (location.zip_code) formData.append('zip_code', location.zip_code);
    formData.append('status', location.status ? '1' : '0');

    return formData;
  }
}
