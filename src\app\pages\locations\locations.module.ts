import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { LocationsComponent } from './locations.component';
import { GoogleMapsModule } from '@angular/google-maps';

const routes: Routes = [
  { path: '', component: LocationsComponent },
];
@NgModule({
  imports: [
    GoogleMapsModule,
    RouterModule.forChild(routes),
    SharedModule,
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule
  ],
  declarations: [LocationsComponent],
  exports: [LocationsComponent]
})
export class LocationModule { }
