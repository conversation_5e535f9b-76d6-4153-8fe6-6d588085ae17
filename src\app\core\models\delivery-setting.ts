
export class DeliverySetting {
  id: string;
  restaurant_id: string;
  delivery_miles: string;
  delivery_charge: string;
  driver_chargehint: string = 'A';
  radius_color: string= '#00FF00';
  map_type: string= 'Radius';

  static toFormData(delivery_setting: DeliverySetting) {
    const formData = new FormData();

    if (delivery_setting.id) formData.append('id', delivery_setting.id);
    if (delivery_setting.restaurant_id) formData.append('restaurant_id', delivery_setting.restaurant_id);
    if (delivery_setting.delivery_miles) formData.append('delivery_miles', delivery_setting.delivery_miles);
    formData.append('delivery_charge', delivery_setting.delivery_charge);
    if (delivery_setting.driver_chargehint) formData.append('driver_chargehint', delivery_setting.driver_chargehint);
    if (delivery_setting.radius_color) formData.append('radius_color', delivery_setting.radius_color);
    if (delivery_setting.map_type) formData.append('map_type', delivery_setting.map_type);
   
    return formData;
  }
}
