<section class="get-in-touch-bg">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="get-in-touch-content">
                    <h2>Get in touch</h2>
                    <p>
                        For help with your order or to explore partnership<br>
                        opportunities with Go Grubz, feel free to contact us<br>
                        using the links or contact form below. We're<br>
                        here to help you enhance your restaurant's online<br>
                        presence and streamline your ordering experience.
                    </p>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="get-in-touch-image">
                    <img src="assets/images/get-in-touch-image.png" alt="Go-Grubz-Get-in-touch-image" loading="lazy">
                </div>
            </div>
        </div>
    </div>
</section>

<section class="customer-support-bg">
    <div class="container">
        <div class="row">
            <div class="col-lg-7">
                <div class="customer-support-content">
                    <h4>Go Grubz Customer Support</h4>
                    <p>
                        Need help with an order, your account or other<br>
                        general enquiries? Contact Go Grubz support at<br>
                        for quick assistance and a delightful ordering<br>
                        experience!
                    </p>
                    <a href="https://api.whatsapp.com/send?phone=************&text=Hello" class="btn cusror"
                        target="_blank">
                        Customer Support
                    </a>
                </div>
            </div>
            <div class="col-lg-5">
                <div class="get-in-touch-image">
                    <img src="assets/images/customer-support-image.png" alt="Go-Grubz-customer-support-image"
                        loading="lazy">
                </div>
            </div>
        </div>
    </div>
</section>

<section class="merchant-partner-bg">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 order-2 order-lg-1">
                <div class="merchant-partner-image">
                    <img src="assets/images/merchant-partner-image.png" alt="Go-Grubz-partner-support-image"
                        loading="lazy">
                </div>
            </div>
            <div class="col-lg-6 order-1 order-lg-2">
                <div class="merchant-partner-content">
                    <h4>Merchant Partner Support</h4>
                    <p>
                        For our valued merchant partners already on board
                        with Go Grubz, our dedicated account support is here
                        to assist you every step of the way. Whether you have
                        questions about onboarding, need guidance, or want
                        to explore additional features, our team is committed
                        to providing comprehensive support to ensure your
                        experience with Go Grubz is seamless and successful.
                    </p>
                    <a href="https://api.whatsapp.com/send?phone=************&text=Hello" class="btn cusror"
                        target="_blank">
                        Partner Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="contact-form-bg">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="main-heading text-center">
                    <h4>Contact Form</h4>
                </div>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-md-12 col-xl-8 col-xxl-7">
                <div class="contact-form-box">
                    <form nz-form #signupForm="ngForm" (ngSubmit)="onSubmit(signupForm)" nzLayout="vertical">
                        <div class="row justify-content-between">

                            <div class="col-lg-6 pe-lg-4">
                                <div class="form-group">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback
                                            nzErrorTip="Your first name can only contain letters, hyphens or apostrophes!">
                                            <nz-input-group>
                                                <input class="form-control" type="text" nz-input
                                                    (keydown)="onSpaceKeyFirstDown($event)"
                                                    [(ngModel)]="contact.first_name" required name="name"
                                                    id="first-name" placeholder="First Name">
                                            </nz-input-group>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <div class="col-lg-6 ps-lg-4">
                                <div class="form-group">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback
                                            nzErrorTip="Your last name can only contain letters, hyphens or apostrophes!">
                                            <nz-input-group>
                                                <input class="form-control" type="text" nz-input
                                                    (keydown)="onSpaceKeyFirstDown($event)"
                                                    [(ngModel)]="contact.last_name" required name="last-name"
                                                    id="last-name" placeholder="Last Name">
                                            </nz-input-group>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback [nzErrorTip]="emailErrorTpl">
                                            <nz-input-group>
                                                <input class="form-control" type="email" nz-input email="true"
                                                    pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$"
                                                    #contactEmail="ngModel" (keydown.space)="onSpaceKeyDown($event)"
                                                    [(ngModel)]="contact.email" required name="email-address"
                                                    id="email-address" placeholder="Email Address">
                                            </nz-input-group>
                                            <ng-template #emailErrorTpl let-control>
                                                <ng-container *ngIf="control.hasError('required')">
                                                    Please enter your email!
                                                </ng-container>
                                                <ng-container
                                                    *ngIf="control.hasError('email') || (!control.hasError('required') && contactEmail.touched) || (!control.hasError('required') && !contactEmail.valid)">
                                                    Email must be a valid email address
                                                </ng-container>
                                            </ng-template>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback nzErrorTip="Please enter phone number!">
                                            <nz-input-group>
                                                <input class="form-control" type="text" inputmode="numeric" nz-input
                                                    (keydown.space)="onSpaceKeyDown($event)" [(ngModel)]="contact.phone"
                                                    (keypress)="validateMobile($event)" required name="phone-number"
                                                    id="phone-number" placeholder="Phone Number">
                                            </nz-input-group>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group select-box">
                                    <select [(ngModel)]="contact.department" required class="form-control"
                                        name="department" id="department">
                                        <option value="" disabled selected>Select Department</option>
                                        <option value="sales" selected>Sales</option>
                                        <option value="support">Support</option>
                                        <option value="complain">Complain</option>
                                        <option value="feedback">Feedback</option>
                                        <option value="marketing">Marketing</option>
                                        <option value="others">Others</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback nzErrorTip="Please enter subject!">
                                            <nz-input-group>
                                                <input class="form-control" type="text" nz-input
                                                    [(ngModel)]="contact.subject" required name="subject" id="subject"
                                                    placeholder="Subject">
                                            </nz-input-group>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <div class="form-group message-box">
                                    <nz-form-item>
                                        <nz-form-control nzHasFeedback nzErrorTip="Please enter message!">
                                            <nz-input-group>
                                                <textarea class="form-control" nz-input [(ngModel)]="contact.message"
                                                    required name="message" id="message" name="message"
                                                    placeholder="Enter your message here...."></textarea>
                                            </nz-input-group>
                                        </nz-form-control>
                                    </nz-form-item>
                                </div>
                            </div>

                            <nz-form-item *ngIf="error">
                                <span class="text-danger">{{ error }}</span>
                            </nz-form-item>

                            <div class="col-lg-12 text-center">
                                <button class="btn" nz-button [disabled]="isLoading">
                                    <i class="spinner-border" *ngIf="isLoading"></i>
                                    Submit
                                    <svg class="fa-solid fa-arrow-right"></svg>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- Phone-Number-Verification-Popup -->
<ng-template #otpModal let-modal>
    <div id="number-verification-popup">
        <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
            <div class="modal-body">
                <div class="d-flex justify-content-center">
                    <h6 class="ps-2">
                        Phone Number Verification
                    </h6>
                </div>
                <div class="verify-text">After contact us, we need to verify your Phone Number for security purposes
                </div>
                <div class="form-group">
                    <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                            <nz-input-group>
                                <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                                    (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                                    (keypress)="validateMobile($event)" [(ngModel)]="contact.otp" required
                                    placeholder="Enter 6-digit phone verification code">
                            </nz-input-group>
                            <ng-template #phoneVeriErrorTpl let-control>
                                <ng-container *ngIf="control.hasError('required')">
                                    Please enter phone verification code!
                                </ng-container>
                            </ng-template>
                        </nz-form-control>
                    </nz-form-item>
                </div>

                <div class="text-center">
                    <span class="d-inline-block w-100 pb-1">We sent a code to {{ contact.phone }}.</span>
                </div>

                <ul class="more-option">
                    <li><a class="d-flex text-nowrap cursor" (click)="resendOtp()"> Resend Code</a></li>
                    <!-- <li><svg class="fa-solid fa-circle"></svg></li>
                    <li><a class="cursor" (click)="openPhoneEdit()"> Update Number</a></li> -->
                </ul>

            </div>
            <div class="modal-footer">
                <nz-form-item *ngIf="Otperror">
                    <span class="text-danger">{{ Otperror }}</span>
                </nz-form-item>

                <button class="btn modal-black-btn" nz-button [disabled]="isOtpLoading">
                    <i class="spinner-border" *ngIf="isOtpLoading"></i>
                    Verify
                </button>
            </div>
        </form>
    </div>
</ng-template>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>