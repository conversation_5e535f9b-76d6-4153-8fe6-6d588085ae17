import { Compo<PERSON>, <PERSON>ement<PERSON><PERSON>, Inject, OnInit, PLATFORM_ID, Renderer2, ViewChild } from '@angular/core';
import { User } from '../core/models/user';
import { UserService } from '../core/services/user.service';
// import { SocialAuthService, GoogleLoginProvider, SocialUser, FacebookLoginProvider } from 'angularx-social-login';
import { CurrencyPipe, DOCUMENT, isPlatformServer } from '@angular/common';
import { NgForm } from '@angular/forms';
import { finalize } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ActivatedRoute, Router } from '@angular/router';
import { CartService } from '../core/services/cart.service';
import { Cart } from '../core/models/cart';
import { Restaurant } from '../core/models/restaurant';
import { RestaurantService } from '../core/services/restaurant.service';
import { Subscription } from 'rxjs';
import { SiteSetting } from '../core/models/site-setting';
import { NotificationService } from '../core/services/notification.service';
import { AppComponent } from '../app.component';
import { SiteSettingService } from '../core/services/site-setting.service';
import { environment } from '../../environments/environment';

@Component({
  selector: 'app-shell',
  host: { ngSkipHydration: 'true' },
  templateUrl: './shell.component.html',
  styleUrls: ['./shell.component.scss']
})
export class ShellComponent implements OnInit {
  subs = new Subscription();

  @ViewChild('textbox1') textbox1!: ElementRef;
  @ViewChild('textbox2') textbox2!: ElementRef;
  @ViewChild('textbox3') textbox3!: ElementRef;
  @ViewChild('textbox4') textbox4!: ElementRef;
  @ViewChild('textbox5') textbox5!: ElementRef;
  @ViewChild('textbox6') textbox6!: ElementRef;

  @ViewChild('closeModal') closeModal;
  @ViewChild('closeSignUpModal') closeSignUpModal;
  @ViewChild('closeForgotUpModal') closeForgotUpModal;
  @ViewChild('profileButton') profileButton: ElementRef;
  @ViewChild('profileMenu') profileMenu: ElementRef;
  @ViewChild('newPassword', { static: true }) newPassword: ElementRef;

  loginUser: User;
  carts: Cart[] = [];
  user: User = new User();
  secondUser: User = new User();
  forgotUser: User = new User();
  restaurant: Restaurant = new Restaurant();
  searchRestaurant: Restaurant = new Restaurant();
  siteSetting: SiteSetting = new SiteSetting();
  emailOtp: string = '';

  userId: string;
  zipcode: string;
  isBrowser: boolean;

  hide: boolean = true;
  chide: boolean = true;
  lhide: boolean = true;

  isLoading = false; error = null;
  visible = false;
  isModelLoading = false; Modelerror = null;
  isSignModelLoading = false; SignModelerror = null;
  isOtpLoading = false; Otperror = null;
  isChangePasswordLoading = false; errorChangePassword = null;
  otpVisible = false;
  searchVisible = false;
  searchData = null;

  isCollapsed = false;
  isProfile = false;

  constructor(
    private siteSettingService: SiteSettingService,
    public userService: UserService,
    public restaurantService: RestaurantService,
    public cartService: CartService,
    public appComponent: AppComponent,
    public route: ActivatedRoute,
    private notificationService: NotificationService,
    // private currencyPipe: CurrencyPipe,
    public router: Router,
    private modalService: NgbModal,
    // private socialAuthService: SocialAuthService,
    @Inject(DOCUMENT) private document: Document,
    private renderer: Renderer2,
  ) {
    this.renderer.listen('window', 'click', (e: Event) => {
      if (e.target !== this.profileButton?.nativeElement && e.target !== this.profileMenu?.nativeElement) {
        this.isProfile = false;
      }
    });
  }

  ngOnInit(): void {
    this.isBrowser = isPlatformServer(PLATFORM_ID);
    this.fetchSiteSetting();
    this.loginUser = JSON.parse(this.userService.getUser());
    this.userService.saveUser(this.loginUser)
    this.userId = this.loginUser?.id
    if (typeof localStorage !== 'undefined') {
      this.zipcode = localStorage.getItem(environment.zipcode);
    }
    this.carts = this.cartService.getCart();
    if (this.carts.length > 0) {
      this.restaurant.id = this.carts[0].restaurant_id;
    } else {
      if (typeof localStorage !== 'undefined') {
        this.restaurant.id = localStorage.getItem(environment.googleFirebase);
      }
    }

    this.route.queryParams.subscribe(params => {
      this.user.referred_by = params['referral'];
    });

    if (this.restaurant.id) {
      this.fetchRestaurants()
    }
  }

  findLocation() {
    // if (navigator.geolocation) {
    //   navigator.geolocation.getCurrentPosition((position) => {
    //     let geocoder = new google.maps.Geocoder();
    //     let latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
    //     let request = { LatLng: latlng };
    //     geocoder.geocode({ location: latlng }, (results, status) => {
    //       if (status === google.maps.GeocoderStatus.OK) {
    //         let result = results[0];
    //         let rsltAdrComponent = result.address_components;
    //         let resultLength = rsltAdrComponent.length;
    //         if (result != null) {
    //           this.restaurant.zipcode = rsltAdrComponent[resultLength - 1].short_name;
    //           this.restaurantService.saveZipCode(this.restaurant.zipcode.trim());
    //           this.router.navigateByUrl('/location/' + this.restaurant.zipcode.trim());
    //         } else {
    //           console.log('No address available!');
    //         }
    //       }
    //     });
    //   });
    // }
  }

  fetchSiteSetting() {
    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
      }, err => this.error = err)
    );
  }

  restaurantFetch(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.restaurantService.saveZipCode(this.searchRestaurant.zipcode.replace(/\s/g, ""));
    this.searchVisible = false;
    window.location.href = "location/" + this.searchRestaurant.zipcode.replace(/\s/g, "");
  }

  formatPostcode(event: any) {
    let input = event.target.value.toUpperCase().replace(/\s+/g, '');
    if (input.length > 3 && input.length <= 7) {
      input = input.slice(0, -3) + ' ' + input.slice(-3);
    }
    this.searchRestaurant.zipcode = input;
  }

  searchResturant(data: string): void {
    // data = data.replace(/\s/g, "");
    data = data.trim();
    this.restaurantService.searchResturant(data);
  }

  clearSearch() {
    this.searchData = null;
    this.restaurantService.searchResturant(this.searchData);
  }

  open(): void {
    this.visible = true;
  }

  close(): void {
    this.visible = false;
  }

  hidePassword() {
    this.hide = !this.hide;
  }

  hideCpassword() {
    this.chide = !this.chide;
  }

  hideLpassword() {
    this.lhide = !this.lhide;
  }

  openProfile() {
    this.isProfile = !this.isProfile;
  }

  dropdownRedirect(redirect) {
    this.isProfile = !this.isProfile;
    this.router.navigateByUrl(redirect);
  }

  fetchRestaurants() {
    this.isLoading = true; this.error = null;

    this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.restaurant = res
        },
        (err) => {
          this.error = err;
        }
      );
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.signup(form)
  }

  signup(form: NgForm) {
    this.isSignModelLoading = true; this.SignModelerror = null;

    this.userService.signup(this.user)
      .pipe(finalize(() => (this.isSignModelLoading = false)))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          this.closeSignUpModal?.nativeElement?.click();
          this.handleCancel();
          this.appComponent.fetchSiteSetting();
          // this.router.navigateByUrl('/');
        },
        (err) => {
          this.SignModelerror = err;
        }
      );
  }

  loginSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.login(form)
  }

  login(form: NgForm) {
    this.isLoading = true; this.error = null;

    this.userService.login(this.secondUser)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.loginUser = res;
          this.userService.saveUser(res);
          this.closeModal.nativeElement.click();
          this.appComponent.fetchSiteSetting();
          // this.router.navigateByUrl('/');
        },
        (err) => {
          this.error = err;
        }
      );
  }

  onSubmitForgot(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.forgotPassword(form);
  }

  forgotPassword(form: NgForm) {
    this.isModelLoading = true; this.Modelerror = null;

    this.subs.add(
      this.userService.forgotpassword(this.forgotUser).
        pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.forgotUser = res;
            this.otpVisible = true;
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz");
          },
          (err) => {
            this.Modelerror = err;
          }
        )
    );
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isOtpLoading = true; this.Otperror = null;

    this.forgotUser.verify_type = 'email';
    this.forgotUser.otp = this.forgotUser.email_otp;

    this.subs.add(
      this.userService.varifyBothOtp(this.forgotUser).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.otpVisible = false;
            this.closeForgotUpModal?.nativeElement?.click();
            this.handleCancel();
            this.modalService.open(this.newPassword, { backdrop: 'static', backdropClass: 'customBackdrop', });
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  serachVisibled() {
    this.searchVisible = !this.searchVisible;
    if (this.searchVisible) {
      this.renderer.addClass(this.document.body, 'open-searchbar');
    } else {
      this.renderer.removeClass(this.document.body, 'open-searchbar');
    }
  }

  handleInput(event: any, index: number = 0) {
    const digit = event.target.value.replace(/\D/g, '');
    this.emailOtp = this.emailOtp.substring(0, index) + digit + this.emailOtp.substring(index + 1);
    this.forgotUser.email_otp = this.emailOtp;

    if (index < 5 && digit.length === 1) {
      let sum = index + 1;
      let nextElementSiblingId = '#' + 'otpInput' + sum;
      // document.getElementById(nextElementSiblingId).focus();
      this.document.querySelector(nextElementSiblingId);
    }
  }

  focusNext(nextTextbox: ElementRef): void {
    nextTextbox.nativeElement.focus();
  }

  onChangePasswordSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.changePassword(form)
  }

  changePassword(form: NgForm) {
    this.isChangePasswordLoading = true; this.errorChangePassword = null;

    this.userService.changepassword(this.forgotUser)
      .pipe(finalize(() => (this.isChangePasswordLoading = false)))
      .subscribe(
        (res) => {
          this.notificationService.showSuccess("Password changed successfully.", "Gogrubz")
          this.modalService.dismissAll();
          this.userService.logout();
          this.forgotUser = new User();
        },
        (err) => {
          this.errorChangePassword = err;
        }
      );
  }

  loginWithFacebook(): void {
    // this.socialAuthService.signIn(FacebookLoginProvider.PROVIDER_ID);
  }

  loginWithGoogle(): void {
    // this.socialAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);
  }

  logout() {
    this.userService.logout();
    this.closeMenu();
    // this.socialAuthService.signOut();
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    // || event.keyCode == 32 
    if (event.keyCode == 189 || event.keyCode == 9 || event.keyCode == 8 || event.keyCode == 48 || (event.keyCode >= 97 && event.keyCode <= 122) || (event.keyCode >= 65 && event.keyCode <= 90)) {
    } else {
      event.preventDefault();
    }
  }

  updateToCart(cart: Cart, index, event: string) {
    this.carts = this.cartService.getCart();
    if (event == 'add') {
      this.carts[index].quantity = this.carts[index].quantity + 1;
      this.carts[index].total_price = this.carts[index].menu_price * this.carts[index].quantity;
      this.carts[index].total_price.toFixed(2);
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;

      if (quantity > 0) {
        cart.quantity = quantity;
        this.carts[index].quantity = quantity;
        this.carts[index].total_price = this.carts[index].menu_price * quantity;
        this.carts[index].total_price.toFixed(2);
      } else {
        this.carts.splice(index, 1);
      }
    }

    if (event == 'delete') {
      this.carts.splice(index, 1);
    }
    this.saveCarts(this.carts);
  }

  saveCarts(carts: Cart[]) {
    this.cartService.saveCart(carts);
    this.carts = this.cartService.getCart();
  }

  showCart() {
    this.renderer.removeClass(this.document.body, 'open-slider');
    const navbar = this.document.querySelector('.navigation-dropdown');
    navbar?.classList.remove('open');
    const navbarTwo = this.document.querySelector('.navigation-dropdown-two');
    navbarTwo?.classList.remove('open');
    this.renderer.addClass(this.document.body, 'open');
  }

  closeCart() {
    this.renderer.removeClass(this.document.body, 'open');
  }

  showMenu() {
    this.renderer.removeClass(this.document.body, 'open');
    this.renderer.addClass(this.document.body, 'open-slider');
    const navbar = this.document.querySelector('.navigation-dropdown');
    navbar?.classList.add('open');
    const navbarTwo = this.document.querySelector('.navigation-dropdown-two');
    navbarTwo?.classList.add('open');
  }

  closeMenu() {
    this.renderer.removeClass(this.document.body, 'open-slider');
    const navbar = this.document.querySelector('.navigation-dropdown');
    navbar?.classList.remove('open');
    const navbarTwo = this.document.querySelector('.navigation-dropdown-two');
    navbarTwo?.classList.remove('open');
  }

  convertNumber(event) {
    var val = parseFloat(event);
    var val1 = (val).toFixed(2);
    // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    return val1
  }

  handleCancel(): void {
    this.isLoading = false;
    this.error = null;
    this.user = new User();
    this.route.queryParams.subscribe(params => {
      this.user.referred_by = params['referral'];
    });
  }
}
