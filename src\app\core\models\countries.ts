
export class Countries {
  id: string;
  country_name: string;
  currency_name: string;
  currency_code: string;
  currency_symbol: string;
  phone_code: string;
  iso_code: string;

  status: boolean;

  created_at: string;
  updated_at: string;

  static toFormData(countries: Countries) {
    const formData = new FormData();

    if (countries.id) formData.append('id', countries.id);
    if (countries.country_name) formData.append('country_name', countries.country_name);
    if (countries.currency_name) formData.append('currency_name', countries.currency_name);
    if (countries.currency_code) formData.append('currency_code', countries.currency_code);
    if (countries.currency_symbol) formData.append('currency_symbol', countries.currency_symbol);
    if (countries.phone_code) formData.append('phone_code', countries.phone_code);
    if (countries.iso_code) formData.append('iso_code', countries.iso_code);
    formData.append('status', countries.status ? '1' : '0');

    return formData;
  }
}
