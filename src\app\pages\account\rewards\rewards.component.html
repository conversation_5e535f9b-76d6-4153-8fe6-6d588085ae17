<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="row" *ngIf="!isLoading">
  <div class="col-sm-12">

    <div class="main-heading pt-2">
      <h6>Reward Points <span class="reward-point-validity">Validity Up to {{rewardValidity}} Days</span></h6>
    </div>

    <div class="successful-invite-friends-list">     
      <div class="table-responsive">
        <table class="table" *ngIf="rewards.length > 0">
          <thead>
            <tr>
              <th>id</th>
              <th>Order id</th>
              <th>Restaurant Name</th>
              <th>Total</th>
              <th>Point</th>
              <th>Type</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of rewards">
              <td>{{item.id}}</td>
              <td>{{item.order_id}}</td>
              <td>{{item.restaurant_name}}</td>
              <td>{{item.total}}</td>
              <td>
                {{(item.type == 'Spent'?item.reward_totalpoint:item.points)}}
                <span *ngIf="item.reward_offer">({{convertNumber(item.reward_offer)}})</span>
              </td>
              <td>{{item.type}}</td>
              <td>{{convertToDate(item.created)}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <app-pagination *ngIf="rewards.length > 0" [total_items]="totalRewards" class="pt1 mt-1"
        [per_page]="options.per_page" [(current_page)]="options.page" (onChange)="loadPage($event)">
      </app-pagination>
    </div>
  </div>
</div>