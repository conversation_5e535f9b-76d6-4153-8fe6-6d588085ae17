{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"newGrogrubz": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/new-grogrubz", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/web.config", "src/robots.txt", "src/sitemap.xml"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/slick-carousel/slick/slick.scss", "./node_modules/slick-carousel/slick/slick-theme.scss", "./node_modules/ngx-toastr/toastr.css", "src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.js", "./node_modules/jquery/dist/jquery.min.js", "./node_modules/slick-carousel/slick/slick.min.js", "./node_modules/bootstrap/dist/js/bootstrap.js", "./node_modules/@popperjs/core/dist/umd/popper.min.js"], "server": "src/main.server.ts", "prerender": false, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "30kb", "maximumError": "50kb"}], "outputHashing": "all"}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "newGrogrubz:build:production"}, "development": {"buildTarget": "newGrogrubz:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "newGrogrubz:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/web.config", "src/robots.txt", "src/sitemap.xml"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/slick-carousel/slick/slick.scss", "./node_modules/slick-carousel/slick/slick-theme.scss", "./node_modules/ngx-toastr/toastr.css", "src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.js", "./node_modules/jquery/dist/jquery.min.js", "./node_modules/slick-carousel/slick/slick.min.js", "./node_modules/bootstrap/dist/js/bootstrap.js", "./node_modules/@popperjs/core/dist/umd/popper.min.js"]}}}}}}