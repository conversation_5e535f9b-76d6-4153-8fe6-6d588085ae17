import { Component, HostListener, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { Restaurant } from '../../core/models/restaurant';
import { NgForm } from '@angular/forms';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { environment } from '../../../environments/environment';
import { CityService } from '../../core/services/city.service';
import { City } from '../../core/models/city';

@Component({
  selector: 'app-about',
  host: { ngSkipHydration: 'true' },
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss']
})

export class AboutComponent implements OnInit {
  private subs = new Subscription();

  cities: City[] = [];
  restaurant: Restaurant = new Restaurant();

  isLoading = false; error = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private restaurantService: RestaurantService,
    public userService: UserService,
    private citiesService: CityService,
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() {
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); }
}
