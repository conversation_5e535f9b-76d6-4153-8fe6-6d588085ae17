<div class="inner-page">
    <div class="container loader-height" *ngIf="isLoading">
        <div class="grubz-loader">
            <div class="set-one">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
            <div class="set-two">
                <div class="circle"></div>
                <div class="circle"></div>
            </div>
        </div>
    </div>

    <div class="container loader-height" *ngIf="!isLoading">

        <div class="order-history-box">
            <div class="order-image">
                <div class="order-logo">
                    <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
                        onerror="this.src='assets/favicon.png';">
                </div>
                <img class="order-main-image" [src]="promotions?.image_url" [alt]="restaurant?.restaurant_name"
                    onerror="this.src='./assets/images/product-main-image.png';">
            </div>
            <div class="order-details">
                <div class="order-title">
                    <h6>{{ restaurant.restaurant_name }}</h6>
                    <ul class="order-date-time">
                        <li>{{order.delivery_date}}</li>
                        <li>{{order.delivery_time}}</li>
                        <li>{{ order.cart_view?.length }} Items For Total {{ convertNumber(order.order_grand_total +
                            order.charity_amount) }}</li>
                    </ul>
                </div>
                <div class="reservation-id">
                    <p><strong>Order ID</strong> : #{{ order.order_number }}</p>
                </div>
                <div class="order-addon-list">
                    <ul>
                        <li *ngFor=" let cart of order.cart_view; let i=index">
                            <div class="order-count">{{cart.quantity}}</div>
                            <span class="multiple-symbol">x</span>
                            <div class="addon-order-detail">
                                <p>{{cart.menu_name}}</p>
                                <ul>
                                    <ul *ngIf="cart.subaddons_name">
                                        <li>{{cart.subaddons_name}}</li>
                                    </ul>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="view-store">
                <ul>
                    <!-- <li>
                        <a class="btn">View Store</a>
                    </li> -->
                    <li *ngIf="!close">
                        <a class="btn" (click)="closeComplaint()">Close Complaint</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row justify-content-center mb-5">
            <div class="col-lg-12">
                <div class="chat">
                    <div class="messages-chat" #scrollMe [scrollTop]="scrollMe.scrollHeight">
                        <div *ngFor=" let complaint of complaints; let i=index">
                            <div class="message" *ngIf="complaint.admin">
                                <div class="photo">
                                    <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
                                        onerror="this.src='assets/favicon.png';">
                                    <div class="online"></div>
                                </div>
                                <div class="user-response">
                                    <p class="text"> {{ complaint.message }} </p>
                                    <p class="time">{{ convertToDate(complaint.created) }}</p>
                                </div>
                            </div>

                            <div class="message" *ngIf="!complaint.admin">
                                <div class="response">
                                    <p class="text"> {{ complaint.message }}.</p>
                                    <p class="response-time time">{{ convertToDate(complaint.created) }}</p>
                                </div>
                                <div class="photo">
                                    <img [src]="user?.image_url" [alt]="restaurant?.restaurant_name"
                                        onerror="this.src='assets/images/user.png';">
                                </div>
                            </div>
                        </div>
                    </div>
                    <form nz-form #complaintForm="ngForm" (ngSubmit)="onSubmit(complaintForm)"
                        *ngIf="complaint && !close">
                        <div class="footer-chat">

                            <nz-form-item *ngIf="errorComplaint">
                                <span class="text-danger">{{ errorComplaint }}</span>
                            </nz-form-item>

                            <nz-form-item>
                                <nz-form-control nzHasFeedback>
                                    <nz-input-group>
                                        <input type="text" class="write-message" nz-input
                                            [(ngModel)]="complaint.message" id="message" name="message"
                                            placeholder="Please enter your message here..." required>
                                    </nz-input-group>
                                </nz-form-control>
                            </nz-form-item>
                            <button class="btn" nz-button>
                                <i class="spinner-border" *ngIf="isComplaintLoading"></i>
                                Send
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-DMXH9C9D17');
</script>