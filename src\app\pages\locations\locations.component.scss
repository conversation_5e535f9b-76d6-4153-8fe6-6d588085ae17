$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

/* -----Location-Banner------*/

.restaurant-location-section {
  padding-top: 142px;
}

.restaurant-search-banner {
  position: relative;
  margin-bottom: 60px;
}

.search-restaurant-title {
  width: 100%;
  position: absolute;
  top: 135px;
  text-align: center;
  font-family: 'Fredoka One';
  font-size: 60px;
  color: #fff;
  font-weight: 400;
  line-height: 73px;
}

.restaurant-search-banner .enter-postcode-address {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 130px;
  margin: auto;
  margin-bottom: 0;
}

.restaurant-search-banner img.restaurant-banner-image {
  width: 100%;
  height: 700px;
  object-fit: cover;
  border-radius: 40px;
}

/* -----Restaurants-Near-Me-CSS------*/
.restaurants-bg {
  position: relative;
  overflow-x: hidden;
}

.restaurants-bg img {
  max-width: 100%;
  width: 100%;
}

.restaurants-near-me {
  position: absolute;
  padding: 28px 35px;
  top: 60px;
  left: 100px;
  background-color: #fff;
  border-radius: 24px;
  z-index: 2;
}

.restaurants-near-me h3 {
  line-height: 44px;
  color: $primary;
  margin-bottom: 0;
}

/* -----View-all-cities-CSS------*/
.view-all-cities-bg {
  padding: 65px 70px 55px 70px;
}

.view-all-cities-bg .main-heading {
  margin-bottom: 92px;
}

.cities-box ul {
  width: 100%;
  margin-bottom: 35px;
}

.cities-box ul li {
  width: 100%;
  display: inline-block;
  margin-bottom: 0px;
}

.cities-box ul li span{
  font-size: 25px;
  color: #101A24;
  font-weight: 600;
  text-decoration: none;
  overflow: hidden;
  word-break:break-word;
  display: inline-block;
  cursor: pointer;
  position: relative;
}
.cities-box ul li span::before{
  position: absolute;
  content:'';
  width:0;
  height:2px;  
  background-color:#101a24;
  margin: auto;
  left:0;
  right:0;
  bottom:0;
  transition: all 0.5s;  
}
.cities-box ul li span:hover:before{
  width:100%;
}
.city-character-count {
  margin-bottom: 40px;
}

.city-character-count span {
  font-size: 28px;
  color: #fff;
  font-weight: 700;
  width: 60px;
  height: 55px;
  display: inline-block;
  line-height: 46px;
  text-align: center;
  background-color: #fc353a;
  border: 3px dashed #ffffff;
}

.all-cities-list {
  padding: 0 15px;
  border-radius: 50px;
  box-shadow: 0 0 20px 8px #0000001a;
  margin-bottom: 40px;
}

.all-cities-list ul {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  overflow-x: auto;
  margin-bottom: 0;
  padding: 10px 0;
}

.all-cities-list ul::-webkit-scrollbar {
  height: 5px;
}

.all-cities-list ul::-webkit-scrollbar-track {
  background: #ffe6e7;
  border-radius: 10px;
}

.all-cities-list ul::-webkit-scrollbar-thumb {
  background: $primary;
  border-radius: 10px;
}

.all-cities-list ul::-webkit-scrollbar-thumb:hover {
  background: $primary;
}

.all-cities-list ul li button {
  font-size: 22px;
  color: #202020;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  border-radius: 50%;
  display: inline-block;
  background-color: transparent;
  width: 42px;
  height: 42px;
  line-height: 40px;
  border:0;
  transition: all 0.5s;
  display: inline-block;
}

.all-cities-list ul li button:hover,
.all-cities-list ul li button.active {
  color: #fff;
  background-color: #fc353a;
}

@media screen and (max-width:1750px) {
  .restaurant-search-banner img.restaurant-banner-image {
    height: 600px;
  }

  .search-restaurant-title {
    top: 100px;
  }

  .restaurant-search-banner .enter-postcode-address {
    bottom: 100px;
  }
  
  .view-all-cities-bg {
    padding: 60px 120px 50px 120px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 72px;
  }

  .cities-box ul {
    margin-bottom: 30px;
  }

  .cities-box ul li span{
    font-size: 24px;
  }

}

@media screen and (max-width:1500px) {
  .restaurant-location-section {
    padding-top: 105px;
  }

  .restaurant-search-banner {
    margin-bottom: 45px;
  }

  .restaurant-search-banner img.restaurant-banner-image {
    height: 525px;
    border-radius: 30px;
  }

  .restaurants-near-me {
    padding: 20px 28px;
    border-radius: 18px;
  }

  .restaurants-near-me h3 {
    font-size: 36px;
    line-height: 34px;  
  }

  .view-all-cities-bg {
    padding: 65px 55px 15px 55px;
  }

  .cities-box ul li span{
    font-size: 16px;
  }

  .city-character-count {
    margin-bottom: 30px;
  }

  .city-character-count span {
    font-size: 24px;
    width: 56px;
    height: 50px;
    line-height: 42px;
  }

  .all-cities-list {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 35px;
  }

  .all-cities-list ul {
    padding: 8px 0;
  }
  .all-cities-list ul li button,
  .all-cities-list ul li a {
    font-size: 18px;
    width: 36px;
    height: 36px;
    line-height: 36px;
  }

}

@media screen and (max-width:1300px) {
  .view-all-cities-bg {
    padding:50px 50px 10px;
  }

}

@media screen and (max-width:1199px) {
  .restaurant-search-banner img.restaurant-banner-image {
    height: 500px;
  }

  .search-restaurant-title {
    font-size: 55px;
    top: 80px;
    padding: 0 30px;
  }

  .restaurant-search-banner .enter-postcode-address {
    bottom: 80px;
  }

  .restaurants-near-me {    
    top: 50px;
    left: 80px;
  }

  .restaurants-near-me h3 {
    line-height: 36px;
  }

  .all-cities-list {
    padding-left: 8px;
    padding-right: 8px;
  }

  .all-cities-list ul {
    padding: 6px 0;
  }
  .all-cities-list ul li button,
  .all-cities-list ul li a {
    font-size: 18px;
    width: 34px;
    height: 34px;
    line-height: 32px;
  }

  .view-all-cities-bg {
    padding: 50px 30px 10px 30px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 52px;
  }

}

@media screen and (max-width:991px) {
  .restaurant-search-banner img.restaurant-banner-image {
    height: 400px;
  }

  .search-restaurant-title {
    font-size: 45px;
    top: 50px;
    line-height: normal;
  }

  .restaurant-search-banner .enter-postcode-address {
    text-align: center;
    bottom: 60px;
  }

  .restaurants-bg img {
    min-height: 350px;
    border-radius: 30px;
    object-fit: cover;
  }

  .restaurants-near-me {    
    top: 50px;
    left: 75px;
  }

  .view-all-cities-bg {
    padding: 50px 20px 20px 20px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 50px;
  }

  .cities-box {
    margin-bottom: 40px;
  }

}

@media screen and (max-width:767px) {
  .restaurant-location-section {
    padding-top: 90px;
  }

  .restaurant-search-banner {
    margin-bottom: 40px;
  }

  .restaurant-search-banner img.restaurant-banner-image {
    height: 320px;
    border-radius: 20px;
  }

  .search-restaurant-title {
    font-size: 40px;
    top: 40px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .restaurant-search-banner .enter-postcode-address {
    bottom: 40px;
  }
  
  .restaurants-near-me{
    top: 40px;
    left: 55px;
  }
  .view-all-cities-bg{
    padding-top: 40px;
  }
  .view-all-cities-bg .main-heading {
    margin-bottom: 40px;
  }  

}

@media screen and (max-width:575px) {
  .search-restaurant-title {
    font-size: 36px;
    top: 35px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .restaurant-search-banner {
    margin-bottom: 35px;
  }

  .restaurant-search-banner .enter-postcode-address {
    max-width: 400px;
    bottom: 35px;
  }

  .all-cities-list {
    padding-left: 6px;
    padding-right: 6px;
  }

  .all-cities-list ul {
    padding: 5px 0;
  }
  .all-cities-list ul li button,
  .all-cities-list ul li a {
    font-size: 14px;
    width: 30px;
    height: 30px;
    line-height: 30px;
  }  

  .city-character-count {
    text-align: center;
  }

  .city-character-count span {
    font-size: 20px;
    width: 50px;
    height: 44px;
    line-height: 34px;
  }

}

@media screen and (max-width:480px) {
  .restaurant-search-banner img.restaurant-banner-image {
    height: 250px;
  }

  .search-restaurant-title {
    font-size: 32px;
    top: 20px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .restaurant-search-banner .enter-postcode-address {
    max-width: 90%;
    bottom: 25px;
  }

  .restaurants-bg img {
    min-height: 320px;
  }
  
  .restaurants-near-me {
    padding: 15px 20px;
    border-radius: 15px;
    top: 20px;
    left: 20px;
  }

  .restaurants-near-me h3 {
    font-size: 35px;
    line-height: 42px;
  }

  .view-all-cities-bg {
    padding: 40px 10px 20px 10px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 30px;
  }

  .cities-box {
    margin-bottom: 35px;
  }

}


@media screen and (max-width:400px) {
  .restaurant-location-section {
    padding-top: 75px;
  }
}
