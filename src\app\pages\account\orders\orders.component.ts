import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, formatDate, ViewportScroller } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, HostListener, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { environment } from '../../../../environments/environment';
import { NotificationService } from '../../../core/services/notification.service';
import { User } from '../../../core/models/user';
import { OrderService } from '../../../core/services/order.service';
import { Order } from '../../../core/models/order';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { Restaurant } from '../../../core/models/restaurant';
import { Review } from '../../../core/models/review';
import { ReviewService } from '../../../core/services/review.service';
import { CartService } from '../../../core/services/cart.service';
import { NgForm } from '@angular/forms';
import { Cart } from '../../../core/models/cart';

@Component({
  selector: 'app-orders',
  host: { ngSkipHydration: 'true' },
  templateUrl: './orders.component.html',
  styleUrls: ['./orders.component.scss'],
})
export class OrdersComponent implements OnInit, OnDestroy {
  @ViewChild('target') target: any;

  subs = new Subscription();

  user: User;
  orders: Order[] = [];
  carts: Cart[] = [];
  restaurant: Restaurant = new Restaurant();
  reOrder: Order = new Order();
  reviewAdd: Review;
  modalOptions: NgbModalOptions;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isReOrderLoading = false; ReOrdererror = null;
  isScrollLoading = false;
  scrolling: boolean = false;

  currentRate = 2;
  totalOrders = 0;
  last_page = 0;
  previousPage: any;
  page = 1;
  per_page = 10;

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    public orderService: OrderService,
    public reviewService: ReviewService,
    private cartService: CartService,
    private router: Router,
    private modalService: NgbModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    this.fetchOrders();
  }

  @HostListener("window:scroll", ["$event"])
  onScroll() {
    const height = this.target.nativeElement.offsetTop;
    if (window.pageYOffset >= height - 200 && !this.scrolling) {
      this.page = this.page + 1;
      this.scrolling = true;
      this.isScrollLoading = true;

      this.subs.add(this.orderService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page })
        .pipe(finalize(() => this.isScrollLoading = false))
        .subscribe(
          (res) => {
            this.orders = this.orders.concat(res.data);
            this.totalOrders = res.total;
            this.scrolling = false;
          }, (err) => { }
        )
      )
    }
  }

  fetchOrders() {
    this.isLoading = true;

    this.subs.add(this.orderService.get({ customer_id: this.user.id, page: this.page, per_page: this.per_page, completed: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.orders = res.data;
          this.totalOrders = res.total;
          this.last_page = res.last_page;
        }, (err) => {
          this.orders = [];
          this.totalOrders = 0;
          this.fetchRestaurant();
        }
      )
    )
  }

  fetchRestaurant() {
    this.isLoading = false; this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  redirectOnRest() {
    this.router.navigateByUrl('/location/' + this.restaurantService.zipcode);
  }

  orderView(orderId) {
    var orderIds = btoa(orderId);
    this.router.navigateByUrl(`/order-details/${orderIds}`);
  }

  reOrderCreate(order) {
    this.isReOrderLoading = true;
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, order.restaurant_id);
    }
    this.reOrder = order;
    this.subs.add(this.orderService.reOrder(order?.id)
      .pipe(finalize(() => this.isReOrderLoading = false))
      .subscribe(
        res => {
          this.cartService.saveCart(res);
          this.router.navigateByUrl('/' + order?.restaurant?.city_name + '/' + order?.restaurant?.seo_url + '/menus');
        }, err => { this.notificationService.showError(err, "Gogrubz") }
      )
    )
  }

  orderStore(order) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, order.restaurant_id);
    }
    this.router.navigateByUrl('/' + order?.restaurant?.city_name + '/' + order?.restaurant?.seo_url + '/menus');
  }

  addReview(model, order: Order) {
    this.reviewAdd = new Review();
    this.reviewAdd.order_id = order.id;
    this.reviewAdd.customer_id = order.customer_id;
    this.reviewAdd.restaurant_id = order.restaurant_id;
    this.reviewAdd.rating = 5;
    this.openModal(model);
  }

  validateReview(form: NgForm) {
    this.isModelLoading = true; this.Modelerror = false;
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      this.isModelLoading = false;
      return;
    }


    this.subs.add(this.reviewService.create(this.reviewAdd)
      .pipe(finalize(() => this.isModelLoading = false))
      .subscribe(
        (res) => {
          this.fetchOrders();
          this.notificationService.showSuccess("Thank's for your feedback", "Gogrubz")
          this.modalService.dismissAll();
        }, (err) => {
          this.Modelerror = err
        }
      )
    )
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.modalService.dismissAll();
  }
}
