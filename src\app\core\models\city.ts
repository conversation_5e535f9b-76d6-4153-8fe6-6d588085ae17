import { Countries } from "./countries";
import { State } from "./state";

export class City {
  id: string;
  country_id: string;
  state_id: string;
  city_name: string;

  status: boolean;

  country: Countries;
  state: State;

  created_at: string;
  updated_at: string;

  static toFormData(city: City) {
    const formData = new FormData();

    if (city.id) formData.append('id', city.id);
    if (city.country_id) formData.append('country_id', city.country_id);
    if (city.state_id) formData.append('state_id', city.state_id);
    if (city.city_name) formData.append('city_name', city.city_name);
    formData.append('status', city.status ? '1' : '0');

    return formData;
  }
}
