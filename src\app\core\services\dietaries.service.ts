import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ErrorHandler } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { Dietary } from '../models/dietaries';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DietaryService {
  private url = `${environment.apiBaseUrl}dietaries/`

  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.status) params = params.set('status', options.status);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Dietary> {
    return this.http.get<Dietary>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(category: Dietary): Observable<Dietary> {
    return this.http.post<Dietary>(this.url, Dietary.toFormData(category))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(category: Dietary): Observable<Dietary> {
    return this.http.post<Dietary>(this.url + category.id, Dietary.toFormData(category))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<Dietary> {
    return this.http.delete<Dietary>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  enable(id: string): Observable<Dietary> {
    return this.http.post<Dietary>(this.url + id + '/enable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  disable(id: string): Observable<Dietary> {
    return this.http.post<Dietary>(this.url + id + '/disable', {})
      .pipe(catchError(ErrorHandler.handleError));
  }

}
