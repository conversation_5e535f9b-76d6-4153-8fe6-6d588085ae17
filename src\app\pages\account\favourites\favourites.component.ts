import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { NgbActiveModal, NgbModal, } from '@ng-bootstrap/ng-bootstrap';
import { User } from '../../../core/models/user';
import { UserService } from '../../../core/services/user.service';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { FavouriteRestaurant } from '../../../core/models/favourite-restaurant';
import { environment } from '../../../../environments/environment';
import { Restaurant } from '../../../core/models/restaurant';

@Component({
  selector: 'app-favourites',
  host: { ngSkipHydration: 'true' },
  templateUrl: './favourites.component.html',
  styleUrls: ['./favourites.component.scss'],
})
export class FavouriteComponent implements OnInit, OnDestroy {
  @ViewChild('confirmationModel', { static: true }) confirmationModel: ElementRef;
  subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  favouriteRestaurant: FavouriteRestaurant = new FavouriteRestaurant();
  favouriteRestaurants: FavouriteRestaurant[] = [];

  confirmed: boolean = false;

  isLoading = false; error = null;

  options = {
    nopaginate: 1,
    customer_id: null,
    is_favourite: '1',
  };

  constructor(
    public userService: UserService,
    public restaurantService: RestaurantService,
    private router: Router,
    // public activeModal: NgbActiveModal,
    private modalService: NgbModal
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    this.fetchFavRestaurants()
  }

  fetchFavRestaurants() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.restaurantService.getFavourite(this.options)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.favouriteRestaurants = res;
      }, err => { this.favouriteRestaurants = []; this.error = err; })
    );
  }

  orderStore(favourite) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem(environment.googleFirebase, favourite.restaurant_id);
    }
    this.router.navigateByUrl('/' + favourite?.restaurant?.city_name + '/' + favourite?.restaurant?.seo_url + '/menus');
  }

  close(confirm: boolean): void {
    this.modalService.dismissAll();
    this.confirmed = confirm;
    if (this.confirmed) {
      this.favourite(this.favouriteRestaurant);
    }
  }

  favourite(favourite) {
    this.favouriteRestaurant = favourite
    if (!this.confirmed) {
      this.modalService.open(this.confirmationModel);
      return;
    }
    this.restaurant.restaurant_id = favourite.restaurant_id;
    this.restaurant.user_id = this.user.id;
    this.restaurant.is_favourite = false;

    this.restaurantService.favourite(this.restaurant)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.fetchFavRestaurants();
        },
        (err) => { }
      );
    this.confirmed = false;
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.modalService.dismissAll();
  }
}
