import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MakeReservationComponent } from './make-a-reservation.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { SharedModule } from '../../shared/shared.module';

const routes: Routes = [
  { path: '', component: MakeReservationComponent },
];
@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    SlickCarouselModule,
    SharedModule
  ],
  declarations: [MakeReservationComponent],
  exports: [MakeReservationComponent]
})
export class MakeReservationModule { }
