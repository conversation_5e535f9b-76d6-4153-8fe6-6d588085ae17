import { Component, HostListener, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-terms-condition',
  host: { ngSkipHydration: 'true' },
  templateUrl: './terms-condition.component.html',
  styleUrls: ['./terms-condition.component.scss']
})

export class TermsConditionComponent implements OnInit {
  private subs = new Subscription();

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() { }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
