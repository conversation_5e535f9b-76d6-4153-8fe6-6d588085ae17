<div class="row">
  <div class="col-md-6">
    <div class="help-support-box">
      <h6>
        Go Grubz Customer Support
      </h6>
      <p>
        Need help with an order, your account or
        other general enquiries? Contact Go Grubz
        support for quick assistance and a
        delightful ordering experience!
      </p>
      <button class="btn" (click)="openCustomerSupportChat()">
        Customer Support
      </button>
    </div>
  </div>
  <div class="col-md-6">
    <div class="help-support-box">
      <h6>
        Merchant Partner Support
      </h6>
      <p>
        For our valued merchant partners already
        on board with Go Grubz, our dedicated
        account support is here to assist you every
        step of the way.
      </p>
      <button class="btn" href="#">
        Partner Support
      </button>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-sm-12">
    <div class="main-heading text-center pt-2">
      <h6>Frequently Asked Questions</h6>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-sm-12">
    <div class="accordion" id="faq-accordion">

      <div class="accordion-item" *ngFor="let faq of faqs;">
        <div class="accordion-header" [id]="'heading'+faq.id">
          <button class="accordion-button" [class.collapsed]="expandableId != faq.id" data-bs-toggle="collapse"
            [data-bs-target]="'#faq-accordion-'+faq.id" (click)="clickOnAccordion(faq.id)">
            {{faq.question}}
          </button>
        </div>
        <div [id]="'faq-accordion-'+faq.id" class="accordion-collapse collapse"
          [class.collapse]="expandableId != faq.id" [aria-labelledby]="'heading'+faq.id"
          data-bs-parent="#faq-accordion">
          <div class="accordion-body">
            <p [innerHTML]="faq.answer"></p>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- Customer Support Chat Box -->
<div class="chat-overlay" [class.active]="isChatOpen" (click)="closeChatBox()"></div>
<div class="chat-container" [class.active]="isChatOpen">
  <!-- Chat Header -->
  <div class="chat-header">
    <div class="header-left">
      <button class="back-btn" (click)="closeChatBox()">
        <i class="fa fa-arrow-left"></i>
      </button>
      <div class="header-title">
        <h4>Chat to us</h4>
      </div>
    </div>
    <div class="header-right">
      <div class="dropdown-container">
        <button class="menu-btn" (click)="toggleDropdown()">
          <i class="fa fa-ellipsis-v"></i>
        </button>
        <div class="dropdown-menu" [class.show]="isDropdownOpen">
          <button class="dropdown-item" (click)="minimizeChat(); closeDropdown()">
            <i class="fa fa-minus"></i>
            <span>Minimise</span>
          </button>
          <button class="dropdown-item" (click)="emailTranscript(); closeDropdown()">
            <i class="fa fa-envelope"></i>
            <span>Email Transcript</span>
          </button>
          <button class="dropdown-item" (click)="sendFeedback(); closeDropdown()">
            <i class="fa fa-comment"></i>
            <span>Send Feedback</span>
          </button>
        </div>
      </div>
      <button class="close-btn" (click)="closeChatBox()">
        <i class="fa fa-times"></i>
      </button>
    </div>
  </div>

  <!-- Chat Body -->
  <div class="chat-body" [class.minimized]="isChatMinimized" #chatBody>
    <!-- Welcome Illustration -->
    <!-- <div class="welcome-section" *ngIf="chatMessages.length <= 1">
      <div class="illustration-container">
        <div class="support-character">
          <div class="character-avatar">
            <span class="avatar-letter">G</span>
          </div>
          <div class="character-illustration">
            <div class="person">
              <div class="head"></div>
              <div class="body"></div>
              <div class="legs">
                <div class="leg"></div>
                <div class="leg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <div class="chat-messages">
      <div class="message-item" *ngFor="let message of chatMessages; let i = index"
        [class.sent]="message.type === 'sent'" [class.received]="message.type === 'received'">
        <div class="message-avatar" *ngIf="message.type === 'received'">
          <span class="avatar-letter">G</span>
        </div>
        <div class="message-content">
          <p>{{ message.text }}</p>
          <span class="message-time">{{ message.timestamp | date:'short' }}</span>
        </div>
        <div class="user-avatar" *ngIf="message.type === 'sent'">
          <img src="assets/images/user.png" alt="User" onerror="this.src='assets/images/user.png'">
        </div>
      </div>

      <!-- Typing indicator -->
      <div class="typing-indicator" *ngIf="isTyping">
        <div class="message-avatar">
          <span class="avatar-letter">G</span>
        </div>
        <div class="typing-content">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <!-- Order Help Flow -->
      <div class="order-help-flow" *ngIf="showOrderHelp">

        <!-- Order Selection -->
        <div class="order-selection" *ngIf="showOrderSelection">
          <!-- Show all orders when none is selected -->
          <div class="orders-list" *ngIf="!selectedOrder">
            <div class="order-card" *ngFor="let order of userOrders">
              <div class="order-content">
                <div class="order-image">
                  <img [src]="order.image" [alt]="order.restaurantName"
                    onerror="this.src='assets/images/offer-image-one.png'">
                </div>
                <div class="order-details">
                  <div class="order-header">
                    <h5>{{order.restaurantName}}</h5>
                  </div>
                  <div class="order-meta">
                    <span class="order-total">£{{order.total}}</span>
                    <span class="order-items-count">• {{order.items.length}} items</span>
                  </div>
                  <div class="order-status-row">
                    <span class="order-status" [class]="'status-' + order.status.toLowerCase().replace(' ', '-')">
                      {{order.status}}
                    </span>
                    <span class="order-date">• {{order.orderDate | date:'d\'th\' MMM yyyy'}}</span>
                  </div>
                  <button class="help-order-btn" (click)="selectOrder(order)">
                    Help with this order
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Show only selected order when one is selected -->
          <div class="orders-list" *ngIf="selectedOrder">
            <div class="order-card">
              <div class="order-content">
                <div class="order-image">
                  <img [src]="selectedOrder.image" [alt]="selectedOrder.restaurantName"
                    onerror="this.src='assets/images/offer-image-one.png'">
                </div>
                <div class="order-details">
                  <div class="order-header">
                    <h5>{{selectedOrder.restaurantName}}</h5>
                  </div>
                  <div class="order-meta">
                    <span class="order-total">£{{selectedOrder.total}}</span>
                    <span class="order-items-count">• {{selectedOrder.items.length}} items</span>
                  </div>
                  <div class="order-status-row">
                    <span class="order-status" [class]="'status-' + selectedOrder.status.toLowerCase().replace(' ', '-')">
                      {{selectedOrder.status}}
                    </span>
                    <span class="order-date">• {{selectedOrder.orderDate | date:'d\'th\' MMM yyyy'}}</span>
                  </div>
                  <button class="help-order-btn selected">
                    Selected ✓
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="see-more-container" *ngIf="!selectedOrder">
            <button class="see-more-btn">
              See More
            </button>
          </div>
        </div>

        <!-- Issue Selection -->
        <div class="issue-selection" *ngIf="showIssueSelection">
          <div class="issue-buttons">
            <button class="issue-btn" *ngFor="let issue of issueTypes" (click)="selectIssue(issue)">
              {{issue}}
            </button>
          </div>
        </div>

        <!-- Detailed Input for Quality/Hygiene Issue -->
        <div class="detailed-input-section" *ngIf="showDetailedInput">
          <div class="input-container">
            <div class="message-input-wrapper">
              <textarea
                [(ngModel)]="detailMessage"
                placeholder="Click here to add your message"
                class="detail-textarea"
                rows="4">
              </textarea>
            </div>

            <div class="file-upload-section">
              <div class="upload-header">
                <span class="upload-icon">+</span>
                <div class="upload-text">
                  <div class="upload-title">Upload any images/videos (Up to 10 files)</div>
                  <div class="upload-subtitle">File type: JPG, PNG, MP4, HEIF (max. size 5 MB)</div>
                </div>
              </div>
              <input
                type="file"
                #fileInput
                (change)="onFileSelected($event)"
                multiple
                accept=".jpg,.jpeg,.png,.mp4,.heif"
                style="display: none;">
              <button class="upload-btn" (click)="fileInput.click()">Choose Files</button>
            </div>

            <div class="selected-files" *ngIf="selectedFiles.length > 0">
              <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                <span class="file-name">{{file.name}}</span>
                <button class="remove-file-btn" (click)="removeFile(i)">×</button>
              </div>
            </div>

            <div class="send-button-container">
              <button
                class="send-detail-btn"
                (click)="sendDetailedMessage()"
                [disabled]="!detailMessage.trim()">
                Send Message
              </button>
            </div>
          </div>
        </div>

        <!-- Order Details & Issue Selection -->
        <div class="order-details-view" *ngIf="showOrderDetails">
          <div class="section-header">
            <button class="back-btn" (click)="backToOrderSelection()">
              <i class="fa fa-arrow-left"></i>
            </button>
            <div class="header-content">
              <h4>{{selectedOrder?.restaurantName}}</h4>
              <p>Order {{selectedOrder?.id}} • {{selectedOrder?.orderDate | date:'MMM d, y'}}</p>
            </div>
          </div>

          <div class="selected-order-summary">
            <div class="order-items-summary">
              <h5>Items ordered:</h5>
              <ul>
                <li *ngFor="let item of selectedOrder?.items">{{item}}</li>
              </ul>
            </div>
            <div class="order-total-summary">
              <strong>Total: ${{selectedOrder?.total}}</strong>
            </div>
          </div>

          <div class="issue-selection">
            <h5>What's the issue?</h5>
            <div class="issues-grid">
              <button class="issue-btn" *ngFor="let issue of orderIssues" (click)="selectIssue(issue)">
                <i class="fa" [class]="issue.icon"></i>
                <span>{{issue.title}}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Issue Resolution -->
        <div class="issue-resolution" *ngIf="showIssueDetails">
          <div class="section-header">
            <button class="back-btn" (click)="backToIssueSelection()">
              <i class="fa fa-arrow-left"></i>
            </button>
            <div class="header-content">
              <h4>Issue Resolution</h4>
              <p>We're working on resolving your issue</p>
            </div>
          </div>

          <div class="resolution-content">
            <div class="resolution-status">
              <i class="fa fa-check-circle"></i>
              <p>Your issue has been escalated to our support team. You should receive a resolution within 24 hours.</p>
            </div>

            <div class="resolution-actions">
              <button class="action-btn primary" (click)="closeOrderHelp()">
                Continue Chat
              </button>
              <button class="action-btn secondary" (click)="closeOrderHelp()">
                Close
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Reservation Help Flow -->
      <div class="reservation-help-flow" *ngIf="showReservationHelp">

        <!-- Reservation Selection -->
        <div class="reservation-selection" *ngIf="showReservationSelection">
          <!-- Show all reservations when none is selected -->
          <div class="reservations-list" *ngIf="!selectedReservation">
            <div class="reservation-card" *ngFor="let reservation of userReservations">
              <div class="reservation-content">
                <div class="reservation-image">
                  <img [src]="reservation.image" [alt]="reservation.restaurantName"
                    onerror="this.src='assets/images/offer-image-one.png'">
                </div>
                <div class="reservation-details">
                  <div class="reservation-header">
                    <h5>{{reservation.restaurantName}}</h5>
                  </div>
                  <div class="reservation-meta">
                    <span class="reservation-name">Name: {{reservation.customerName}}</span>
                    <span class="reservation-time">Time: {{reservation.time}}</span>
                  </div>
                  <div class="reservation-date-row">
                    <span class="reservation-date">Date: {{reservation.date | date:'dd/MM/yyyy'}}</span>
                    <span class="reservation-guests">Guests: {{reservation.guests}}</span>
                  </div>
                  <button class="help-reservation-btn" (click)="selectReservation(reservation)">
                    Help with this reservation
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Show only selected reservation when one is selected -->
          <div class="reservations-list" *ngIf="selectedReservation">
            <div class="reservation-card">
              <div class="reservation-content">
                <div class="reservation-image">
                  <img [src]="selectedReservation.image" [alt]="selectedReservation.restaurantName"
                    onerror="this.src='assets/images/offer-image-one.png'">
                </div>
                <div class="reservation-details">
                  <div class="reservation-header">
                    <h5>{{selectedReservation.restaurantName}}</h5>
                  </div>
                  <div class="reservation-meta">
                    <span class="reservation-name">Name: {{selectedReservation.customerName}}</span>
                    <span class="reservation-time">Time: {{selectedReservation.time}}</span>
                  </div>
                  <div class="reservation-date-row">
                    <span class="reservation-date">Date: {{selectedReservation.date | date:'dd/MM/yyyy'}}</span>
                    <span class="reservation-guests">Guests: {{selectedReservation.guests}}</span>
                  </div>
                  <button class="help-reservation-btn selected">
                    Selected ✓
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="see-more-container" *ngIf="!selectedReservation">
            <button class="see-more-btn">
              See More
            </button>
          </div>
        </div>

        <!-- Reservation Details & Actions -->
        <div class="reservation-details-view" *ngIf="showReservationDetails">
          <!-- Show selected reservation card -->
          <div class="selected-reservation-card">
            <div class="reservation-card">
              <div class="reservation-content">
                <div class="reservation-image">
                  <img [src]="selectedReservation?.image" [alt]="selectedReservation?.restaurantName"
                    onerror="this.src='assets/images/product-1.png'">
                </div>
                <div class="reservation-details">
                  <div class="reservation-header">
                    <h5>{{selectedReservation?.restaurantName}}</h5>
                  </div>
                  <div class="reservation-meta">
                    <span class="reservation-name">Name: {{selectedReservation?.customerName}}</span>
                    <span class="reservation-time">Time: {{selectedReservation?.time}}</span>
                  </div>
                  <div class="reservation-date-row">
                    <span class="reservation-date">Date: {{selectedReservation?.date | date:'dd/MM/yyyy'}}</span>
                    <span class="reservation-guests">Guests: {{selectedReservation?.guests}}</span>
                  </div>
                  <button class="help-reservation-btn selected">
                    Selected ✓
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Action buttons grid -->
          <div class="reservation-actions-grid">
            <button class="reservation-action-btn" (click)="selectReservationAction('change-date')">
              Change date
            </button>
            <button class="reservation-action-btn" (click)="selectReservationAction('change-guests')">
              Change number of guests
            </button>
            <button class="reservation-action-btn" (click)="selectReservationAction('special-instructions')">
              Add special instructions
            </button>
            <button class="reservation-action-btn" (click)="selectReservationAction('cancel-reservation')">
              Cancel Reservation
            </button>
            <button class="reservation-action-btn" (click)="selectReservationAction('contact-restaurant')">
              Contact restaurant
            </button>
            <button class="reservation-action-btn" (click)="selectReservationAction('other')">
              Other
            </button>
          </div>
        </div>

        <!-- Reservation Action Result -->
        <div class="reservation-action-result" *ngIf="showReservationActions">
          <div class="section-header">
            <button class="back-btn" (click)="backToReservationDetails()">
              <i class="fa fa-arrow-left"></i>
            </button>
            <div class="header-content">
              <h4>Reservation Updated</h4>
              <p>Your request has been processed</p>
            </div>
          </div>

          <div class="action-result-content">
            <div class="result-status">
              <i class="fa fa-check-circle"></i>
              <p>Your reservation request has been submitted successfully. You'll receive a confirmation shortly.</p>
            </div>

            <div class="result-actions">
              <button class="action-btn primary" (click)="closeReservationHelp()">
                Continue Chat
              </button>
              <button class="action-btn secondary" (click)="closeReservationHelp()">
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions" *ngIf="chatMessages.length === 1 && !showOrderHelp && !showReservationHelp">
      <div class="quick-action-buttons">
        <button class="quick-action-btn" (click)="sendQuickMessage('Help with an order')">
          Help with an order
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('My Reservations')">
          My Reservations
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('Loyalty Points')">
          Loyalty Points
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('My Wallet')">
          My Wallet
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('Credits')">
          Credits
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('Dining In')">
          Dining In
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('My Account')">
          My Account
        </button>
        <button class="quick-action-btn" (click)="sendQuickMessage('Other')">
          Other
        </button>
      </div>
    </div>


  </div>

  <!-- Chat Input -->
  <div class="chat-input" [class.minimized]="isChatMinimized">
    <div class="input-container">
      <button class="attach-btn">
        <i class="fa fa-paperclip"></i>
      </button>
      <input type="text" [(ngModel)]="newMessage" (keyup.enter)="sendMessage()" placeholder="Enter your message"
        class="message-input" #messageInput>
      <button class="send-btn" (click)="sendMessage()" [disabled]="!newMessage.trim()">
        <i class="fa fa-paper-plane"></i>
      </button>
    </div>
  </div>
</div>

<!-- Chat Toggle Button (when minimized) -->
<div class="chat-toggle" [class.show]="isChatMinimized" (click)="maximizeChat()">
  <div class="chat-toggle-content">
    <img src="assets/images/customer-support-image.png" alt="Support" onerror="this.src='assets/images/user.png'">
    <span class="notification-badge" *ngIf="unreadMessages > 0">{{ unreadMessages }}</span>
  </div>
</div>