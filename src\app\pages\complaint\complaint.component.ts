import { Component, HostListener, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { Restaurant } from '../../core/models/restaurant';
import { NgForm } from '@angular/forms';
import { RestaurantService } from '../../core/services/restaurant.service';
import { UserService } from '../../core/services/user.service';
import { environment } from '../../../environments/environment';
import { CityService } from '../../core/services/city.service';
import { City } from '../../core/models/city';
import { Order } from '../../core/models/order';
import { OrderService } from '../../core/services/order.service';
import { User } from '../../core/models/user';
import { Promotions } from '../../core/models/promotions';
import { Complaint } from '../../core/models/complaint';
import { ComplaintService } from '../../core/services/complaint.service';
import { NotificationService } from '../../core/services/notification.service';
import { formatDate } from '@angular/common';
import { SocketService } from '../../core/services/socket.service';

@Component({
  selector: 'app-complaint',
  host: { ngSkipHydration: 'true' },
  templateUrl: './complaint.component.html',
  styleUrls: ['./complaint.component.scss']
})

export class ComplaintComponent implements OnInit {
  private subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  promotions: Promotions = new Promotions();
  complaints: Complaint[] = [];
  complaint: Complaint = new Complaint();
  close: boolean = false;
  intervalId: any;


  isLoading = false; error = null;
  isComplaintLoading = false; errorComplaint = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private restaurantService: RestaurantService,
    public userService: UserService,
    private orderService: OrderService,
    private complaintService: ComplaintService,
    private notificationService: NotificationService,
    private route: ActivatedRoute,
    private router: Router,
    private socketService: SocketService,
  ) {
  }

  ngOnInit() {
    if (this.route.snapshot.paramMap.get('id') && this.route.snapshot.paramMap.get('id') != null) {
      this.user = JSON.parse(this.userService.getUser());
      this.order.id = this.route.snapshot.paramMap.get('id');
      this.fetchUser();
      this.fetchOrder();
      this.fetchComplaints();
    }
  }

  fetchRestaurant() {
    this.error = null;

    this.subs.add(this.restaurantService.show(this.restaurant.id)
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.restaurant = res;
        this.promotions = this.restaurant.promotions[0];
        const businessId = this.restaurant.business.id;
        const soketUrl = this.restaurant.business?.socket_url;
        if (businessId && soketUrl) {
          this.socketService.connectSoketWithOrder(businessId, this.user.id, soketUrl, this.order.id);
          this.socketService.listen<any>('complaints').subscribe(data => {
            this.complaints = this.complaints.concat(data);
          });
        } else {
          this.intervalId = setInterval(() => {
            this.fetchIntervalComplaints();
          }, 3000);
        }

      }, err => this.error = err)
    );
  }

  fetchOrder() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.orderService.show(this.order.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.order = res;
          this.restaurant.id = this.order.restaurant_id;
          this.fetchRestaurant();
        }, (err) => {
          this.router.navigateByUrl('/account/orders');
        }
      )
    )
  }

  fetchUser() {
    this.error = null;

    this.subs.add(this.userService.show(this.user?.id)
      .pipe(finalize(() => { }))
      .subscribe(
        (res) => {
          this.user = res;
        }, (err) => {
        }
      )
    )
  }

  fetchComplaints() {
    this.isLoading = true;

    this.subs.add(this.complaintService.get({ order_id: this.order.id, nopaginate: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.complaints = res;
          this.close = this.complaints[0].close;
          this.complaint.message = '';
        }, (err) => {
          this.complaints = [];
        }
      )
    )
  }

  fetchIntervalComplaints() {
    this.subs.add(this.complaintService.get({ order_id: this.order.id, nopaginate: 1, last_id: this.complaints[this.complaints.length - 1]?.id })
      .pipe(finalize(() => { }))
      .subscribe(
        (res) => {
          // this.complaints = res;
          this.complaints = this.complaints.concat(res);
          this.close = this.complaints[0].close;
        }, (err) => { }
      )
    )
  }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.addComplaint(form)
  }

  addComplaint(form: NgForm) {
    if (this.socketService.isConnected()) {
      this.isComplaintLoading = true;
      this.errorComplaint = null;

      this.complaint.restaurant_id = this.restaurant.id;
      this.complaint.order_id = this.order.id;
      this.complaint.user_id = this.user.id;

      this.socketService.emit('complaints', this.complaint);
      this.isComplaintLoading = false;
      this.notificationService.showSuccess("Message send successfully !!", "Gogrubz");
      this.complaint.message = '';
      return;
    } else {
      this.isComplaintLoading = true; this.errorComplaint = null;

      this.complaint.restaurant_id = this.restaurant.id;
      this.complaint.order_id = this.order.id;
      this.complaint.user_id = this.user.id;

      this.complaintService.create(this.complaint)
        .pipe(finalize(() => (this.isComplaintLoading = false)))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("Message send successfully !!", "Gogrubz");
            this.complaint.message = '';
            this.fetchIntervalComplaints();
          },
          (err) => {
            this.errorComplaint = err;
          }
        );
    }
  }

  closeComplaint() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.complaintService.close(this.order.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.router.navigateByUrl('/account/orders');
      }, err => { this.error = err; })
    );
  }

  openComplaint() {
    this.isLoading = true; this.error = null;

    this.subs.add(this.complaintService.open(this.order.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.router.navigateByUrl('/account/orders');
      }, err => { this.error = err; })
    );
  }

  convertToDate(date, format?) {
    if (date) {
      return formatDate(date, format ? format : 'hh:mm a , MMMM dd ', 'en_US')
    } else {
      return null;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() {
    this.subs.unsubscribe(); clearInterval(this.intervalId);
    this.socketService.disconnectSocket();
  }
}
