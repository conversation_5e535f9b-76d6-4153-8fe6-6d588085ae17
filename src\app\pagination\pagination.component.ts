import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-pagination',
  host: { ngSkipHydration: 'true' },
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent implements OnInit {

  @Input() total_items: number;
  @Input() per_page: number;
  @Input() current_page: number;
  @Output() current_pageChange = new EventEmitter<number>();
  @Output() onChange = new EventEmitter<number>();

  total_pages = 1;
  pagesArrya = []
  pagedItems = [];
  constructor() { }

  ngOnInit(): void {
    this.pagedItems = this.pagesArrya.slice(this.current_page <= 3 ? 0 : this.current_page - 3, this.current_page >= 3 ? (this.total_pages == this.current_page ? this.current_page : this.current_page + 1) : (this.total_pages > 5 ? 5 : this.total_pages));
  }
  ngOnChanges() {
    if (this.total_items == 0) this.total_pages = 1;
    if (this.per_page == 0) this.total_pages = 1;

    this.total_pages = Math.ceil(this.total_items / this.per_page);

    this.pagesArrya = Array(this.total_items).fill(0).map((x, i) => ({ id: (i + 1), name: `Item ${i + 1}` }));
  }
  counter(i: number) {
    return new Array(i);
  }
  pageClick(page) {
    if (page == 'next') {
      if (this.current_page + 1 <= this.total_pages) {
        this.current_page = this.current_page + 1
        this.current_pageChange.emit(this.current_page)
        this.onChange.emit(this.current_page)
      }
    } else if (page == 'prev') {
      if (this.current_page - 1 > 0) {
        this.current_page = this.current_page - 1
        this.current_pageChange.emit(this.current_page)
        this.onChange.emit(this.current_page)
      }
    } else {
      this.current_page = page
      this.current_pageChange.emit(this.current_page)
      this.onChange.emit(this.current_page)
    }

  }
  setPage(page: number) {
    if (page < 1 || page > this.total_pages) {
      return;
    }
    this.current_page = page
    this.current_pageChange.emit(this.current_page)
    this.onChange.emit(this.current_page)
    // get current page of items
    this.pagedItems = this.pagesArrya.slice(this.current_page <= 3 ? 0 : this.current_page - 3, this.current_page >= 3 ? (this.total_pages == this.current_page ? this.current_page : this.current_page + 1) : (this.total_pages > 5 ? 5 : this.total_pages));
  }
}
