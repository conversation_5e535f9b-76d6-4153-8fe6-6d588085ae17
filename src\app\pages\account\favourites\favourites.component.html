<div class="container loader-height" *ngIf="isLoading">
  <div class="grubz-loader">
    <div class="set-one">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
    <div class="set-two">
      <div class="circle"></div>
      <div class="circle"></div>
    </div>
  </div>
</div>

<div class="your-favourite-restaurants" *ngIf="!isLoading">
  <p>Your Favourite Restaurants</p>
</div>


<div class="dont-have-order text-center" *ngIf="favouriteRestaurants.length <= 0 && !isLoading">
  <p>You have not added any restaurant in your favourite list.</p>
  <!-- <span>Make your first order by clicking the button below.</span> -->
</div>

<div *ngIf="favouriteRestaurants.length > 0 && !isLoading">
  <div class="order-history-box" *ngFor="let favouriteRestaurant of favouriteRestaurants; let i=index">
    <div class="order-image">
      <div class="order-logo">
        <img [src]="favouriteRestaurant?.restaurant?.image_url" [alt]="favouriteRestaurant?.restaurant?.restaurant_name"
          onerror="this.src='./assets/favicon.png';">
      </div>
      <img class="order-main-image" [src]="favouriteRestaurant?.restaurant?.promotions[0]?.image_url"
        [alt]="favouriteRestaurant?.restaurant?.restaurant_name" onerror="this.src='./assets/images/product-1.png';">
    </div>

    <div class="order-details">
      <div class="order-title">
        <h6>{{ favouriteRestaurant?.restaurant?.restaurant_name }}</h6>
      </div>
      <div class="address-details">
        <p>Address:</p>
        <ul>
          <li>{{ favouriteRestaurant?.restaurant?.street_address }}</li>
        </ul>
      </div>
    </div>

    <div class="view-store">
      <ul>
        <li>
          <button class="btn" (click)="favourite(favouriteRestaurant)">Remove</button>
        </li>
        <li>
          <button class="btn" (click)="orderStore(favouriteRestaurant)">View Store</button>
        </li>
      </ul>
    </div>
  </div>
</div>

<ng-template #confirmationModel let-modal>
  <div class="modal-header justify-content-center border-0">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <svg class="fa-solid fa-xmark"></svg>
    </button>
    <h5 class="login-title mt-2 m-0">Confirmation</h5>
  </div>
  <div class="modal-body product-body">
    <p class="text-center">Are you sure you want to remove favourite restaurant?</p>
    <div class="d-flex justify-content-center pt-2">
      <button type="button" class="btn modal-black-btn" (click)="close(false)">No</button>
      <button type="button" class="btn modal-black-btn" (click)="close(true)">Yes</button>
    </div>
  </div>
</ng-template>