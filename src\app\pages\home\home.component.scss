$primary: #FC353A;

:root {
  --primary: #FC353A;
}

::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

/* -----Banner-CSS------*/

.banner-bg {
  padding: 230px 138px 25px 138px;
  position: relative;
  background: rgb(252, 53, 58);
  background: linear-gradient(180deg, rgba(252, 53, 58, 1) 0%, rgba(252, 53, 58, 1) 0%, rgba(225, 47, 51, 1) 83%, rgba(163, 33, 36, 1) 100%);
  margin-bottom: 40px;
}
.banner-bg::before{
  width: 100%;
  height:calc(100% + 20px);
  position: absolute;
  left:-100%;
  top:-20px;
  bottom:0;
  content:'';  
  background: rgb(252, 53, 58);
  background: linear-gradient(180deg, rgba(252, 53, 58, 1) 0%, rgba(252, 53, 58, 1) 0%, rgba(225, 47, 51, 1) 83%, rgba(163, 33, 36, 1) 100%);
}
.banner-bg::after{
  width: 100%;
  height:calc(100% + 20px);
  position: absolute;
  right:-100%;
  top:-20px;
  bottom:0;
  content:'';  
  background: rgb(252, 53, 58);
  background: linear-gradient(180deg, rgba(252, 53, 58, 1) 0%, rgba(252, 53, 58, 1) 0%, rgba(225, 47, 51, 1) 83%, rgba(163, 33, 36, 1) 100%);
}
.banner-top-shape{  
  position: absolute;
  left:0;
  right:0;  
  top:-100%;
  height:100%;
  background: var(--primary);
}
.banner-content {
  position: relative;
  z-index:10;
  padding-top: 20px;
}

.banner-content h2 {
  font-family: 'Fredoka One';
  font-weight: normal;
  color: #fff;    
  position: relative;
  z-index:1;
  margin-bottom:0;
}

.banner-content h2 img {  
  width: 52px;
  position: relative;
  top: -8px;
  margin-right: 6px;
}
.banner-content-shape{
  position: relative;
  padding: 20px 0;
  margin-bottom: 40px;
}
.banner-content-shape::before{
  width:calc(100% + 120px);
  height:100%;
  position: absolute;
  left:-120px;
  right:0;
  top:0;
  bottom:0;
  content:'';
  background: rgb(251,52,57);
  background: linear-gradient(90deg, #ef3236 1%, #f73438 10%, #ea3035 34%, #db2d31 75%, #db2d31);
  border-radius: 0 80px 30px 0;
  transform: skew(-20deg, -1deg);
  animation:banner-shape 1s cubic-bezier(0.215, 0.61, 0.355, 1) forwards;
}
@keyframes banner-shape{
  0% {
    width: 0%;
  }
  100% {
    width:calc(100% + 120px);
  }
}
.banner-shapes{
  width:100px;
  height:100px;
  position: absolute;
  top:-45px;
  left:-120px;
  display:none;
}
.banner-shapes .shape-one{
  width: 55px;
  height: 55px;
  border-radius: 50%;
  background-color:#DB2D31;
}
.banner-shapes .shape-two{
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #db2d31;
  position: absolute;
  left: 65px;
  top: 30px;
}
.banner-shapes .shape-three{
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #db2d31;
  position: absolute;  
  right: 35px;
  bottom: 0px;
}
.got-account-sign-in p{
  color: #fff;
  font-size: 25px;
  font-weight: 500;
  line-height: 30px;
  font-family: 'Fredoka';
  margin-bottom:0;
}
.got-account-sign-in p a{
  color: #fff;
  text-decoration: underline;
}
.users-restaurants-main-box {
  max-width: 580px;
  display: flex;
  display: none;
}

.users-restaurants-box {
  margin-right: 110px;
  text-align: center;
}

.users-restaurants-box h3 {
  font-size: 60px;
  color: #fff;
  line-height: 60px;
  margin-bottom: 24px;
}

.users-restaurants-box p {
  font-size: 32px;
  color: #fff;
  font-family: 'Fredoka';
  line-height: 30px;
  margin-bottom: 0;
}

.users-restaurants-box:last-child {
  margin-right: 0;
}

.banner-image {
  text-align: right;
  position: absolute;
  z-index:2;  
  right:-42px;
  bottom:135px;
}
.banner-image img{
  max-width: 100%;
}
.banner-bottom-shape{
  position: absolute;
  z-index:1;
  bottom:0;
  left:-40px;
  right:-40px;
  width:calc(100% + 80px);
  height:340px;
  background: rgb(219,45,49);
  background: linear-gradient(90deg, rgba(219,45,49,1) 0%, rgba(214,44,48,1) 100%);
  clip-path: polygon(0 50%,100% 0,100% 100%,0 100%);
  display: none;
}
.favourite-dishes{
  padding-top: 110px;
  text-align: center;
}
.favourite-dishes h5{  
  font-size: 30px;
  font-weight: 400;
  line-height: 36px;
  color:#fff;
  font-family: 'Fredoka One';
  margin-bottom: 15px;
}
.favourite-dishes button{
  background-color: transparent;
  border:0;
  border-radius:0;
}
.favourite-dishes img{
  -moz-animation: bounce 2s infinite;
  -webkit-animation: bounce 2s infinite;
  animation: bounce 2s infinite;
}
/* -----Join-the-movement-CSS------*/
.Join-the-movement-bg {
  width: 100%;
  height: 510px;
  position: relative;
  overflow-x: hidden;
  margin-bottom: 65px;
}

.Join-the-movement-bg::before {
  max-width: 985px;
  width: 100%;
  height: 100%;
  background-color: #FDF6F2;
  border-radius: 35px 0 0 35px;
  content: '';
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  bottom: 0;
  clip-path: polygon(0 0, 100% 0%, 53% 100%, 0% 100%);
}
.Join-the-movement-bg .container{
  height: 100%;
}
.Join-the-movement-bg img.Join-bg-image {
  height: 100%;
  border-radius: 35px;
  object-fit: cover;
  position: absolute;
  z-index: 1;
  right: 0;
  max-width: 100%;
  width:calc(100% - 420px);
}

.Join-the-movement-bg .row {
  position: relative;
  z-index: 10;
}

.Join-movement-content {
  position: relative;
  z-index: 1;  
}

/* -----Everything-you-crave-CSS------*/
.everything-you-crave-bg {
  margin-bottom: 70px;
  overflow-x: hidden;
}

.everything-you-crave-image {
  max-width: 530px;
  width: 100%;
  text-align: center;
  padding: 75px 0 0 0;
  background-color: #FDF6F2;
  border-radius: 35px;
}

.everything-you-crave-image img {
  max-width: 380px;
}

.everything-you-crave-content {
  padding: 30px 80px 30px 175px;
  background-image: url('/assets/images/everything-crave-shape.svg');
  background-repeat: no-repeat;
  background-position: center;
}

.everything-you-crave-content a.btn {
  padding: 10px 29px;
}

.perfect-cuisine-image img {
  max-width: 520px;
}


/* -----find-perfect-cuisine-CSS------*/
.find-perfect-cuisine-bg {
  margin-bottom: 65px;
  overflow-x: hidden;
}

.perfect-cuisine-content {
  padding-top: 50px;
  padding-bottom: 50px;
  padding-left: 106px;
  background-image: url(/assets/images/find-cuisine-shape.svg);
  background-repeat: no-repeat;
  background-position: 140px;
}

.perfect-cuisine-content a.btn {
  padding: 5px 15px;
}

/* -----Download-Our-App-CSS------*/
.download-our-app-bg {
  padding-top: 80px;
  background-color: $primary;
  border-radius: 50px;
  overflow-x: hidden;
  margin-bottom: 49px;
  position: relative;
}
.download-our-app-bg .shape-column-one{
  position: absolute;
  top: 5px;
  left:30px;
  z-index:5;
}
.download-our-app-bg .shape-column-one .shape-one{
  width: 15px;
  height: 15px;
  background-color: #DB2D31;
  border-radius: 50%;
  position: absolute;
  top: 20px;
  left:105px;
}
.download-our-app-bg .shape-column-one .shape-two{
  width: 30px;
  height: 30px;
  background-color: #DB2D31;
  border-radius: 50%;
  position: absolute;
  top: 30px;
  left:70px;
}
.download-our-app-bg .shape-column-two{
  width: 112px;
  height: 112px;
  background-color:#DB2D31;
  border-radius: 50%;
  position: absolute;
  top: 40px;
  right:65px;
}
.download-our-app-bg .shape-column-three{
  width: 45px;
  height: 45px;
  background-color:#FC353A;
  border-radius: 50%;
  position: absolute;
  right:175px;
  bottom:65px;
  z-index:5;
}
.download-our-app-bg .download-bottom-shape{
  position: absolute;
  z-index:1;
  bottom:0;
  left:0px;
  right:0px;
  width:100%;
  height:278px;
  background: #DB2D31;
  clip-path: polygon(0 45%,100% 0,100% 100%,0 100%);
  display: block;
}
.download-our-app-bg .row{
  position: relative;
  z-index: 2;
}
.download-our-app-image {
  padding-left: 105px;
}

.download-our-app-image img {
  max-width: 100%;
}

.download-our-app-content {
  padding-top: 62px;
  padding-left: 107px;
}

.download-our-app-content .main-heading h2 {
  color: #fff;
  line-height: 46px;
}

.download-our-app-content .main-heading p {
  color: #fff;
}

.download-our-app-content ul {
  padding-top: 13px;
  margin-bottom: 0;
}

.download-our-app-content ul li {
  display: inline-block;
  margin-right: 17px;
}

.download-our-app-content ul li img {
  width: 140px;
}

/* -----Restaurants-Near-Me-CSS------*/
.restaurants-bg {
  position: relative;
  overflow-x: hidden;
}
.restaurants-bg img {
  max-width: 100%;
  width: 100%;
}

.restaurants-near-me {
  position: absolute;
  padding: 28px 35px;
  top: 60px;
  left: 100px;
  background-color: #fff;
  border-radius: 24px;
  z-index: 2;
}

.restaurants-near-me h3 {
  line-height: 44px;
  color: $primary;
  margin-bottom: 0;
}

/* -----View-all-cities-CSS------*/
.view-all-cities-bg {
  padding: 65px 70px 55px 70px;
}

.view-all-cities-bg .main-heading {
  margin-bottom: 92px;
}

.cities-box ul {
  width: 100%;
  margin-bottom: 35px;
}

.cities-box ul li {
  width: 100%;
  display: inline-block;
  margin-bottom: 0px;
}

.cities-box ul li span {
  font-size: 25px;
  color: #101A24;
  font-weight: 600;
  text-decoration: none;
  overflow: hidden;
  word-break:break-word;
  cursor: pointer;
  display: inline-block;
  position: relative;
}
.cities-box ul li span::before{
  position: absolute;
  content:'';
  width:0;
  height:2px;  
  background-color:#101a24;
  margin: auto;
  left:0;
  right:0;
  bottom:0;
  transition: all 0.5s;  
}
.cities-box ul li span:hover:before{
  width:100%;
}
.city-character-count{
  margin-bottom: 40px;
}
.city-character-count span{  
  font-size: 28px;
  color: #fff;
  font-weight: 700;
  width: 60px;
  height: 55px;
  display: inline-block;
  line-height: 44px;
  text-align: center;
  background-color: #fc353a;
  border: 3px dashed #ffffff;
}
.middle-sections{
  padding:0 110px;
}
.offers-section{  
  margin-bottom: 65px;
}
.offers-section .carousel{
  margin-left: -30px;
  margin-right: -30px;
}
.offers-section .main-heading{
  width: 340px;
  height: 215px;
  background-image: url(/assets/images/offers-heading-shape.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
  margin: auto; 
  margin-bottom: 30px; 
}
.offers-slider-item{
  padding: 0 27px;
}
.offers-box{  
  display: flex;
  background-color:#FDF6F2;
  border-radius: 28px;
}
.offers-box-content{
  padding: 25px 25px;
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.offers-box-content h5{
  font-family: 'Fredoka One';
  font-size: 20.53px;
  font-weight: 400;
  line-height: 18.66px;
  text-align: left;
  color: #000000;
  margin-bottom: 26px;
}
.offers-box-content p{
  font-family: 'Fredoka';
  font-size: 16.8px;
  font-weight: 400;
  line-height: 18.67px;
  color:#000000;
  margin-bottom: 26px;
}
.offers-box-content.green-text h5{
  color:#14A411;
}
.offers-box-content.red-text h5{
  color:#FC353A;
}
.offers-box-content.orange-text h5{
  color:#FD811F;
}
.offers-box-content a.btn{
  width: 108px;
  height: 28px;
  padding: 4px;
  font-family: 'Visby CF';
  font-size: 14.86px;
  font-weight: 600;
  line-height: 17.83px;
  text-align: center;
}
.offers-box-content.green-text a.btn{
  color:#fff !important;
  background-color:#14A411 !important;
  border-color:#14A411 !important;
}
.offers-box-content.green-text a.btn:hover{
  color:#14A411 !important;
  background-color:transparent !important;
}
.offers-box-content.red-text a.btn{
  color:#fff !important;
  background-color:#FC353A !important;
  border-color:#FC353A !important;
}
.offers-box-content.red-text a.btn:hover{
  color:#FC353A !important;
  background-color:transparent !important;
}
.offers-box-content.orange-text a.btn{
  color:#fff !important;
  background-color:#FD811F !important;
  border-color:#FD811F !important;
}
.offers-box-content.orange-text a.btn:hover{
  color:#FD811F !important;
  background-color:transparent !important;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

@media screen and (min-width:2000px) {
  .Join-the-movement-bg img.Join-bg-image {
    width: 100%;
  }
  .Join-the-movement-bg:before{
    max-width: 50%;
  }
  
}

@media screen and (min-width:2200px) {
  .Join-the-movement-bg:before{
    max-width: 55%;
    clip-path: polygon(0 0, 100% 0, 75% 100%, 0 100%);
  }  
  
  }

@media screen and (max-width:1800px) {
  .banner-bg {
    padding: 205px 90px 25px 90px;
  }

  .banner-content h2 {
    font-size: 72px;
    line-height: 96px;  
  }

  .banner-content h2 img {
    width: 52px;
  }

  .users-restaurants-box {
    margin-right: 90px;
  }

  .users-restaurants-box:last-child {
    margin-right: 0;
  }
  
  .perfect-cuisine-content {
    padding-left: 90px;
  }

  .download-our-app-image {
    padding-left: 90px;
  }

  .everything-you-crave-content {
    padding: 30px 40px 30px 88px;
  }

  .download-our-app-content {
    padding-top: 0;
    padding-left: 70px;
    padding-right: 50px;
  }    
  .view-all-cities-bg {
    padding: 60px 120px 50px 120px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 72px;
  }

  .cities-box ul {
    margin-bottom: 30px;
  }

  .cities-box ul li span {
    font-size: 24px;
  }

}

@media screen and (max-width:1600px) {
  .Join-movement-content{
      padding-left: 115px;
  }
  
}

@media screen and (max-width:1500px) {
.middle-sections{
  padding: 0 74px;
}  
.banner-bg{    
  margin-bottom: 25px;
  padding-bottom: 20px;
}
.banner-content{
  padding-top:0;
}
.banner-content h2 {
  font-size: 62px;
  line-height: 74px;  
}
.banner-shapes{
  top: -55px;
  left: -80px;
}
.banner-content h2 img{
  top:-5px;
  width: 42px;  
}
.users-restaurants-box h3 {
  font-size: 48px;
  line-height: 50px;
  margin-bottom: 5px;
}
.users-restaurants-box p {
  font-size: 25px;
}
.banner-shapes{
  width: 80px;
  height: 80px;
  top: -55px;
  left: -80px;
}
.banner-shapes .shape-one{
  width: 42px;
  height: 42px;
}
.banner-shapes .shape-two {
  width: 26px;
  height: 26px;  
  top: 30px;
  left: 45px;
}
.banner-shapes .shape-three {
  width: 15px;
  height: 15px;
}
.banner-bottom-shape{
  height: 230px;
}
.banner-content-shape{
  padding: 35px 0;
}
.banner-content-shape:before {
  width: 80%;
  left: -40px;
  right: auto;
  max-width: 100%;
}
.banner-image{
  right:-40px;
}
.offers-section{
  margin-bottom: 50px;
}
.offers-section .main-heading {
  width: 260px;
  height: 165px;  
  margin-bottom: 25px;
}
.Join-the-movement-bg{
  height: 378px;
}
.Join-the-movement-bg:before{
  border-radius:36px 0 0 36px;
  max-width: 60%;
  clip-path: polygon(0 0, 100% 0, 58% 100%, 0 100%);
}
.Join-the-movement-bg img.Join-bg-image {
  border-radius: 26px;
  width:calc(100% - 350px);
}
.everything-you-crave-bg{
  margin-bottom: 65px;
}
.everything-you-crave-image{
  max-width: 400px;
  padding-top: 55px;
  border-radius: 26px;
}
.everything-you-crave-image img {
  max-width: 285px;
}
.everything-you-crave-content{
  padding: 50px 10px 50px 120px;
}
.everything-you-crave-content a.btn{
  padding: 4px 18px;
}
.perfect-cuisine-content{
  background-position: 120px;
}
.perfect-cuisine-content a.btn{
  padding: 4px 12px;
}
.perfect-cuisine-image img {
  max-width: 400px;
}
.download-our-app-bg{
  padding-top: 70px;
  margin-bottom: 35px;
  border-radius: 36px;
}
.download-our-app-bg .shape-column-one{
  top:0;
  left:0;
}
.download-our-app-image img{
  max-width: 408px;
}
.download-our-app-content{  
  padding-left: 40px;
  padding-right: 40px;
}
.download-our-app-content .main-heading h2 {
  margin-bottom: 45px;
}
.download-our-app-content .main-heading p{
  margin-bottom: 30px;
}
.download-our-app-content ul li img {
  width: 90px;
}
.download-our-app-bg .download-bottom-shape {  
  height: 218px;  
  -webkit-clip-path: polygon(0 35%, 100% 0, 100% 100%, 0 100%);
  clip-path: polygon(0 35%, 100% 0, 100% 100%, 0 100%);  
}
.restaurants-near-me{
  padding: 20px 28px;
  border-radius: 18px;
}
.restaurants-near-me h3 {
  font-size: 36px;
  line-height: 34px;  
}
.view-all-cities-bg{
  padding: 65px 55px 15px 55px;
}
.cities-box ul li span {
  font-size: 16px;
}
.city-character-count{
  margin-bottom: 30px;
}
.city-character-count span {
  font-size: 24px;
  width: 56px;
  height: 50px;
  line-height: 42px;
}

}

@media screen and (max-width:1300px) {
  .banner-bg {
    padding: 180px 70px 20px 70px;
  }    
  
  .banner-content h2 {
    font-size: 52px;
    line-height: 64px;    
  }

  .banner-content h2 img {
    width: 34px;
  }

  .Join-the-movement-bg img.Join-bg-image{
    width:100%;
  }
  .Join-movement-content{
    padding-left: 80px;
  }
  .offers-box-content h5{
    font-size: 18.53px;
    margin-bottom: 20px;
  }
  .offers-box-content p{
    font-size: 15px;
    margin-bottom: 20px;
  }
  .perfect-cuisine-content {
    padding-left: 70px;
  }

  .everything-you-crave-content {
    padding: 50px 10px 50px 68px;
  }

  .view-all-cities-bg {
    padding:50px 50px 10px;
  }

  .download-our-app-image {
    padding-left: 70px;
  }

  .Join-the-movement-bg::before {
    max-width: 650px;
    border-radius:26px 0 0 26px;
  }
  .restaurants-bg .map-container {
    height: 480px !important;
  }

}

@media screen and (max-width:1199px) {
  .banner-bg {
    padding: 150px 80px 20px 80px;    
  }  
  .banner-content h2 img{
    width:35px;
    top:-5px;
  }      
  .banner-image{
    right: -20px;
  }
  .banner-image img {
    max-width: 450px;
  }
  .offers-box-content{
    align-items: flex-start;
    flex-direction: column;
  }
  .offers-box-content h5 {
    margin-bottom: 30px;
  }
  .offers-box-content p{
    margin-bottom: 30px;
  }
  .Join-the-movement-bg {
    height: 350px;
    margin-bottom: 50px;
  }
  .Join-the-movement-bg::before {
    max-width: 600px;
    clip-path: polygon(0 0, 100% 0%, 63% 100%, 0% 100%);
  }  
  .Join-movement-content{
    padding-left: 50px;
  }
  .everything-you-crave-bg{
    margin-bottom: 50px;
  }
  .everything-you-crave-image {
    padding: 40px 40px 0 40px;
  }

  .download-our-app-bg {
    padding-top: 40px;
  }
  .download-our-app-image {
    padding-left: 60px;
  }  
  .download-our-app-image img {
    max-width: 360px;
  }
  .download-our-app-bg .shape-column-one .shape-one {
    width: 12px;
    height: 12px;    
    left: 65px;
  }
  .download-our-app-bg .shape-column-one .shape-two {
    width: 25px;
    height: 25px;    
    top: 30px;
    left: 30px;
  }
  .download-our-app-content {
    padding-left: 30px;
    padding-right: 20px;
  }
  .download-our-app-content .main-heading {
    margin-bottom: 20px;
  }
  .download-our-app-content .main-heading h2 {
    margin-bottom: 35px;
  }  
  .download-our-app-bg .download-bottom-shape{    
    height: 178px;
  }
  .download-our-app-bg .shape-column-two {
    width: 80px;
    height: 80px;    
    top: 25px;
    right: 40px;
  }
  .download-our-app-bg .shape-column-three {
    width: 35px;
    height: 35px;    
    right: 85px;
    bottom: 40px;
  }
  .find-perfect-cuisine-bg{
    margin-bottom: 50px;
  }
  .perfect-cuisine-content {
    padding-left: 0px;
    background-position: 0;
  }
  .perfect-cuisine-image img {
    max-width: 100%;
  }  
  .restaurants-near-me {
    top: 50px;
    left: 80px;
  }
  .restaurants-near-me h3 {
    line-height: 36px;
  }
  .view-all-cities-bg {
    padding: 50px 30px 10px 30px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 52px;
  }  

}

@media screen and (max-width:991px) {
  .middle-sections{
    padding: 0 30px;
  }
  .banner-bg {
    padding: 160px 30px 20px 30px;
  }

  .banner-content {
    text-align: center;
    padding-top: 20px;
    padding-bottom: 40px;
  }
  .banner-shapes{
    top: -40px;
    left: -45px;
  }
  .banner-shapes .shape-one {
    width: 36px;
    height: 36px;
  }
  .banner-shapes .shape-two {
    width: 20px;
    height: 20px;
    left: 40px;
  }
  .banner-shapes .shape-three {
    right: 40px;
    bottom: 5px;
  }
  .banner-content-shape{
    padding: 30px 0;
    margin-bottom: 30px;
  }
  .banner-content-shape:before {
    width: 100%;
    left: 0;
    right: 0;    
  }
  .users-restaurants-main-box {
    max-width: 100%;
    justify-content: center;
  }
  .banner-image {
    text-align: center;
    position: relative;
    right: 0;
    bottom: 0;
  }
  .banner-image img {
    max-width: 450px;
  }
  .offers-section .carousel{
    margin-left: -20px;
    margin-right: -20px;
  }
  .offers-slider-item{
    padding: 0 20px;
  }
  .got-account-sign-in{
    padding-top: 25px;
  }
  .got-account-sign-in p{
    font-size: 20px;
  }
  .favourite-dishes{
    padding-top: 50px;
  }
  .Join-the-movement-bg img.Join-bg-image {
    display: none;
  }

  .Join-the-movement-bg::before {
    max-width: 100%;
    clip-path: polygon(0 0, 100% 0%, 100% 100%, 0% 100%);
    border-radius: 30px;
  }

  .Join-movement-content {    
    text-align: center;
    padding:0;
  }

  .everything-you-crave-image {
    max-width: 100%;
    padding-top: 50px;
    border-radius: 30px;
  }

  .everything-you-crave-content {
    padding: 0 30px 0 68px;
    background-size: 250px;
  }

  .everything-you-crave-content {
    padding: 30px 30px 0 30px;
    text-align: center;
  }

  .perfect-cuisine-content {
    padding: 30px 30px 0 30px;
    text-align: center;
    background-position: center;
    background-size: 200px;
  }

  .perfect-cuisine-image {
    text-align: center !important;
  }

  .download-our-app-bg {
    padding-top: 50px;
    border-radius: 30px;
  }

  .download-our-app-image {
    padding: 0 30px;
    text-align: center;
  }

  .download-our-app-content {
    padding: 30px 30px;
    text-align: center;
  }  
  .restaurants-bg img {
    min-height: 350px;
    border-radius: 30px;
    object-fit: cover;
  }

  .restaurants-near-me {
    top: 50px;
    left: 75px;
  }

  .view-all-cities-bg {
    padding: 50px 20px 20px 20px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 50px;
  }

  .cities-box {
    margin-bottom: 40px;
  }

}

@media screen and (max-width:767px) {    
  .restaurants-near-me{
    top: 40px;
    left: 55px;
  }
  
}

@media screen and (max-width:575px) {
  .banner-bg {
    padding-top: 150px;
    margin-bottom: 30px;
  }
  .banner-shapes{
    left: -35px;
  } 
  .banner-image img {
    max-width: 350px;
  }
  .banner-bottom-shape{
    height: 150px;
  }
  .banner-content-shape{
    padding: 30px 20px;    
  }
  .favourite-dishes{
    padding-top: 40px;
  }
  .favourite-dishes h5{
    font-size: 24px;
    margin-bottom: 10px;
  }
  .middle-sections{
    padding: 0;
  }
  .offers-section{
    margin-bottom: 40px;
  }
  .Join-the-movement-bg{
    margin-bottom: 40px;
  }
  .Join-movement-content{
    padding: 0 30px;
  }
  .everything-you-crave-image {
    padding-top: 40px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .everything-you-crave-content {
    padding-top: 30px;    
  }

  .users-restaurants-box {
    margin-right: 40px;
  }

  .users-restaurants-box:last-child {
    margin-right: 0;
  }

  .users-restaurants-box h3 {
    font-size: 48px;
    margin-bottom: 0;
  }

  .users-restaurants-box p {
    font-size: 24px;
  }
  .everything-you-crave-bg{
    margin-bottom: 40px;
  }
  .find-perfect-cuisine-bg{
    margin-bottom: 40px;
  }
  .perfect-cuisine-content {
    padding-top: 30px;
  }
  .view-all-cities-bg{
    padding-top: 40px;
  }
  .view-all-cities-bg .main-heading {
    margin-bottom: 40px;
  }
  .city-character-count{
    text-align: center;
  }
  .city-character-count span {
    font-size: 20px;
    width: 50px;
    height: 44px;
    line-height: 36px;
  }

}

@media screen and (max-width:480px) {
  .banner-bg {    
    padding: 120px 10px 20px 10px;
  }
  .got-account-sign-in{
    padding: 20px 0;    
  }
  .got-account-sign-in p{
    font-size: 16px;
  }
  .banner-content-shape{
    padding: 20px 20px;
  }
  .banner-content-shape:before {
    border-radius: 0 50px 20px 0;
    transform: skew(-15deg, -1deg);    
  }
  .banner-shapes{
    left: 0;
  }
  .banner-shapes .shape-one {
    width: 25px;
    height: 25px;
  }
  .banner-shapes .shape-two {
    width: 15px;
    height: 15px;
    top: 24px;
    left:30px;
  }
  .banner-shapes .shape-three {
    width: 12px;
    height: 12px;
    right: 50px;
    bottom: 20px;
  }
  .banner-image img {
    max-width: 270px;
  }

  .banner-content {
    padding-top: 15px;
    padding-bottom: 10px;
  }

  .banner-content h2 {
    font-size: 34px;
    line-height: 48px;
  }

  .banner-content h2 img {
    width: 24px;
    top: -3px;
  }

  .users-restaurants-main-box {
    flex-wrap: wrap;
  }

  .users-restaurants-box {
    width: 100%;
    margin: 0;
    margin-bottom: 25px;
  }

  .users-restaurants-box h3 {
    font-size: 45px;
    line-height: normal;
    margin-bottom: 5px;
  }
  .favourite-dishes{
    padding-top: 30px;
  }
  .favourite-dishes h5 {
    font-size: 22px;
    line-height: 30px;    
  }
  .restaurants-bg img {
    min-height: 320px;
  }
  .offers-section .main-heading h5{
    font-size: 28px;
  }
  .offers-section .main-heading {
    width: 200px;
    height: 125px;
    margin-bottom: 20px;
  }
  .offers-box{
    border-radius: 20px;
  }
  .offers-box-content{
    padding: 20px 25px;
  }
  .offers-box-content h5{
    font-size: 15px;
    margin-bottom: 18px;
  }  
  .offers-box-content p{
    margin-bottom: 25px;
  }
  .offers-box-image img{
    width: 120px;
  }  
  .offers-box-content a.btn {
    width: 100px;
    height: 26px;
    padding: 2px;
    font-size: 13.86px;    
  }
  .Join-the-movement-bg {
    height: 360px;
  }
  .Join-the-movement-bg:before {
    border-radius: 20px;
  }  
  .everything-you-crave-image{
    border-radius: 20px;
    padding: 30px 20px 0 20px;
  }
  .everything-you-crave-image img{
    max-width: 100%;
  }
  .everything-you-crave-content{
    padding: 30px 20px 0 20px;
  }    
  .perfect-cuisine-content{    
    padding: 30px 20px 0 20px;
  }  
  .restaurants-near-me {
    padding: 15px 20px;
    border-radius: 15px;
    top: 20px;
    left: 20px;
  }
  .restaurants-near-me h3 {
    font-size: 35px;
    line-height: 42px;
  }
  .download-our-app-bg {
    padding-top: 40px;
    border-radius: 20px;
  }
  .download-our-app-bg .shape-column-one .shape-one {
    width: 10px;
    height: 10px;
    left: 25px;
  }
  .download-our-app-bg .shape-column-one .shape-two{
    width: 20px;
    height: 20px;
    top: 30px;
    left: 8px;
  }
  .download-our-app-image{
    padding: 0 20px;
  }
  .download-our-app-image img {
    max-width: 255px;
  }  
  .download-our-app-content {
    padding: 20px;
  }
  .download-our-app-content .main-heading h2 {    
    margin-bottom: 25px;
  }
  .download-our-app-content .main-heading p {
    margin-bottom: 20px;
  }
  .download-our-app-content ul li {
    width: 100%;
    margin: 0;
    margin-bottom: 10px;
  }
  .download-our-app-content ul li:last-child {
    margin-bottom: 0;
  }
  .download-our-app-bg .download-bottom-shape{
    height: 138px;
  }
  .download-our-app-bg .shape-column-two {
    width: 60px;
    height: 60px;
    top: 15px;
    right: 20px;
  }
  .download-our-app-bg .shape-column-three{
    width: 30px;
    height: 30px;
    right: 25px;
    bottom: 20px;
  }
  .view-all-cities-bg {
    padding: 40px 10px 20px 10px;
  }
  .view-all-cities-bg .main-heading {
    margin-bottom: 30px;
  }
  .cities-box{
    margin-bottom: 35px;
  }

}
