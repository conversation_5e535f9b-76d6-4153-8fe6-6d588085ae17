import { Restaurant } from "./restaurant";

export class FavouriteRestaurant {
  id: string;
  restaurant_id: string;
  customer_id: string;
  is_favourite: boolean;

  restaurant: Restaurant;

  created_at: string;
  updated_at: string;

  static toFormData(favourite_restaurant: FavouriteRestaurant) {
    const formData = new FormData();

    if (favourite_restaurant.id) formData.append('id', favourite_restaurant.id);
    if (favourite_restaurant.restaurant_id) formData.append('restaurant_id', favourite_restaurant.restaurant_id);
    if (favourite_restaurant.customer_id) formData.append('customer_id', favourite_restaurant.customer_id);
    formData.append('is_favourite', favourite_restaurant.is_favourite ? '1' : '0');

    return formData;
  }
}
