import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { PrivacyPolicyComponent } from './privacy-policy.component';

const routes: Routes = [
  { path: '', component: PrivacyPolicyComponent },
];
@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule
  ],
  declarations: [PrivacyPolicyComponent],
  exports: [PrivacyPolicyComponent]
})
export class PrivacyPolicyModule { }
