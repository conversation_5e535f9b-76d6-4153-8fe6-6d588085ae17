import { Injectable } from '@angular/core';
import {
  CanActivate,
  CanActivateChild,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { UserService } from './services/user.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate, CanActivateChild {
  constructor(private router: Router, private userService: UserService) { }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    // This method calls whenever we reload page; We can authenticate with our backend
    if (this.userService.hasTokens()) {
      return this.userService.me().pipe(
        map((user) => {
          this.userService.saveUser(user);
          return true;
        })
      );
    } else {
      return true;
    }
    return true;
  }

  canActivateChild(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):


    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    // This method calls whenever angular change routes. we can permit and do other stuffs over here.
    // if (next.data.role == null || next.data.role == 'guest') {
    //   return true;
    // } else {
    //   if (!this.userService.hasTokens()) {
    //     this.router.navigateByUrl('/');
    //     return false;
    //   }
    // }
    return true;
  }
}
