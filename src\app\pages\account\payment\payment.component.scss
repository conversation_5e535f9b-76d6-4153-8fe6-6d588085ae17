.save-your-payment{
  padding: 40px 0 65px 0;
}
.save-your-payment p {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
}
.save-your-payment span {
  font-size: 18px;
  color: #202020;
  display: block;  
}
.save-your-payment button.btn{
  padding:10px 25px;
  margin-top: 65px;
}
.save-your-payment button.btn svg{
  margin-left: 20px;
}
.credit-card-box{
  max-width:fit-content;
  position: relative;
  margin-bottom: 20px;
}
.credit-card-box img.card-bg{
  width:365px;
}
.card-detail-box {
  position: absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;  
  padding:30px 25px 20px 25px;
}
.credit-card-header {
  margin-bottom: 80px;
}
.credit-card-header span {
  font-size: 18px;
  color: #fff;
  font-weight: 400;
}
.credit-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.credit-card-footer .card-holder-name p{
  font-size: 18px;
  color:#fff;
  margin-bottom: 0;
} 
.credit-card-footer .card-holder-name p svg{
  font-size: 4px;
  color:#fff;
  margin-right: 10px;
}
.credit-card-footer .expiry-date p {
  font-size: 18px;
  color: #fff;
  font-weight: 400;
  margin-bottom: 0;
}
.edit-delete{
  width:365px;
  margin-bottom: 30px;
}
.edit-delete ul {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
}
.edit-delete ul li {
  margin:0 15px;
}
.edit-delete ul li button {
  padding: 0;
  color: #fff;
  font-size: 15px;
  width: 35px;
  height: 35px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0px 0px 4px 3px #0000001A;
  border: 1px dashed #242323;
  background-color:#242323;
}
.edit-delete ul li button.delete-btn {
  border-color: #fc353a;
  background-color:#fc353a;
}
.modal-body {
  padding: 20px 30px 30px 30px;
}
.modal-body .login-title {
  margin-bottom: 30px;
}
.modal-body .form-group {
  margin-bottom: 20px;
}
.modal-body .form-group .form-control {
  padding:12px 20px;
  font-size: 18px;
  height: 50px;
}
.modal-body .form-group.postcode-group input.form-control {
  padding-right: 105px;
}
.modal-body .form-group.postcode-group div.btn {
  width: 75px;
}
.modal-body button.btn {
  width: 100%;
}

@media screen and (max-width:1600px) {
.credit-card-box img.card-bg {
  width: 275px;
}
.credit-card-header{
  margin-bottom: 60px;
}
.card-detail-box{
  padding: 20px 20px 20px;
}
.credit-card-header span {
  font-size: 13px;
}
.credit-card-footer .card-holder-name p {
  font-size: 13px;
}
.credit-card-footer .card-holder-name p svg {
  margin-right: 7px;
}
.credit-card-footer .expiry-date p{
  font-size: 13px;
}
.credit-card-box{
  margin-bottom: 15px;
}
.edit-delete{
  width: 275px;
}

}

@media screen and (max-width:1500px) {
.save-your-payment{
  padding-top:30px;
}
.save-your-payment p{
  font-size: 22px;
  margin-bottom: 25px;
}
.save-your-payment span{
  font-size:15px;
  font-weight:700;  
}
.modal-body{
    padding: 20px;
}  
.modal-body .login-title {
  margin-bottom: 20px;
}
.modal-body .form-group {
  margin-bottom: 15px;
}
.modal-body .form-group .form-control{
  padding:5px 15px;
  border-radius: 7px !important;
  height:36px;
}
.modal-body button.btn {
  padding: 5px 15px;
}

}

@media screen and (max-width:1199px) {
.edit-delete ul li button {
  font-size: 13px;
  width: 30px;
  height: 30px;
  line-height: 28px;
}

}

@media screen and (max-width:991px) {
.credit-card-box img.card-bg {
    width: 225px;
}
.credit-card-header{
  margin-bottom: 36px;
}
.card-detail-box{
  padding: 15px;
}
.edit-delete{
  width: 225px;
}
.edit-delete ul li {
  margin: 0 10px;
}
.edit-delete ul li button {
  font-size: 12px;
  width: 28px;
  height: 28px;
  line-height: 23px;
}

}

@media screen and (max-width:767px) {
.save-your-payment{
  padding-top: 20px;
  padding-bottom: 45px;
}
.save-your-payment p {
  margin-bottom: 20px;
}
.save-your-payment button.btn {
  padding: 9px 20px;
  margin-top: 45px;
}
.save-your-payment button.btn svg{
  margin-left: 15px;
}

}

@media screen and (max-width:575px) {  
  .save-your-payment{
    padding-top: 0px;
    padding-bottom: 35px;
  }
  .save-your-payment button.btn {
    margin-top: 35px;
  }
  .credit-card-box{
    margin: auto;
    margin-bottom: 15px;    
  }
  .edit-delete{
    margin: auto;
    margin-bottom: 30px;
  }  

}

@media screen and (max-width:480px) {
  .save-your-payment p {
    font-size: 20px;
  }

}
