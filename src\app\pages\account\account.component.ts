import { Component, HostListener, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { User } from '../../core/models/user';
import { UserService } from '../../core/services/user.service';
import { Referral } from '../../core/models/referral';
import { RewardService } from '../../core/services/reward.service';
import { ReferralService } from '../../core/services/referral.service';
import { SiteSetting } from '../../core/models/site-setting';
import { SiteSettingService } from '../../core/services/site-setting.service';

@Component({
  selector: 'app-account',
  host: { ngSkipHydration: 'true' },
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss']
})

export class AccountComponent implements OnInit {
  private subs = new Subscription();

  user: User;
  referral: Referral = new Referral();
  reward: any;
  siteSetting: SiteSetting = new SiteSetting();

  isLoading = false; error = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    public userService: UserService,
    private referralService: ReferralService,
    private rewardService: RewardService,
    private siteSettingService: SiteSettingService,
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() {
    this.fetchSiteSetting();
    this.user = JSON.parse(this.userService.getUser());
    if (!this.user) {
      this.router.navigateByUrl('/menu');
    }
    this.fetchReferral();
    this.fetchReferral();
  }

  fetchSiteSetting() {
    this.subs.add(this.siteSettingService.show_all()
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.siteSetting = res;
      }, err => this.error = err)
    );
  }

  fetchReward() {
    this.subs.add(this.rewardService.rewardMain()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.reward = res;
      }, err => this.error = err)
    );
  }

  fetchReferral() {
    this.isLoading = true;

    this.subs.add(this.referralService.show('1')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.referral = res;
      }, err => this.error = err)
    );
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); }

}
