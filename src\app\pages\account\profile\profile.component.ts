import { Cur<PERSON>cyPipe, DOCUMENT, formatDate } from '@angular/common';
import { Component, Inject, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { environment } from '../../../../environments/environment';
import { User } from '../../../core/models/user';
import { RestaurantService } from '../../../core/services/restaurant.service';
import { Restaurant } from '../../../core/models/restaurant';
import { NgForm } from '@angular/forms';
import { NotificationService } from '../../../core/services/notification.service';
import { Order } from '../../../core/models/order';
import { StripeCustomer } from '../../../core/models/stripe-customer';
import { Booking } from '../../../core/models/booking';
import { AddressBook } from '../../../core/models/address-book';
import { SiteSetting } from '../../../core/models/site-setting';
import { SiteSettingService } from '../../../core/services/site-setting.service';

@Component({
  selector: 'app-profile',
  host: { ngSkipHydration: 'true' },
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit, OnDestroy {
  @ViewChild('changepassword', { static: false }) changepassword: NgForm;
  @ViewChild('VerifyModal', { static: false }) VerifyModal: NgForm;

  subs = new Subscription();

  user: User = new User();
  restaurant: Restaurant = new Restaurant();
  modalOptions: NgbModalOptions;
  previousPage: any;

  dashboardDetails: any;
  siteSetting: SiteSetting = new SiteSetting();
  completeOrderList: Order[] = [];
  deliveredOrderList: Order[] = [];
  bookigList: Booking[] = [];
  cardList: StripeCustomer[] = [];
  addressList: AddressBook[] = [];

  emailOtp: string = '';
  verfiType: string = '';

  isLoading = false; error = null;
  isProfileLoading = false; errorMessage = null;
  isChangePasswordLoading = false; errorChangePassword = null;
  isDeleteUserLoading = false; errorDeleteUser = null;
  isModelOtpLoading = false; Modelotperror = null;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    public userService: UserService,
    private restaurantService: RestaurantService,
    private siteSettingService: SiteSettingService,
    private route: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    if (typeof localStorage !== 'undefined') {
      this.restaurant.id = localStorage.getItem(environment.googleFirebase);
    }
    this.options.customer_id = this.user?.id;
    if (!this.user?.id) {
      this.router.navigateByUrl('/');
    }
    // this.fetchRestaurant();
    this.fetchDashboard();

    if (this.route.snapshot.queryParamMap.get('code') && this.route.snapshot.queryParamMap.get('code') != null) {
      let code = atob(this.route.snapshot.queryParamMap.get('code'));
      this.subs.add(this.userService.show(code)
        .pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(res => {
          this.user.email_verify = true;
          this.userService.update(this.user)
            .pipe(finalize(() => (this.isLoading = false)))
            .subscribe(
              (res) => {
                this.notificationService.showSuccess("Thank you for verified your email address.", "Gogrubz")
                this.subs.add(this.userService.me()
                  .pipe(finalize(() => this.isLoading = false))
                  .subscribe(res => {
                    this.userService.saveUser(res);
                    this.user = res
                  }, err => this.errorMessage = err)
                );
              },
              (err) => {
                this.errorMessage = err;
              }
            );

        }, err => this.Modelotperror = err)
      );
    }
  }

  fetchDashboard() {
    this.isLoading = true;

    this.subs.add(this.userService.allDashboard()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.dashboardDetails = res;
        this.completeOrderList = res.complete_order_list;
        this.deliveredOrderList = res.delivered_order_list;
        this.bookigList = res.booking_list;
        this.cardList = res.card_list;
        this.addressList = res.address_list;
      }, err => this.error = err)
    );
  }

  onSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.updateUser(form)
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    if (event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.length == 0 && (event.key >= 0 && event.key <= 9)) {
      event.preventDefault();
    }
  }

  updateUser(form: NgForm) {
    this.isProfileLoading = true; this.errorMessage = null;

    if (this.userService.user.phone_number != this.user.phone_number) {
      this.user.phone_verify = false;

      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isProfileLoading = false)))
        .subscribe(
          (res) => { },
          (err) => { }
        );
    }

    this.userService.update(this.user)
      .pipe(finalize(() => (this.isProfileLoading = false)))
      .subscribe(
        (res) => {
          this.subs.add(this.userService.me()
            .pipe(finalize(() => this.isProfileLoading = false))
            .subscribe(res => {
              this.notificationService.showSuccess("Profile updated successfully !!", "Gogrubz")
              this.user = res
              this.userService.saveUser(res);
            }, err => this.errorMessage = err)
          );
        },
        (err) => {
          this.errorMessage = err;
        }
      );
  }

  onFileChanged(event) {
    const file = event.target.files[0]
  }

  onChangePasswordSubmit(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.changePassword(form)
  }

  changePassword(form: NgForm) {
    this.isChangePasswordLoading = true; this.errorChangePassword = null;

    this.userService.changepassword(this.user)
      .pipe(finalize(() => (this.isChangePasswordLoading = false)))
      .subscribe(
        (res) => {
          this.notificationService.showSuccess("Password change successfully !!", "Gogrubz")
          this.modalService.dismissAll();
          this.userService.logout();
          // this.changepassword.resetForm();
        },
        (err) => {
          this.errorChangePassword = err;
        }
      );
  }

  keyPress(event: any) {
    const pattern = /[0-9]/;
    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  delete(user: User) {
    this.isDeleteUserLoading = true; this.errorDeleteUser = null;

    this.subs.add(this.userService.delete(user.id)
      .pipe(finalize(() => this.isDeleteUserLoading = false))
      .subscribe(res => {
      }, err => { this.errorDeleteUser = err; })
    );
  }

  validateEmail() {
    let code = location.origin + "/account/profile?code=" + btoa(this.user.id);
    this.subs.add(
      this.userService.varifyEmail({ code: code, id: this.user.id }).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("Check your email & click the button to verify yout email address.", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  handleInput(event: any, index: number = 0) {
    const digit = event.target.value.replace(/\D/g, '');
    this.emailOtp = this.emailOtp.substring(0, index) + digit + this.emailOtp.substring(index + 1);

    if (this.verfiType == 'email') {
      this.user.email_otp = this.emailOtp;
    } else {
      this.user.otp = this.emailOtp;
    }

    if (index < 5 && digit.length === 1) {
      this.document.getElementById(`otp-input-${index + 1}`)?.focus();
    }
  }

  otpSend(event) {
    this.isModelOtpLoading = false; this.Modelotperror = null;

    this.verfiType = event;
    if (this.verfiType == 'email') {
      this.user.verify_type = 'email';
    } else {
      this.user.verify_type = 'phone';
    }

    this.modalService.open(this.VerifyModal, {});

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp(event) {
    this.isModelOtpLoading = false; this.Modelotperror = null;

    this.verfiType = event;
    if (this.verfiType == 'email') {
      this.user.verify_type = 'email';
    } else {
      this.user.verify_type = 'phone';
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("OTP sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }
    this.validateOtp(form)
  }

  validateOtp(form: NgForm) {
    this.isModelOtpLoading = true; this.Modelotperror = null

    if (this.verfiType == 'email') {
      this.user.verify_type = 'email';
    } else {
      this.user.verify_type = 'phone';
    }

    this.user.email_otp = this.user.otp
    this.subs.add(
      this.userService.varifyBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.subs.add(this.userService.me()
              .pipe(finalize(() => this.isModelOtpLoading = false))
              .subscribe(res => {
                this.user = res
                this.userService.saveUser(this.user);
                this.notificationService.showSuccess("OTP verify successfully !!", "Gogrubz")
              }, err => this.Modelotperror = err)
            );
            this.modalService.dismissAll();
          },
          (err) => {
            this.Modelotperror = err;
            this.notificationService.showError(err, "Gogrubz")
          }
        )
    )
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      // val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.modalService.dismissAll();
    this.subs.unsubscribe();
  }
}
