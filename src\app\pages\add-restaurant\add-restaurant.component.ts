import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { UserService } from '../../core/services/user.service';
import { BecomeRestaurant } from '../../core/models/become-restaurant';
import { BecomeRestaurantService } from '../../core/services/become-restaurant.service';
import { NotificationService } from '../../core/services/notification.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-add-restaurant',
  host: { ngSkipHydration: 'true' },
  templateUrl: './add-restaurant.component.html',
  styleUrls: ['./add-restaurant.component.scss']
})

export class AddRestaurantComponent implements OnInit {
  @ViewChild('otpModal') otpModal: ElementRef;

  private subs = new Subscription();

  becomeRestaurant: BecomeRestaurant = new BecomeRestaurant();
  restaurantForm: NgForm;

  isLoading = false; error = null;
  isOtpLoading = false; Otperror = null;
  isPostcodeLoading = false; Postcodeerror = null;

  options = {
    query: null,
    page: 1,
    per_page: 20,
  };

  constructor(
    private becomeRestaurantService: BecomeRestaurantService,
    public userService: UserService,
    private notificationService: NotificationService,
    public modalService: NgbModal,
    private route: ActivatedRoute,
    private router: Router,
  ) { }

  ngOnInit() {
  }

  onRestaurantAdd(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.createRestaurant(form);
  }

  createRestaurant(form: NgForm) {
    this.isLoading = true; this.error = null;

    this.subs.add(this.becomeRestaurantService.create(this.becomeRestaurant)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.becomeRestaurant = res;
        this.restaurantForm = form;
        this.modalService.open(this.otpModal, { backdropClass: 'customBackdrop', backdrop: 'static' });
        this.notificationService.showSuccess("Otp send successfully!", "Gogrubz")
      }, err => this.error = err)
    );
  }

  onSubmitOtp(form: NgForm) {
    if (!form.valid) {
      for (const i in form.controls) {
        form.controls[i].markAsDirty();
        form.controls[i].updateValueAndValidity();
      }
      return;
    }

    this.isOtpLoading = true; this.Otperror = null;
    this.subs.add(
      this.becomeRestaurantService.verify(this.becomeRestaurant).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.restaurantForm.reset();
            this.modalService.dismissAll();
            this.notificationService.showSuccess("Thank you for add restaurant.", "Gogrubz")
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.isOtpLoading = false; this.Otperror = false;

    this.subs.add(
      this.becomeRestaurantService.resend(this.becomeRestaurant).
        pipe(finalize(() => this.isOtpLoading = false))
        .subscribe(
          (res) => {
            this.notificationService.showSuccess("otp sent successfully !!", "Gogrubz")
          },
          (err) => {
            this.Otperror = err;
          }
        )
    )
  }

  findzipcode(postcode: string) {
    this.isPostcodeLoading = true; this.Postcodeerror = null;

    if (postcode == null || postcode == undefined) {
      this.Postcodeerror = 'Please enter valid postcode and press lookup button';
      this.isPostcodeLoading = false;
    } else {
      this.subs.add(this.userService.postcode(postcode)
        .pipe(finalize(() => this.isPostcodeLoading = false))
        .subscribe(res => {
          var address = '';
          if (res.street) {
            address += res.street;
          }
          if (res.post_town) {
            address += ',' + res.post_town;
          }
          if (res.post_code) {
            address += ',' + res.post_code;
          }
          this.becomeRestaurant.restaurant_address = address;
        }, err => this.Postcodeerror = err)
      );
    }
  }

  validateMobile(event) {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onSpaceKeyDown(event: KeyboardEvent): void {
    if (event.code === 'Space') {
      event.preventDefault();
    }
  }

  onSpaceKeyFirstDown(event: any) {
    if (event.keyCode == 189 || event.keyCode == 9 || event.keyCode == 8 || event.keyCode == 48 || (event.keyCode >= 97 && event.keyCode <= 122) || (event.keyCode >= 65 && event.keyCode <= 90)) {
    } else {
      event.preventDefault();
    }
  }

  applyFilters() { this.router.navigate([], { queryParams: this.options }); }

  ngOnDestroy() { this.subs.unsubscribe(); }
}
