export class Review {
  id: string;
  order_id: string;
  customer_id: string;
  customer_name: string;
  restaurant_id: string;

  rating: number;
  message: string;
  responce: string;
  delete_status: string;
  order_type: string;

  status: boolean = true;

  created: string;
  updated: string;

  static toFormData(review: Review) {
    const formData = new FormData();

    if (review.id) formData.append('id', review.id);
    if (review.order_id) formData.append('order_id', review.order_id);
    if (review.customer_id) formData.append('customer_id', review.customer_id);
    if (review.restaurant_id) formData.append('restaurant_id', review.restaurant_id);
    if (review.rating) formData.append('rating', review.rating.toString());
    if (review.message) formData.append('message', review.message);
    if (review.responce) formData.append('responce', review.responce);
    formData.append('order_type', review.order_type == 'pickup' ? '1' : '0');

    return formData;
  }
}
