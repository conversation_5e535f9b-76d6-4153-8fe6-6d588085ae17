import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CheckoutComponent } from './checkout.component';
import { SharedModule } from '../../shared/shared.module';
// import { LottieModule } from 'ngx-lottie';

const routes: Routes = [
  { path: '', component: CheckoutComponent },
];

// export function playerFactory() {
//   return import(/* webpackChunkName: 'lottie-web' */ 'lottie-web');
// }
@NgModule({
  imports: [
    RouterModule.forChild(routes),
    NgbModule,
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    SharedModule,
    // LottieModule.forRoot({ player: playerFactory })
  ],
  declarations: [CheckoutComponent],
  exports: [CheckoutComponent]
})
export class CheckoutModule { }
